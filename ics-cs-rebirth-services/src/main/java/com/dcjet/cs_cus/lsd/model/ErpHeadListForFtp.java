package com.dcjet.cs_cus.lsd.model;

import com.dcjet.cs.base.model.BasicModel;
import com.xdo.validation.annotation.XdoSize;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Setter
@Getter
@Table(name = "t_gw_lsd_ftp_erp_head_list_temp")
public class ErpHeadListForFtp extends BasicModel {

    /**
     * 出货单号
     */
    @NotEmpty(message = "出货单号不可为空")
    @XdoSize(max = 30, message = "{出货单号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "ems_list_no")
    private String emsListNo;
    /**
     * 客户订单号
     */
    @XdoSize(max = 50, message = "{客户订单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "customer_po_no")
    private String customerPoNo;
    /**
     * 行号
     */
    @NotNull(message = "行号不可为空")
    @Digits(integer = 30, fraction = 0, message = "{行号必须为数字,整数位最大30位,小数最大0位!}")
    @Column(name = "line_no")
    private BigDecimal lineNo;
    /**
     * 目的地
     */
    @NotEmpty(message = "目的地不可为空")
    @XdoSize(max = 3, message = "{目的地长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "destination_country")
    private String destinationCountry;
    /**
     * 客户物料编号
     */
    @XdoSize(max = 50, message = "{客户物料编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "customer_g_no")
    private String customerGNo;
    /**
     * 罗斯蒂物料编号
     */
    @NotEmpty(message = "罗斯蒂物料编号不可为空")
    @XdoSize(max = 50, message = "{罗斯蒂物料编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "fac_g_no")
    private String facGNo;
    /**
     * 币种
     */
    @XdoSize(max = 3, message = "{币种长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "curr")
    private String curr;
    /**
     * 运输方式
     */
    @NotEmpty(message = "运输方式不可为空")
    @XdoSize(max = 10, message = "{运输方式长度不能超过7位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 发票号
     */
    @XdoSize(max = 30, message = "{发票号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 数量
     */
    @NotNull(message = "数量不可为空")
    @Digits(integer = 10, fraction = 5, message = "{数量必须为数字,整数位最大10位,小数最大5位!}")
    @Column(name = "qty")
    private BigDecimal qty;
    /**
     * 栈板数
     */
    @Digits(integer = 9, fraction = 5, message = "{栈板数必须为数字,整数位最大9位,小数最大5位!}")
    @Column(name = "pack_num")
    private BigDecimal packNum;
    /**
     * 总毛重
     */
    @Digits(integer = 13, fraction = 5, message = "{总毛重必须为数字,整数位最大13位,小数最大5位!}")
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * 总净重
     */
    @Digits(integer = 13, fraction = 5, message = "{总净重必须为数字,整数位最大13位,小数最大5位!}")
    @Column(name = "head_net_wt")
    private BigDecimal headNetWt;
    /**
     * 行净重
     */
    @Digits(integer = 13, fraction = 5, message = "{行净重必须为数字,整数位最大13位,小数最大5位!}")
    @Column(name = "net_wt")
    private BigDecimal netWt;
    /**
     * 贸易国别
     */
    @XdoSize(max = 6, message = "{贸易国别长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "trade_nation")
    private String tradeNation;
    /**
     * 成交方式
     */
    @XdoSize(max = 6, message = "{成交方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "trans_mode")
    private String transMode;
    /**
     * 境外收发货人名称
     */
    @XdoSize(max = 100, message = "{境外收发货人名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "overseas_shipper_name")
    private String overseasShipperName;
    /**
     * 总价
     */
    @Digits(integer = 13, fraction = 4, message = "{总价必须为数字,整数位最大13位,小数最大4位!}")
    @Column(name = "dec_total")
    private BigDecimal decTotal;
    /**
     * 境内货源地
     */
    @XdoSize(max = 5, message = "{境内货源地长度不能超过5位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "district_code")
    private String districtCode;
    /**
     * 原产国
     */
    @XdoSize(max = 50, message = "{原产国长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @Column(name = "origin_country")
    private String originCountry;

    @Column(name = "box_qty")
    private BigDecimal boxQty;

    @Column(name = "temp_flag")
    private String tempFlag;
    @Column(name = "temp_remark")
    private String tempRemark;
    @Column(name = "serial_no")
    private int serialNo;

    @Column(name = "destination_country_convert")
    private String destinationCountryConvert;
    @Column(name = "origin_country_convert")
    private String originCountryConvert;
    @Column(name = "curr_convert")
    private String currConvert;
    @Column(name = "trade_nation_convert")
    private String tradeNationConvert;
    @Column(name = "trans_mode_convert")
    private String transModeConvert;
    @Column(name = "traf_mode_convert")
    private String trafModeConvert;
    @Column(name = "head_id")
    private String headId;
    @Column(name = "exg_version")
    private String exgVersion;
    @Column(name = "batch_no")
    private String batchNo;
}
