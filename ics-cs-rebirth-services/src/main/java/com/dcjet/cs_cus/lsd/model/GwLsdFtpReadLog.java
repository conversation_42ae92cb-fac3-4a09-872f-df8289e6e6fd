package com.dcjet.cs_cus.lsd.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 9/4/2024
 */
@Setter
@Getter
@Table(name = "t_gw_lsd_ftp_read_log")
public class GwLsdFtpReadLog extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 文件名称
     */
	@Column(name = "file_name")
	private  String fileName;
	/**
     * 识别状态1 识别失败 2 识别成功
     */
	@Column(name = "status")
	private  String status;
	/**
     * 文件大小
     */
	@Column(name = "size")
	private  BigDecimal size;
	/**
	 * 程序错误原因
	 */
	@Column(name = "err_message")
	private String errMessage;
}
