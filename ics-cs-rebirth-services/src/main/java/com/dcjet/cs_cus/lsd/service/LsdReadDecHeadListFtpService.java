package com.dcjet.cs_cus.lsd.service;

import com.alibaba.excel.util.DateUtils;
import com.dcjet.cs.bi.dao.BiCustomerParamsMapper;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.ftpConfig.dao.GwstdFtpConfigMapper;
import com.dcjet.cs.ftpConfig.model.GwstdFtpConfig;
import com.dcjet.cs.util.*;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs_cus.lsd.dao.GwLsdErpHeadListMapper;
import com.dcjet.cs_cus.lsd.dao.GwLsdFtpReadLogMapper;
import com.dcjet.cs_cus.lsd.model.ErpHeadListForFtp;
import com.dcjet.cs_cus.lsd.model.GwLsdFtpReadLog;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.KeyValuePair;
import com.xdo.pcode.service.PCodeHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LsdReadDecHeadListFtpService {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    private static final String FTP_TYPE = "ERP_HEAD_LIST";
    private static final String FTP_BACK_PATH = "/backup";
    private static final String FTP_ERR_PATH = "/err";

    private static Map<String, String> countryMap = new HashMap<>();
    private static Map<String, String> currMap = new HashMap<>();

    private static List<KeyValuePair<String, String>> columns = new ArrayList<>();

    static {
        columns.add(new KeyValuePair<>("emsListNo", "单据内部编号"));
        columns.add(new KeyValuePair<>("destinationCountry", "目的地"));
        columns.add(new KeyValuePair<>("lineNo", "行号"));
        columns.add(new KeyValuePair<>("facGNo", "罗斯蒂物料编号"));
        columns.add(new KeyValuePair<>("curr", "币种"));
        columns.add(new KeyValuePair<>("trafMode", "运输方式"));
        columns.add(new KeyValuePair<>("transMode", "交运条款"));
        columns.add(new KeyValuePair<>("invoiceNo", "发票号"));
        columns.add(new KeyValuePair<>("qty", "发货数量"));
        columns.add(new KeyValuePair<>("boxQty", "总箱数"));
        columns.add(new KeyValuePair<>("packNum", "总栈板数"));
        columns.add(new KeyValuePair<>("overseasShipperName", "境外收货人名称"));
        columns.add(new KeyValuePair<>("tradeNation", "贸易国别"));
        columns.add(new KeyValuePair<>("netWt", "行净重"));
        columns.add(new KeyValuePair<>("headNetWt", "总净重"));
        columns.add(new KeyValuePair<>("grossWt", "总毛重"));
        columns.add(new KeyValuePair<>("decTotal", "总价"));
        columns.add(new KeyValuePair<>("districtCode", "境内货源地"));
        columns.add(new KeyValuePair<>("originCountry", "原产国"));
    }

    @Value("${dc.export.temp:}")
    private String tempPath;

    @Resource
    private GwstdFtpConfigMapper ftpConfigMapper;
    @Resource
    private ValidatorUtil validatorUtil;
    @Resource
    private ExportService exportService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private BiCustomerParamsMapper paramsMapper;
    @Resource
    private GwLsdFtpReadLogMapper logMapper;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private GwLsdErpHeadListMapper lsdErpHeadListMapper;


    public void readFtp() {
        List<GwstdFtpConfig> configs = ftpConfigMapper.selectListByType(FTP_TYPE);
        if (CollectionUtils.isEmpty(configs)) {
            LoggerUtil.logInfo(logger, "=================当前没有配置从FTP读取erp出口整单数据的企业=================");
            return;
        }
        for (GwstdFtpConfig config : configs) {
            FTPClient ftpClient = null;
            Timer timer = new Timer();
            try {
                String coding = null;
                if (config.getCoding() != null) {
                    coding = config.getCoding();
                } else {
                    coding = CommonVariable.GBK;
                }
                ftpClient = FtpUtil.connectFtpServer(config.getIpAddr(), config.getPort(), config.getFtpAccount(), config.getFtpPwd(), coding);
                keepFtpConnAlive(timer, ftpClient);
                String ftpDirPath = config.getServerFilePath();
                //参数初始化
                BiCustomerParams param = new BiCustomerParams();
                param.setTradeCode(config.getTradeCode());
                param.setParamsType("COUNTRY");
                List<BiCustomerParams> countryParams = paramsMapper.getListByParams(param);
                if (CollectionUtils.isNotEmpty(countryParams)) {
                    countryMap = countryParams.stream().collect(Collectors.groupingBy(BiCustomerParams::getParamsCode, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0).getCustomParamCode())));
                }
                param.setParamsType("CURR");
                List<BiCustomerParams> currParams = paramsMapper.getListByParams(param);
                if (CollectionUtils.isNotEmpty(currParams)) {
                    currMap = currParams.stream().collect(Collectors.groupingBy(BiCustomerParams::getParamsCode, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0).getCustomParamCode())));
                }
                List<FTPFile> ftpFiles = Arrays.asList(ftpClient.listFiles("/file"));

                if (CollectionUtils.isEmpty(ftpFiles)) {
                    LoggerUtil.logInfo(logger, "企业：" + config.getTradeCode() + "FTP目录下没有需要处理的文件");
                    return;
                }
                for (FTPFile ftpFile : ftpFiles) {
                    String ftpFileName = ftpFile.getName();
                    GwLsdFtpReadLog log = new GwLsdFtpReadLog();
                    log.setSid(UUID.randomUUID().toString());
                    log.setTradeCode(config.getTradeCode());
                    log.setInsertUser("system");
                    log.setInsertTime(new Date());
                    log.setFileName(ftpFile.getName());
                    String tempFileName = DateUtils.format(new Date(), "yyyyMMddHHmmss") + "_" + ftpFileName;
                    String tempFilePath = tempPath + tempFileName;
                    logger.info("临时文件名" + tempFilePath);
                    FileInputStream fis = null;
                    try {
                        //下载ftp文件到本地
                        FtpUtil.downloadFile(ftpClient, tempFilePath, ftpDirPath + "/" + ftpFileName);
                        logger.info("临时文件" + ftpFileName + "下载到本地成功");
                        log.setSize(BigDecimal.valueOf(ftpFile.getSize()));
                        fis = new FileInputStream(tempFilePath);
                        logger.info("临时文件" + ftpFileName + "获取文件流成功");
                        //处理文件
                        boolean isSuccess = handlerFile(tempFilePath, tempFileName, config, ftpClient);
                        logger.info("文件" + ftpFileName + "处理完成");
                        //处理完成之后,将当前文件移动至备份文件目录
                        ftpClient.deleteFile(ftpDirPath + "/" + ftpFileName);
                        logger.info("ftp文件" + ftpFileName + "删除成功");
                        ftpClient.storeFile(FTP_BACK_PATH + "/" + tempFileName, fis);
                        logger.info("ftp文件" + ftpFileName + "已移动到备份目录");
                        if (isSuccess) {
                            log.setStatus("1");
                        } else {
                            log.setStatus("2");
                        }
                    } catch (Exception e) {
                        log.setStatus("3");
                        log.setErrMessage(e.toString().length() > 4000 ? e.toString().substring(0, 4000) : e.toString());
                        logger.error("处理罗斯蒂FTP文件失败，错误信息：" + e);
                    } finally {
                        logMapper.insert(log);
                        if (fis != null) fis.close();
                        FileUtil.deleteFile(tempFilePath);
                    }
                }
            } catch (Exception e) {
                log.error("读取文件失败:{}", e);
            } finally {
                // 关闭sftp连接
                FtpUtil.closeFTPConnect(ftpClient);
                timer.cancel();//销毁保活定时器
                log.info("关闭SFTP连接");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean handlerFile(String filePath, String fileName, GwstdFtpConfig config, FTPClient ftpClient) throws Exception {
        boolean flag = true;
        File file = new File(filePath);
        //读取文件
        List<ErpHeadListForFtp> data = ExcelImportUtil.read(new FileInputStream(file), ErpHeadListForFtp.class, 0, 3, columns);

        logger.info("ftp文件" + fileName + "读取成功");
        if (CollectionUtils.isEmpty(data)) {
            throw new ErrorException(400, "数据为空");
        }

        List<String> emsListNos = data.stream().map(ErpHeadListForFtp::getEmsListNo).distinct().collect(Collectors.toList());
        Map<String, String> headIdInit = new HashMap<>();
        for (String emsListNo : emsListNos) {
            headIdInit.put(emsListNo, UUID.randomUUID().toString());
        }

        int serialNo = 1;
        String batchNo = UUID.randomUUID().toString();
        for (ErpHeadListForFtp datum : data) {
            datum.setSid(UUID.randomUUID().toString());
            datum.setSerialNo(serialNo++);
            datum.setInsertTime(new Date());
            datum.setInsertUser("system");
            datum.setTradeCode(config.getTradeCode());
            datum.setTempFlag("0");
            datum.setTempRemark("");
            datum.setOriginCountryConvert("142");
            datum.setHeadId(headIdInit.get(datum.getEmsListNo()));
            datum.setBatchNo(batchNo);
            String packNum = datum.getPackNum() == null ? "" : datum.getPackNum().toString();
            if (datum.getPackNum() != null && packNum.contains(".")) {
                String suffix = packNum.substring(packNum.indexOf("."));
                if (new BigDecimal(suffix).compareTo(BigDecimal.ZERO) != 0) {
                    datum.setPackNum(null);
                }
            }
            //通用栏位校验，长度+必填
            String errorMsg = validatorUtil.validation(datum);
            if (StringUtils.isNotEmpty(errorMsg)) {
                datum.setTempRemark(errorMsg);
                datum.setTempFlag("1");
            }

            //校验目的地
            if (StringUtils.isNotEmpty(datum.getDestinationCountry())) {
                if (MapUtils.isEmpty(countryMap) || !countryMap.containsKey(datum.getDestinationCountry())) {
                    String value = pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, datum.getDestinationCountry());
                    if (StringUtils.isEmpty(value)) {
                        datum.setTempRemark(datum.getTempRemark() + "目的地不存在|");
                        datum.setTempFlag("1");
                    } else {
                        datum.setDestinationCountryConvert(datum.getDestinationCountry());
                    }
                } else {
                    datum.setDestinationCountryConvert(countryMap.get(datum.getDestinationCountry()));
                }
            }
            //校验币制
            if (StringUtils.isNotEmpty(datum.getCurr())) {
                if (MapUtils.isEmpty(currMap) || !currMap.containsKey(datum.getCurr())) {
                    String value = pCodeHolder.getValue(PCodeType.CURR_OUTDATED, datum.getCurr());
                    if (StringUtils.isEmpty(value)) {
                        datum.setTempRemark(datum.getTempRemark() + "币制不存在|");
                        datum.setTempFlag("1");
                    } else {
                        datum.setCurrConvert(datum.getCurr());
                    }
                } else {
                    datum.setCurrConvert(currMap.get(datum.getCurr()));
                }
            }
            //校验贸易国别
            if (StringUtils.isNotEmpty(datum.getTradeNation())) {
                if (MapUtils.isEmpty(countryMap) || !countryMap.containsKey(datum.getTradeNation())) {
                    String value = pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, datum.getTradeNation());
                    if (StringUtils.isEmpty(value)) {
                        datum.setTempRemark(datum.getTempRemark() + "贸易国别不存在|");
                        datum.setTempFlag("1");
                    } else {
                        datum.setTradeNationConvert(datum.getTradeNation());
                    }
                } else {
                    datum.setTradeNationConvert(countryMap.get(datum.getTradeNation()));
                }
            }
            //校验成交方式
            if (StringUtils.isNotEmpty(datum.getTransMode())) {
                if (Arrays.asList("DAP", "DDU").contains(datum.getTransMode())) {
                    datum.setTransMode("1");
                } else if ("EXW".equals(datum.getTransMode())) {
                    datum.setTransMode("7");
                } else if (Arrays.asList("FCA", "FOB").contains(datum.getTransMode())) {
                    datum.setTransMode("3");
                }
            }
            //校验境内货源地
            if (StringUtils.isNotEmpty(datum.getDistrictCode())) {
                String value = pCodeHolder.getValue(PCodeType.AREA, datum.getDistrictCode());
                if (StringUtils.isEmpty(value)) {
                    datum.setTempRemark(datum.getTempRemark() + "境内货源地不存在|");
                    datum.setTempFlag("1");
                }
            }
            if (StringUtils.isNotBlank(datum.getInvoiceNo()) && datum.getInvoiceNo().endsWith("B") && datum.getInvoiceNo().length() == 10) {
                datum.setExgVersion(datum.getInvoiceNo().substring(3, 9));
            }
        }

        int success = bulkSqlOpt.batchInsert(data, GwLsdErpHeadListMapper.class);
        if (success == -2) {
            throw new ErrorException(400, "数据入临时表出错");
        }
        lsdErpHeadListMapper.checkData(batchNo);

        if (lsdErpHeadListMapper.selectErrorCount(batchNo) > 0) {
            logger.info("ftp文件" + fileName + "存在错误数据");
            List<ErpHeadListForFtp> errData = lsdErpHeadListMapper.getErrorData(batchNo);
            String templateName = "lsd_erp_head_list_err_template.xlsx";
            String exportFileName = exportService.export(errData, "err_" + fileName, templateName);
            logger.info("ftp文件" + fileName + "错误数据生成文件成功");

            //将错误文件上传到FTP错误文件目录
            FileInputStream fis = new FileInputStream(exportFileName);
            ftpClient.storeFile(FTP_ERR_PATH + "/" + "err_" + fileName, fis);
            logger.info("ftp文件" + fileName + "错误数据上传至ftp成功");
            //删除本地临时文件
            fis.close();
            FileUtil.deleteFile(exportFileName);
            logger.info("ftp文件" + fileName + "本地临时文件删除成功");

            flag = false;
        }

        lsdErpHeadListMapper.insertSelect(config.getTradeCode(), batchNo);
        return flag;
    }


    public void keepFtpConnAlive(Timer timer, FTPClient ftpClient) {
        timer.scheduleAtFixedRate(new TimerTask() {
            public void run() {
                try {
                    ftpClient.sendCommand("pwd");
                    log.info("=============ftp保活成功=============");
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }, 100, 3000);
    }
}