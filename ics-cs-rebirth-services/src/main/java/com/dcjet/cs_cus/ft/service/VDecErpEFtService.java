package com.dcjet.cs_cus.ft.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.report.VDecErpEHeadListDto;
import com.dcjet.cs.dto.report.VDecErpEHeadListHeadDto;
import com.dcjet.cs.dto.report.VDecErpEHeadListHeadParam;
import com.dcjet.cs.dto.report.VDecErpEHeadListParam;
import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListFtParam;
import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListHeadFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListHeadFtParam;
import com.dcjet.cs.report.dao.VDecErpEHeadListMapper;
import com.dcjet.cs.report.mapper.VDecErpEDtoMapper;
import com.dcjet.cs.report.model.VDecErpEHeadList;
import com.dcjet.cs.util.WeekUnit;
import com.dcjet.cs_cus.ft.dao.VDecErpEHeadListFtMapper;
import com.dcjet.cs_cus.ft.mapper.VDecErpEFtDtoMapper;
import com.dcjet.cs_cus.ft.model.VDecErpEHeadFt;
import com.dcjet.cs_cus.ft.model.VDecErpEHeadListFt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * @author: 沈振宇
 * @date: 2019-03-11
 */
@Service
public class VDecErpEFtService extends BaseService<VDecErpEHeadListFt> {

    @Resource
    private VDecErpEHeadListFtMapper vDecErpEHeadListFtMapper;

    @Override
    public Mapper<VDecErpEHeadListFt> getMapper() {
        return vDecErpEHeadListFtMapper;
    }

    @Resource
    private VDecErpEFtDtoMapper vDecErpEFtDtoMapper;
    @Resource
    private PCodeHolder pCodeHolder;

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param vDecErpEHeadListFtParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<VDecErpEHeadListFtDto>> getListPaged(VDecErpEHeadListFtParam vDecErpEHeadListFtParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecErpEHeadListFt vDecErpEHeadListFt = vDecErpEFtDtoMapper.toPo(vDecErpEHeadListFtParam);

        vDecErpEHeadListFt.setTradeCode(userInfo.getCompany());
        Page<VDecErpEHeadListFt> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vDecErpEHeadListFtMapper.getList(vDecErpEHeadListFt));
        List<VDecErpEHeadListFtDto> matImgexgDtos = page.getResult().stream().map(head -> {
            VDecErpEHeadListFtDto dto = vDecErpEFtDtoMapper.toDto(head);
            dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
            String allWrap=dto.getWrapTypeName();
            if(StringUtils.isNotBlank(allWrap))
            {
                String newWarpList="";
                String[] warpList=allWrap.split(",");
                for (String warp :warpList) {
                    String warpName =  pCodeHolder.getValue(PCodeType.WRAP,warp);
                    if (StringUtils.isNotBlank(warpName)) {
                        newWarpList=newWarpList+warpName+"/";
                    }
                }
                if(newWarpList.length()>0) {
                    dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                }
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecErpEHeadListFtDto>> paged = ResultObject.createInstance(matImgexgDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param vDecErpEHeadListParam
     * @param userInfo
     * @return
     */
    public List<VDecErpEHeadListFtDto> selectAll(VDecErpEHeadListFtParam vDecErpEHeadListFtParam, PageParam pageParam,UserInfoToken userInfo) {
        VDecErpEHeadListFt vDecErpEHeadListFt = vDecErpEFtDtoMapper.toPo(vDecErpEHeadListFtParam);
        vDecErpEHeadListFt.setTradeCode(userInfo.getCompany());
        List<VDecErpEHeadListFtDto> vDecErpEHeadListFtDtos = new ArrayList<>();
        List<VDecErpEHeadListFt> vDecEEntries = vDecErpEHeadListFtMapper.getList(vDecErpEHeadListFt);
        if (CollectionUtils.isNotEmpty(vDecEEntries)) {
            vDecErpEHeadListFtDtos = vDecEEntries.stream().map(head -> {
                VDecErpEHeadListFtDto dto = vDecErpEFtDtoMapper.toDto(head);
                dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
                String allWrap=dto.getWrapTypeName();
                if(StringUtils.isNotBlank(allWrap))
                {
                    String newWarpList="";
                    String[] warpList=allWrap.split(",");
                    for (String warp :warpList) {
                        String warpName =  pCodeHolder.getValue(PCodeType.WRAP,warp);
                        if (StringUtils.isNotBlank(warpName)) {
                            newWarpList=newWarpList+warpName+"/";
                        }
                    }
                    if(newWarpList.length()>0) {
                        dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                    }
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecErpEHeadListFtDtos;
    }

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param vDecErpEHeadListHeadFtParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<VDecErpEHeadListHeadFtDto>> getListHeadPaged(VDecErpEHeadListHeadFtParam vDecErpEHeadListHeadFtParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecErpEHeadFt vDecErpEHeadFt = vDecErpEFtDtoMapper.toHeadPo(vDecErpEHeadListHeadFtParam);
        vDecErpEHeadFt.setTradeCode(userInfo.getCompany());
        Page<VDecErpEHeadFt> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vDecErpEHeadListFtMapper.getListHead(vDecErpEHeadFt));
        List<VDecErpEHeadListHeadFtDto> vDecErpEHeadListHeadFtDtos = page.getResult().stream().map(head -> {
            VDecErpEHeadListHeadFtDto dto = vDecErpEFtDtoMapper.toHeadDto(head);
            dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
            String allWrap=dto.getWrapTypeName();
            if(StringUtils.isNotBlank(allWrap))
            {
                String newWarpList="";
                String[] warpList=allWrap.split(",");
                for (String warp :warpList) {
                    String warpName =  pCodeHolder.getValue(PCodeType.WRAP,warp);
                    if (StringUtils.isNotBlank(warpName)) {
                        newWarpList=newWarpList+warpName+"/";
                    }
                }
                if(newWarpList.length()>0) {
                    dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                }
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecErpEHeadListHeadFtDto>> paged = ResultObject.createInstance(vDecErpEHeadListHeadFtDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VDecErpEHeadListHeadFtDto> selectHeadAll(VDecErpEHeadListHeadFtParam exportParam, UserInfoToken userInfo) {
        VDecErpEHeadFt vDecErpEHeadFt = vDecErpEFtDtoMapper.toHeadPo(exportParam);
        vDecErpEHeadFt.setTradeCode(userInfo.getCompany());
        List<VDecErpEHeadListHeadFtDto> vDecErpEHeadListHeadFtDtos = new ArrayList<>();
        List<VDecErpEHeadFt> vDecErpEHeadFts = vDecErpEHeadListFtMapper.getListHead(vDecErpEHeadFt);
        if (CollectionUtils.isNotEmpty(vDecErpEHeadFts)) {
            vDecErpEHeadListHeadFtDtos = vDecErpEHeadFts.stream().map(head -> {
                VDecErpEHeadListHeadFtDto dto = vDecErpEFtDtoMapper.toHeadDto(head);
                dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
                String allWrap=dto.getWrapTypeName();
                if(StringUtils.isNotBlank(allWrap))
                {
                    String newWarpList="";
                    String[] warpList=allWrap.split(",");
                    for (String warp :warpList) {
                        String warpName =  pCodeHolder.getValue(PCodeType.WRAP,warp);
                        if (StringUtils.isNotBlank(warpName)) {
                            newWarpList=newWarpList+warpName+"/";
                        }
                    }
                    if(newWarpList.length()>0) {
                        dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                    }
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecErpEHeadListHeadFtDtos;
    }


    /**
     * 汇总金额
     * @param vDecErpEHeadListFtParam
     * @param userInfo
     * @return
     */
    public String sumDecTotal(VDecErpEHeadListFtParam vDecErpEHeadListFtParam, UserInfoToken userInfo) {
        String sumTotal = "";
        VDecErpEHeadListFt vDecErpEHeadListFt = vDecErpEFtDtoMapper.toPo(vDecErpEHeadListFtParam);
        vDecErpEHeadListFt.setTradeCode(userInfo.getCompany());
        String total =  vDecErpEHeadListFtMapper.getSumDecTotal(vDecErpEHeadListFt);
        sumTotal = xdoi18n.XdoI18nUtil.t("汇总金额:") + new BigDecimal(total).setScale(2, BigDecimal.ROUND_HALF_UP);

        return sumTotal;
    }

    /**
     * 报表中心-进口提单表头数据更新
     */
    public void reportDecEHead(String tradeCode){
        vDecErpEHeadListFtMapper.deleteReportDecEHeadFt(tradeCode);
        vDecErpEHeadListFtMapper.insertReportDecEHeadFt(tradeCode);
    }

    /**
     * 报表中心-进口提单表头表体数据更新
     */
    public void reportDecEHeadList(String tradeCode){
        vDecErpEHeadListFtMapper.deleteReportDecEHeadListFt(tradeCode);
        vDecErpEHeadListFtMapper.insertReportDecEHeadListFt(tradeCode);
    }
}
