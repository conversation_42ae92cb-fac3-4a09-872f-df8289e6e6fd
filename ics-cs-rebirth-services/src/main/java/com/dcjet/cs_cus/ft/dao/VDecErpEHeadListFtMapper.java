package com.dcjet.cs_cus.ft.dao;

import com.dcjet.cs_cus.ft.model.VDecErpEHeadFt;
import com.dcjet.cs_cus.ft.model.VDecErpEHeadListFt;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * DecErpEHeadN
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */

public interface VDecErpEHeadListFtMapper extends Mapper<VDecErpEHeadListFt> {
    /**
     * 查询获取数据
     *
     * @param vDecErpEFtHeadListFt
     * @return
     */
    List<VDecErpEHeadListFt> getList(VDecErpEHeadListFt vDecErpEFtHeadListFt);

    String getSumDecTotal(VDecErpEHeadListFt vDecErpEHeadListFt);

    /**
     * 查询获取数据
     *
     * @param vDecErpEHeadFt
     * @return
     */
    List<VDecErpEHeadFt> getListHead(VDecErpEHeadFt vDecErpEHeadFt);


    Integer selectDataCount(VDecErpEHeadFt vDecErpEHeadFt);

    Integer selectDataCountAll(VDecErpEHeadListFt vDecErpEHeadListFt);

    int deleteReportDecEHeadFt(String tradeCode);

    int insertReportDecEHeadFt(String tradeCode);

    int deleteReportDecEHeadListFt(String tradeCode);

    int insertReportDecEHeadListFt(String tradeCode);
}
