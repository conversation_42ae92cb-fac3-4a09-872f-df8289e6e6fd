package com.dcjet.cs_cus.ft.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-6-13
 */
@Setter
@Getter
@Table(name = "t_cgw_ft_report_dec_i_head")
public class VDecErpIHeadFt implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     *
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     *
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     *
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     *
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     *
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 提单内部编号
     */
    @Column(name = "erp_ems_list_no")
    private String erpEmsListNo;
    /**
     * 经营企业名称
     */
    @Column(name = "trade_name")
    private String tradeName;
    /**
     * 经营企业社会信用代码
     */
    @Column(name = "trade_credit_code")
    private String tradeCreditCode;
    /**
     * 申报单位编码
     */
    @Column(name = "declare_code")
    private String declareCode;
    /**
     * 申报单位名称
     */
    @Column(name = "declare_name")
    private String declareName;
    /**
     * 申报单位社会信用代码
     */
    @Column(name = "declare_credit_code")
    private String declareCreditCode;
    /**
     * 录入企业编号
     */
    @Column(name = "input_code")
    private String inputCode;
    /**
     * 录入单位名称
     */
    @Column(name = "input_name")
    private String inputName;
    /**
     * 录入企业社会信用代码
     */
    @Column(name = "input_credit_code")
    private String inputCreditCode;
    /**
     * 供应商编码
     */
    @Column(name = "supplier_code")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @Column(name = "supplier_name")
    private String supplierName;
    /**
     * 货代编码
     */
    @Column(name = "forward_code")
    private String forwardCode;
    /**
     * 货代名称
     */
    @Column(name = "forward_name")
    private String forwardName;
    /**
     * 进境关别代码
     */
    @Column(name = "i_e_port")
    @JsonProperty("iEPort")
    private String iEPort;
    /**
     * 申报地海关
     */
    @Column(name = "master_customs")
    private String masterCustoms;
    /**
     * 监管方式(贸易方式)
     */
    @Column(name = "trade_mode")
    private String tradeMode;
    /**
     * 运输方式
     */
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 料件成品标记
     */
    @Column(name = "g_mark")
    @JsonProperty("gMark")
    private String gMark;
    /**
     * 贸易国别
     */
    @Column(name = "trade_nation")
    private String tradeNation;
    /**
     * 归并类型 0-自动归并 1-人工归并
     */
    @Column(name = "merge_type")
    private String mergeType;
    /**
     * 备注
     */
    @Column(name = "note")
    private String note;
    /**
     * 录入日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "input_date")
    private Date inputDate;
    /**
     * 成交方式
     */
    @Column(name = "trans_mode")
    private String transMode;
    /**
     * 启运国
     */
    @Column(name = "trade_country")
    private String tradeCountry;
    /**
     * 启运港
     */
    @Column(name = "desp_port")
    private String despPort;
    /**
     * 价格说明
     */
    @Column(name = "promise_items")
    private String promiseItems;
    /**
     * 有效标志 0 有效，1 无效
     */
    @Column(name = "valid_mark")
    private String validMark;
    /**
     * 备案号
     */
    @Column(name = "ems_no")
    private String emsNo;
    /**
     * 主提运单
     */
    @Column(name = "mawb")
    private String mawb;
    /**
     * 分提运单
     */
    @Column(name = "hawb")
    private String hawb;
    /**
     * 合同协议号
     */
    @Column(name = "contr_no")
    private String contrNo;
    /**
     * 许可证号
     */
    @Column(name = "license_no")
    private String licenseNo;
    /**
     * 发票号码
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 件数
     */
    @Column(name = "pack_num")
    private Integer packNum;
    /**
     * 体积
     */
    @Column(name = "volume_total")
    private BigDecimal volumeTotal;
    /**
     * 总净重
     */
    @Column(name = "net_wt_total")
    private BigDecimal netWtTotal;
    /**
     * 经停港
     */
    @Column(name = "dest_port")
    private String destPort;
    /**
     * 包装种类
     */
    @Column(name = "wrap_type")
    private String wrapType;
    /**
     * 运输工具名称
     */
    @Column(name = "traf_name")
    private String trafName;
    /**
     * 航次号
     */
    @Column(name = "voyage_no")
    private String voyageNo;
    /**
     * 杂费币制
     */
    @Column(name = "other_curr")
    private String otherCurr;
    /**
     * 杂费类型
     */
    @Column(name = "other_mark")
    private String otherMark;
    /**
     * 杂费
     */
    @Column(name = "other_rate")
    private BigDecimal otherRate;
    /**
     * 运费币制
     */
    @Column(name = "fee_curr")
    private String feeCurr;
    /**
     * 运费类型
     */
    @Column(name = "fee_mark")
    private String feeMark;
    /**
     * 运费
     */
    @Column(name = "fee_rate")
    private BigDecimal feeRate;
    /**
     * 保费币制
     */
    @Column(name = "insur_curr")
    private String insurCurr;
    /**
     * 保费类型
     */
    @Column(name = "insur_mark")
    private String insurMark;
    /**
     * 保费
     */
    @Column(name = "insur_rate")
    private BigDecimal insurRate;
    /**
     * 总毛重
     */
    @Column(name = "gross_wt_total")
    private BigDecimal grossWtTotal;
    /**
     * 入口日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "i_e_date")
    @JsonProperty("iEDate")
    private Date iEDate;
    /**
     * 境内目的地
     */
    @Column(name = "district_code")
    private String districtCode;
    /**
     * 境内收货人编码
     */
    @Column(name = "receive_code")
    private String receiveCode;
    /**
     * 境内收货人名称
     */
    @Column(name = "receive_name")
    private String receiveName;
    /**
     * 航班日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "voyage_date")
    private Date voyageDate;
    /**
     * 出货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ship_date")
    private Date shipDate;
    /**
     * 境外发货人编码
     */
    @Column(name = "overseas_shipper")
    private String overseasShipper;
    /**
     * 境外发货人名称
     */
    @Column(name = "overseas_shipper_name")
    private String overseasShipperName;
    /**
     * 征免性质
     */
    @Column(name = "cut_mode")
    private String cutMode;
    /**
     * 仓库存放地点
     */
    @Column(name = "warehouse")
    private String warehouse;
    /**
     * 原产国
     */
    @Column(name = "head_destination_country")
    private String headDestinationCountry;
    /**
     * 入境口岸
     */
    @Column(name = "entry_port")
    private String entryPort;
    /**
     * 发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "declare_date")
    private Date declareDate;
    /**
     * 发送人
     */
    @Column(name = "send_user")
    private String sendUser;
    /**
     * 发送人名称
     */
    @Column(name = "send_user_name")
    private String sendUserName;
    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "appr_date")
    private Date apprDate;
    /**
     * 审核人
     */
    @Column(name = "appr_user")
    private String apprUser;
    /**
     * 审核状态
     */
    @Column(name = "appr_status")
    private String apprStatus;
    /**
     * 审核人名称
     */
    @Column(name = "appr_user_name")
    private String apprUserName;
    /**
     * 审核状态名称
     */
    @Column(name = "appr_status_name")
    private String apprStatusName;
    /**
     * 随附单据是否上传
     */
    @Column(name = "attach")
    private String attach;
    /**
     * 随附单据是否上传 0 无 1 有
     */
    @Column(name = "attach_name")
    private String attachName;
    /**
     * 保完税标识 0 保税 1 非保税
     */
    @Column(name = "bond_mark")
    private String bondMark;
    /**
     * 数据来源 0大提单 1 小提单
     */
    @Column(name = "data_source")
    private String dataSource;
    /**
     * 报关标志 1报关 2非报关
     */
    @Column(name = "dclcus_mark")
    private String dclcusMark;
    /**
     * 报关类型
     */
    @Column(name = "dclcus_type")
    private String dclcusType;
    /**
     * 报关单类型
     */
    @Column(name = "entry_type")
    private String entryType;
    /**
     * 申请表编号/申报表编号
     */
    @Column(name = "apply_no")
    private String applyNo;
    /**
     * 关联核注清单编号
     */
    @Column(name = "rel_list_no")
    private String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @Column(name = "rel_ems_no")
    private String relEmsNo;
    /**
     * 境内货源地(行政区域)
     */
    @Column(name = "district_post_code")
    private String districtPostCode;
    /**
     * shipfrom代码
     */
    @Column(name = "ship_from")
    private String shipFrom;
    /**
     * 申报单位编码(基础参数)
     */
    @Column(name = "declare_code_customs")
    private String declareCodeCustoms;
    /**
     * 申报单位名称(基础参数)
     */
    @Column(name = "declare_name_customs")
    private String declareNameCustoms;
    /**
     *
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     *
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 贸易条款
     */
    @Column(name = "trade_terms")
    private String tradeTerms;
    /**
     * 集装箱类型
     */
    @Column(name = "container_type")
    private String containerType;
    /**
     * 集装箱数量
     */
    @Column(name = "container_num")
    private Integer containerNum;
    /**
     * 币值
     */
    @Column(name = "head_curr")
    private String headCurr;
    /**
     * 计费重量
     */
    @Column(name = "c_weight")
    @JsonProperty("cWeight")
    private BigDecimal cWeight;
    /**
     * 总金额
     */
    @Column(name = "sum_dec_total")
    private BigDecimal sumDecTotal;
    /**
     * 清单内部编号
     */
    @Column(name = "bill_ems_list_no")
    private String billEmsListNo;
    /**
     * 核注清单编号
     */
    @Column(name = "list_no")
    private String listNo;
    /**
     * 清单申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "bill_declare_date")
    private Date billDeclareDate;
    /**
     * 报关单申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "entry_declare_date")
    private Date entryDeclareDate;
    /**
     * -1 未发送 0 发送失败  1发送成功 2发送中 J1 通过 J3 退单 JDL 删单
     */
    @Column(name = "bill_status")
    private String billStatus;
    /**
     * 报关单号
     */
    @Column(name = "entry_no")
    private String entryNo;
    /**
     * 报关单统一编号
     */
    @Column(name = "seq_no")
    private String seqNo;
    /**
     * 报关单状态
     */
    @Column(name = "entry_status")
    private String entryStatus;
    /**
     * 特殊关系确认
     */
    @Column(name = "confirm_special")
    private String confirmSpecial;
    /**
     * 价格影响确认
     */
    @Column(name = "confirm_price")
    private String confirmPrice;
    /**
     * 支持特许权使用费确认
     */
    @Column(name = "confirm_royalties")
    private String confirmRoyalties;
    /**
     * 自报自缴
     */
    @Column(name = "duty_self")
    private String dutySelf;
    /**
     * 破损标记0-正常 1-破损
     */
    @Column(name = "damage_mark")
    private String damageMark;
    /**
     * 转关单号
     */
    @Column(name = "trans_no")
    private String transNo;
    /**
     * 转入口岸
     */
    @Column(name = "trans_port")
    private String transPort;
    /**
     * 到货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "arrival_date")
    private Date arrivalDate;
    /**
     * 运费
     */
    @Column(name = "freight")
    private BigDecimal freight;
    /**
     * 运费币制
     */
    @Column(name = "freight_curr")
    private String freightCurr;
    /**
     * 运输费用
     */
    @Column(name = "customs_fee")
    private BigDecimal customsFee;
    /**
     * 海关查验结果
     */
    @Column(name = "customs_check_result")
    private String customsCheckResult;
    /**
     * 报关费用
     */
    @Column(name = "logistics_fee")
    private BigDecimal logisticsFee;
    /**
     * 总税款
     */
    @Column(name = "tax_total")
    private BigDecimal taxTotal;
    /**
     * 其他进口环节税
     */
    @Column(name = "other_tax")
    private BigDecimal otherTax;
    /**
     * 增值税
     */
    @Column(name = "tax_price")
    private BigDecimal taxPrice;
    /**
     * 关税
     */
    @Column(name = "duty_price")
    private BigDecimal dutyPrice;
    /**
     * 到港日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "arrival_port_date")
    private Date arrivalPortDate;
    /**
     * 放行日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "pass_date")
    private Date passDate;
    /**
     * 财务单据号
     */
    @Column(name = "finance_no")
    private String financeNo;
    /**
     * 附加税
     */
    @Column(name = "add_tax_price")
    private BigDecimal addTaxPrice;
    /**
     * 到厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "delivery_date")
    private Date deliveryDate;
    /**
     * 到货通知日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "notice_date")
    private Date noticeDate;
    /**
     * 消费税
     */
    @Column(name = "excise_tax")
    private BigDecimal exciseTax;
    /**
     * 放行备注
     */
    @Column(name = "note_pass")
    private String notePass;
    /**
     * 关税缓征利息
     */
    @Column(name = "duty_interest")
    private BigDecimal dutyInterest;
    /**
     * 增值税缓征利息
     */
    @Column(name = "tax_interest")
    private BigDecimal taxInterest;
    /**
     * 消费税缓息
     */
    @Column(name = "excise_interest")
    private BigDecimal exciseInterest;
    /**
     * shipfrom名称
     */
    @Column(name = "ship_from_name")
    private String shipFromName;
    /**
     * 表体数量汇总
     */
    @Column(name = "qty_all")
    private BigDecimal qtyAll;
    /**
     * 表体金额汇总
     */
    @Column(name = "total_all")
    private BigDecimal totalAll;
    /**
     * 表体品名
     */
    @Column(name = "g_name_all")
    @JsonProperty("gNameAll")
    private String gNameAll;
    /**
     * 制单周次
     */
    @Column(name = "insert_week")
    private String insertWeek;
    /**
     * 内部备注
     */
    @Column(name = "remark")
    private String remark;
    /**
     * 包装种类名称
     */
    @Column(name = "wrap_type_name")
    private String wrapTypeName;
    /**
     * 表头sid
     */
    @Column(name = "erp_head_id")
    private String erpHeadId;
    /**
     * 完税日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "duty_date")
    private Date dutyDate;
    /**
     * 商检查验日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "check_date")
    private Date checkDate;
    /**
     * 海关查验日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "customs_check_date")
    private Date customsCheckDate;
    /**
     * 公式价格确认
     */
    @Column(name = "confirm_formula_price")
    private String confirmFormulaPrice;
    /**
     * 暂定价格确认
     */
    @Column(name = "confirm_temp_price")
    private String confirmTempPrice;
    /**
     * 费用总额
     */
    @Column(name = "rate_total")
    private BigDecimal rateTotal;

    @Transient
    private String insertTimeFrom;
    @Transient
    private String insertTimeTo;
    @Transient
    private String iEDateFrom;
    @Transient
    private String iEDateTo;
    @Transient
    private String voyageDateFrom;
    @Transient
    private String voyageDateTo;
    @Transient
    private String shipDateFrom;
    @Transient
    private String shipDateTo;
    @Transient
    private String declareDateFrom;
    @Transient
    private String declareDateTo;

    @Transient
    private String apprDateFrom;
    @Transient
    private String apprDateTo;

    @Transient
    private String arrivalDateFrom;
    @Transient
    private String arrivalDateTo;
    @Transient
    private String billDeclareDateFrom;
    @Transient
    private String billDeclareDateTo;
    @Transient
    private String entryDeclareDateFrom;
    @Transient
    private String entryDeclareDateTo;
    @Transient
    private String arrivalPortDateFrom;
    @Transient
    private String arrivalPortDateTo;
    @Transient
    private String noticeDateFrom;
    @Transient
    private String noticeDateTo;
    @Transient
    private String dutyDateFrom;
    @Transient
    private String dutyDateTo;
    @Transient
    private String checkDateFrom;
    @Transient
    private String checkDateTo;
    @Transient
    private String customsCheckDateFrom;
    @Transient
    private String customsCheckDateTo;
    @Transient
    private String inputDateFrom;
    @Transient
    private String inputDateTo;
    @Transient
    private String deliveryDateFrom;
    @Transient
    private String deliveryDateTo;
}
