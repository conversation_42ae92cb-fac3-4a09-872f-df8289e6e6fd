package com.dcjet.cs_cus.ft.service;


import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListFtParam;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListHeadFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListHeadFtParam;
import com.dcjet.cs.util.WeekUnit;
import com.dcjet.cs_cus.ft.dao.VDecErpIHeadListFtMapper;
import com.dcjet.cs_cus.ft.mapper.VDecErpIFtDtoMapper;
import com.dcjet.cs_cus.ft.model.VDecErpIHeadFt;
import com.dcjet.cs_cus.ft.model.VDecErpIHeadListFt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * @author: 沈振宇
 * @date: 2019-03-11
 */
@Service
public class VDecErpIFtService extends BaseService<VDecErpIHeadListFt> {

    @Resource
    private VDecErpIHeadListFtMapper vDecErpIHeadListFtMapper;

    @Override
    public Mapper<VDecErpIHeadListFt> getMapper() {
        return vDecErpIHeadListFtMapper;
    }

    @Resource
    private VDecErpIFtDtoMapper vDecErpIFtDtoMapper;
    @Resource
    private PCodeHolder pCodeHolder;


    /**
     * 获取分页信息
     *
     * @param vDecErpIHeadListFtParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<VDecErpIHeadListFtDto>> getListPaged(VDecErpIHeadListFtParam vDecErpIHeadListFtParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecErpIHeadListFt vDecErpIHeadListFt = vDecErpIFtDtoMapper.toPo(vDecErpIHeadListFtParam);
        vDecErpIHeadListFt.setTradeCode(userInfo.getCompany());
        Page<VDecErpIHeadListFt> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vDecErpIHeadListFtMapper.getList(vDecErpIHeadListFt));
        List<VDecErpIHeadListFtDto> matImgexgDtos = page.getResult().stream().map(head -> {
            VDecErpIHeadListFtDto dto = vDecErpIFtDtoMapper.toDto(head);
            dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
            //String allWrap=(StringUtils.isBlank(dto.getWrapType())?"":dto.getWrapType()+',')+dto.getWrapType2();
            String allWrap = dto.getWrapTypeName();
            if (StringUtils.isNotBlank(allWrap)) {
                String newWarpList = "";
                String[] warpList = allWrap.split(",");
                for (String warp : warpList) {
                    String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                    if (StringUtils.isNotBlank(warpName)) {
                        newWarpList = newWarpList + warpName + "/";
                    }
                }
                if (newWarpList.length() > 0) {
                    dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                }
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecErpIHeadListFtDto>> paged = ResultObject.createInstance(matImgexgDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }


    /**
     * 功能描述:查询所有数据
     *
     * @param vDecErpIHeadListFtParam
     * @param userInfo
     * @return
     */
    public List<VDecErpIHeadListFtDto> selectAll(VDecErpIHeadListFtParam vDecErpIHeadListFtParam, PageParam pageParam, UserInfoToken userInfo) {
        VDecErpIHeadListFt vDecErpIHeadListFt = vDecErpIFtDtoMapper.toPo(vDecErpIHeadListFtParam);
        vDecErpIHeadListFt.setTradeCode(userInfo.getCompany());
        List<VDecErpIHeadListFtDto> vDecErpIHeadListDtos = new ArrayList<>();
        List<VDecErpIHeadListFt> vDecEEntries = vDecErpIHeadListFtMapper.getList(vDecErpIHeadListFt);
        if (CollectionUtils.isNotEmpty(vDecEEntries)) {
            vDecErpIHeadListDtos = vDecEEntries.stream().map(head -> {
                VDecErpIHeadListFtDto dto = vDecErpIFtDtoMapper.toDto(head);
                dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
                String allWrap = dto.getWrapTypeName();
                if (StringUtils.isNotBlank(allWrap)) {
                    String newWarpList = "";
                    String[] warpList = allWrap.split(",");
                    for (String warp : warpList) {
                        String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                        if (StringUtils.isNotBlank(warpName)) {
                            newWarpList = newWarpList + warpName + "/";
                        }
                    }
                    if (newWarpList.length() > 0) {
                        dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                    }
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecErpIHeadListDtos;
    }

    /**
     * 获取分页信息
     *
     * @param vDecErpIHeadListHeadFtParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<VDecErpIHeadListHeadFtDto>> getListHeadPaged(VDecErpIHeadListHeadFtParam vDecErpIHeadListHeadFtParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecErpIHeadFt vDecErpIHeadFt = vDecErpIFtDtoMapper.toHeadPo(vDecErpIHeadListHeadFtParam);
        vDecErpIHeadFt.setTradeCode(userInfo.getCompany());
        Page<VDecErpIHeadFt> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vDecErpIHeadListFtMapper.getListHead(vDecErpIHeadFt));
        List<VDecErpIHeadListHeadFtDto> vDecErpIHeadListHeadDtos = page.getResult().stream().map(head -> {
            VDecErpIHeadListHeadFtDto dto = vDecErpIFtDtoMapper.toHeadDto(head);
            dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
            String allWrap = dto.getWrapTypeName();
            if (StringUtils.isNotBlank(allWrap)) {
                String newWarpList = "";
                String[] warpList = allWrap.split(",");
                for (String warp : warpList) {
                    String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                    if (StringUtils.isNotBlank(warpName)) {
                        newWarpList = newWarpList + warpName + "/";
                    }
                }
                if (newWarpList.length() > 0) {
                    dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                }
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecErpIHeadListHeadFtDto>> paged = ResultObject.createInstance(vDecErpIHeadListHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VDecErpIHeadListHeadFtDto> selectHeadAll(VDecErpIHeadListHeadFtParam exportParam, UserInfoToken userInfo) {
        VDecErpIHeadFt vDecErpIHeadFt = vDecErpIFtDtoMapper.toHeadPo(exportParam);
        vDecErpIHeadFt.setTradeCode(userInfo.getCompany());
        List<VDecErpIHeadListHeadFtDto> vDecErpIHeadListHeadDtos = new ArrayList<>();
        List<VDecErpIHeadFt> vDecErpIHeads = vDecErpIHeadListFtMapper.getListHead(vDecErpIHeadFt);
        if (CollectionUtils.isNotEmpty(vDecErpIHeads)) {
            vDecErpIHeadListHeadDtos = vDecErpIHeads.stream().map(head -> {
                VDecErpIHeadListHeadFtDto dto = vDecErpIFtDtoMapper.toHeadDto(head);
                dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
                String allWrap = dto.getWrapTypeName();
                if (StringUtils.isNotBlank(allWrap)) {
                    String newWarpList = "";
                    String[] warpList = allWrap.split(",");
                    for (String warp : warpList) {
                        String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                        if (StringUtils.isNotBlank(warpName)) {
                            newWarpList = newWarpList + warpName + "/";
                        }
                    }
                    if (newWarpList.length() > 0) {
                        dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                    }
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecErpIHeadListHeadDtos;
    }

    /**
     * 汇总金额
     *
     * @param userInfo
     * @return
     */
    public String sumDecTotal(VDecErpIHeadListFtParam vDecErpIHeadListHeadFtParam, UserInfoToken userInfo) {
        String sumTotal = "";
        VDecErpIHeadListFt vDecErpIHeadListFt = vDecErpIFtDtoMapper.toPo(vDecErpIHeadListHeadFtParam);
        vDecErpIHeadListFt.setTradeCode(userInfo.getCompany());
        String total = vDecErpIHeadListFtMapper.getSumDecTotal(vDecErpIHeadListFt);
        sumTotal = xdoi18n.XdoI18nUtil.t("汇总金额:") + new BigDecimal(total).setScale(2, BigDecimal.ROUND_HALF_UP);

        return sumTotal;
    }

    /**
     * 报表中心-进口提单表头数据更新
     */
    public void reportDecIHead(String tradeCode) {
        vDecErpIHeadListFtMapper.deleteReportDecIHeadFt(tradeCode);
        vDecErpIHeadListFtMapper.insertReportDecIHeadFt(tradeCode);
    }

    /**
     * 报表中心-进口提单表头表体数据更新
     */
    public void reportDecIHeadList(String tradeCode) {
        vDecErpIHeadListFtMapper.deleteReportDecIHeadListFt(tradeCode);
        vDecErpIHeadListFtMapper.insertReportDecIHeadListFt(tradeCode);
    }
}
