package com.dcjet.cs_cus.ft.dao;

import com.dcjet.cs_cus.ft.model.VDecErpIHeadFt;
import com.dcjet.cs_cus.ft.model.VDecErpIHeadListFt;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* DecErpIHeadN
* <AUTHOR>
* @date: 2019-4-22
*/
public interface VDecErpIHeadListFtMapper extends Mapper<VDecErpIHeadListFt> {
    /**
     * 查询获取数据
     * @param vDecErpIFtHeadList
     * @return
     */
    List<VDecErpIHeadListFt> getList(VDecErpIHeadListFt vDecErpIFtHeadList);

    String getSumDecTotal(VDecErpIHeadListFt vDecErpIFtHeadList);

 /**
     * 查询获取数据
     * @param vDecErpIHeadFt
     * @return
     */
    List<VDecErpIHeadFt> getListHead(VDecErpIHeadFt vDecErpIHeadFt);


    Integer selectDataCount(VDecErpIHeadFt vDecErpIFtHeadFt);

    Integer selectDataCountAll(VDecErpIHeadListFt vDecErpIFtHeadList);

    int deleteReportDecIHeadFt(String tradeCode);

    int insertReportDecIHeadFt(String tradeCode);

    int deleteReportDecIHeadListFt(String tradeCode);

    int insertReportDecIHeadListFt(String tradeCode);
}
