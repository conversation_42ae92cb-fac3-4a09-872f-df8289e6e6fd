package com.dcjet.cs_cus.ft.mapper;


import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListFtParam;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListHeadFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListHeadFtParam;
import com.dcjet.cs_cus.ft.model.VDecErpIHeadFt;
import com.dcjet.cs_cus.ft.model.VDecErpIHeadListFt;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-03-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VDecErpIFtDtoMapper {

    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpIHeadListFtDto toDto(VDecErpIHeadListFt po);


    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpIHeadListFt toPo(VDecErpIHeadListFtParam po);


    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpIHeadListHeadFtDto toHeadDto(VDecErpIHeadFt po);

    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpIHeadFt toHeadPo(VDecErpIHeadListHeadFtParam po);
}
