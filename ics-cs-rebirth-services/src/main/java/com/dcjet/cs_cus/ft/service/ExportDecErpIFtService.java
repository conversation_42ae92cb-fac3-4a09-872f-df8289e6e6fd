package com.dcjet.cs_cus.ft.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpIHeadListFtParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.WeekUnit;
import com.dcjet.cs_cus.ft.dao.VDecErpIHeadListFtMapper;
import com.dcjet.cs_cus.ft.mapper.VDecErpIFtDtoMapper;
import com.dcjet.cs_cus.ft.model.VDecErpIHeadListFt;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 进口提单-表头表体异步导出
 * @author: WJ
 * @createDate: 2020/9/14 14:05
 */
@Component
public class ExportDecErpIFtService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private VDecErpIHeadListFtMapper vDecErpIHeadListFtMapper;

    @Resource
    private VDecErpIFtDtoMapper vDecErpIFtDtoMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private CommonService commonService;

    private final String taskName = xdoi18n.XdoI18nUtil.t("丰田进口报表表头表体导出(I_DEC_ERP_FT)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("I_DEC_ERP_FT");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        VDecErpIHeadListFtParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        VDecErpIHeadListFt vDecErpIHeadListFt = vDecErpIFtDtoMapper.toPo(exportParam);
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(vDecErpIHeadListFt));
        Integer count = vDecErpIHeadListFtMapper.selectDataCountAll(vDecErpIHeadListFt);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        VDecErpIHeadListFtParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        VDecErpIHeadListFt vDecErpIHeadListFt = vDecErpIFtDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(vDecErpIHeadListFt));
        Page<VDecErpIHeadListFt> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vDecErpIHeadListFtMapper.getList(vDecErpIHeadListFt));

        List<VDecErpIHeadListFtDto> vDecErpIHeadListFtDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            vDecErpIHeadListFtDtos = page.getResult().stream().map(head -> {
                VDecErpIHeadListFtDto dto = vDecErpIFtDtoMapper.toDto(head);
                dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
                String allWrap = dto.getWrapTypeName();
                if (StringUtils.isNotBlank(allWrap)) {
                    String newWarpList = "";
                    String[] warpList = allWrap.split(",");
                    for (String warp : warpList) {
                        String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                        if (StringUtils.isNotBlank(warpName)) {
                            newWarpList = newWarpList + warpName + "/";
                        }
                    }
                    if (newWarpList.length() > 0) {
                        dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                    }
                }
                return dto;
            }).collect(Collectors.toList());
        }

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(vDecErpIHeadListFtDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    public List<VDecErpIHeadListFtDto> convertForPrint(List<VDecErpIHeadListFtDto> list) {
        for (VDecErpIHeadListFtDto item : list) {
            if (StringUtils.isNotBlank(item.getBondMark())) {
                item.setBondMark(CommonEnum.BondMarkEnum.getValue(item.getBondMark()));
            }
            if (StringUtils.isNotBlank(item.getGMark())) {
                item.setGMark(CommonEnum.GMarkEnum.getValue(item.getGMark()));
            }
            if (StringUtils.isNotBlank(item.getContainerType())) {
                item.setContainerType(item.getContainerType() + "|" + CommonEnum.ContainerEnum.getValue(item.getContainerType()));
            }
            if (StringUtils.isNotBlank(item.getInsurMark())) {
                item.setInsurMark(CommonEnum.FeeMarkEnum.getValue(item.getInsurMark()));
            }
            if (StringUtils.isNotBlank(item.getFeeMark())) {
                item.setFeeMark(CommonEnum.FeeMarkEnum.getValue(item.getFeeMark()));
            }
            if (StringUtils.isNotBlank(item.getOtherMark())) {
                item.setOtherMark(CommonEnum.FeeMarkEnum.getValue(item.getOtherMark()));
            }
            if (StringUtils.isNotBlank(item.getMergeType())) {
                item.setMergeType(CommonEnum.MERGE_TYPE.getValue(item.getMergeType()));
            }
            if (StringUtils.isNotBlank(item.getBillStatus())) {
                item.setBillStatus(item.getBillStatus() + "|" + CommonEnum.BillEnum.getValue(item.getBillStatus()));
            }
//            if (StringUtils.isNotBlank(item.getOverseasShipper())) {
//                item.setOverseasShipperName(item.getOverseasShipper()  +" "+ item.getOverseasShipperName());
//            }
            if (StringUtils.isNotBlank(item.getReceiveCode())) {
                item.setReceiveName(item.getReceiveCode() + " " + item.getReceiveName());
            }
            item.setDespPort(commonService.convertPCode(item.getDespPort(), PCodeType.PORT_LIN));
            item.setCutMode(commonService.convertPCode(item.getCutMode(), PCodeType.LEVYTYPE));
            item.setInsurCurr(commonService.convertPCode(item.getInsurCurr(), PCodeType.CURR_OUTDATED));
            item.setOtherCurr(commonService.convertPCode(item.getOtherCurr(), PCodeType.CURR_OUTDATED));
            item.setFeeCurr(commonService.convertPCode(item.getFeeCurr(), PCodeType.CURR_OUTDATED));
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(), PCodeType.TRANSF));
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(), PCodeType.TRADE));
            item.setListTradeMode(commonService.convertPCode(item.getListTradeMode(), PCodeType.TRADE));
            item.setTransMode(commonService.convertPCode(item.getTransMode(), PCodeType.TRANSAC));
            item.setMasterCustoms(commonService.convertPCode(item.getMasterCustoms(), PCodeType.CUSTOMS_REL));
            item.setTradeName(commonService.convertPCode(item.getTrafName(), PCodeType.TRANSF));
            item.setTradeNation(commonService.convertPCode(item.getTradeNation(), PCodeType.COUNTRY_OUTDATED));
            item.setTradeCountry(commonService.convertPCode(item.getTradeCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setIEPort(commonService.convertPCode(item.getIEPort(), PCodeType.CUSTOMS_REL));
            item.setWrapType(commonService.convertPCode(item.getWrapType(), PCodeType.WRAP));
            item.setOriginCountry(commonService.convertPCode(item.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setCurr(commonService.convertPCode(item.getCurr(), PCodeType.CURR_OUTDATED));
            item.setDutyMode(commonService.convertPCode(item.getDutyMode(), PCodeType.LEVYMODE));
            item.setUnit(commonService.convertPCode(item.getUnit(), PCodeType.UNIT));
            item.setUnit1(commonService.convertPCode(item.getUnit1(), PCodeType.UNIT));
            item.setUnit2(commonService.convertPCode(item.getUnit2(), PCodeType.UNIT));
            item.setDestinationCountry(commonService.convertPCode(item.getDestinationCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setEntryPort(commonService.convertPCode(item.getEntryPort(), PCodeType.CIQ_ENTY_PORT));
            if (StringUtils.isNotBlank(item.getEntryStatus())) {
                item.setEntryStatus(item.getEntryStatus() + "|" + item.getEntryStatusName());
            }
            if (StringUtils.isNotBlank(item.getDeclareCodeCustoms())) {
                item.setDeclareCodeCustoms(item.getDeclareCode() + "|" + item.getDeclareNameCustoms());
            }

        }
        return list;
    }

    private VDecErpIHeadListFtParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        VDecErpIHeadListFtParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, VDecErpIHeadListFtParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
