package com.dcjet.cs_cus.ft.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-6-13
 */
@Setter
@Getter
@Table(name = "t_cgw_ft_report_dec_i_head_list")
public class VDecErpIHeadListFt implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     *
     */
    @Column(name = "erp_head_id")
    private String erpHeadId;
    /**
     *
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     *
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     *
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     *
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     *
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 提单内部编号
     */
    @Column(name = "erp_ems_list_no")
    private String erpEmsListNo;
    /**
     * 经营企业名称
     */
    @Column(name = "trade_name")
    private String tradeName;
    /**
     * 经营企业社会信用代码
     */
    @Column(name = "trade_credit_code")
    private String tradeCreditCode;
    /**
     * 申报单位编码
     */
    @Column(name = "declare_code")
    private String declareCode;
    /**
     * 申报单位名称
     */
    @Column(name = "declare_name")
    private String declareName;
    /**
     * 申报单位社会信用代码
     */
    @Column(name = "declare_credit_code")
    private String declareCreditCode;
    /**
     * 供应商编码
     */
    @Column(name = "supplier_code")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @Column(name = "supplier_name")
    private String supplierName;
    /**
     * 货代编码
     */
    @Column(name = "forward_code")
    private String forwardCode;
    /**
     * 货代名称
     */
    @Column(name = "forward_name")
    private String forwardName;
    /**
     * 进境关别代码
     */
    @Column(name = "i_e_port")
    @JsonProperty("iEPort")
    private String iEPort;
    /**
     * 申报地海关
     */
    @Column(name = "master_customs")
    private String masterCustoms;
    /**
     * 监管方式(贸易方式)
     */
    @Column(name = "trade_mode")
    private String tradeMode;
    /**
     * 运输方式
     */
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 料件成品标记
     */
    @Column(name = "g_mark")
    @JsonProperty("gMark")
    private String gMark;
    /**
     * 贸易国别
     */
    @Column(name = "trade_nation")
    private String tradeNation;
    /**
     * 归并类型 0-自动归并 1-人工归并
     */
    @Column(name = "merge_type")
    private String mergeType;
    /**
     * 备注
     */
    @Column(name = "note")
    private String note;
    /**
     * 成交方式
     */
    @Column(name = "trans_mode")
    private String transMode;
    /**
     * 启运国
     */
    @Column(name = "trade_country")
    private String tradeCountry;
    /**
     * 启运港
     */
    @Column(name = "desp_port")
    private String despPort;
    /**
     * 价格说明
     */
    @Column(name = "promise_items")
    private String promiseItems;
    /**
     * 有效标志 0 有效，1 无效
     */
    @Column(name = "valid_mark")
    private String validMark;
    /**
     * 备案号
     */
    @Column(name = "ems_no")
    private String emsNo;
    /**
     * 分提运单
     */
    @Column(name = "hawb")
    private String hawb;
    /**
     * 合同协议号
     */
    @Column(name = "contr_no")
    private String contrNo;
    /**
     * 许可证号
     */
    @Column(name = "license_no")
    private String licenseNo;
    /**
     * 发票号码
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 件数
     */
    @Column(name = "pack_num")
    private Integer packNum;
    /**
     * 体积
     */
    @Column(name = "volume_total")
    private BigDecimal volumeTotal;
    /**
     * 总净重
     */
    @Column(name = "net_wt_total")
    private BigDecimal netWtTotal;
    /**
     * 经停港
     */
    @Column(name = "dest_port")
    private String destPort;
    /**
     * 包装种类
     */
    @Column(name = "wrap_type")
    private String wrapType;
    /**
     * 运输工具名称
     */
    @Column(name = "traf_name")
    private String trafName;
    /**
     * 航次号
     */
    @Column(name = "voyage_no")
    private String voyageNo;
    /**
     * 杂费币制
     */
    @Column(name = "other_curr")
    private String otherCurr;
    /**
     * 杂费类型
     */
    @Column(name = "other_mark")
    private String otherMark;
    /**
     * 杂费
     */
    @Column(name = "other_rate")
    private BigDecimal otherRate;
    /**
     * 运费币制
     */
    @Column(name = "fee_curr")
    private String feeCurr;
    /**
     * 运费类型
     */
    @Column(name = "fee_mark")
    private String feeMark;
    /**
     * 运费
     */
    @Column(name = "fee_rate")
    private BigDecimal feeRate;
    /**
     * 保费币制
     */
    @Column(name = "insur_curr")
    private String insurCurr;
    /**
     * 保费类型
     */
    @Column(name = "insur_mark")
    private String insurMark;
    /**
     * 保费
     */
    @Column(name = "insur_rate")
    private BigDecimal insurRate;
    /**
     * 总毛重
     */
    @Column(name = "gross_wt_total")
    private BigDecimal grossWtTotal;
    /**
     * 入口日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "i_e_date")
    @JsonProperty("iEDate")
    private Date iEDate;
    /**
     * 境内目的地
     */
    @Column(name = "district_code")
    private String districtCode;
    /**
     * 境内收货人编码
     */
    @Column(name = "receive_code")
    private String receiveCode;
    /**
     * 境内收货人名称
     */
    @Column(name = "receive_name")
    private String receiveName;
    /**
     * 航班日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "voyage_date")
    private Date voyageDate;
    /**
     * 出货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ship_date")
    private Date shipDate;
    /**
     * 境外发货人编码
     */
    @Column(name = "overseas_shipper")
    private String overseasShipper;
    /**
     * 境外发货人名称
     */
    @Column(name = "overseas_shipper_name")
    private String overseasShipperName;
    /**
     * 征免性质
     */
    @Column(name = "cut_mode")
    private String cutMode;
    /**
     * 仓库存放地点
     */
    @Column(name = "warehouse")
    private String warehouse;
    /**
     * 表头原产国
     */
    @Column(name = "head_destination_country")
    private String headDestinationCountry;
    /**
     * 表体原产国
     */
    @Column(name = "destination_country")
    private String destinationCountry;
    /**
     * 入境口岸
     */
    @Column(name = "entry_port")
    private String entryPort;
    /**
     * 发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "declare_date")
    private Date declareDate;
    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "appr_date")
    private Date apprDate;
    /**
     * 审核人
     */
    @Column(name = "appr_user")
    private String apprUser;
    /**
     * 审核状态
     */
    @Column(name = "appr_status")
    private String apprStatus;
    /**
     * 审核人名称
     */
    @Column(name = "appr_user_name")
    private String apprUserName;
    /**
     * 审核状态名称
     */
    @Column(name = "appr_status_name")
    private String apprStatusName;
    /**
     * 随附单据是否上传
     */
    @Column(name = "attach")
    private String attach;
    /**
     * 随附单据是否上传 0 无 1 有
     */
    @Column(name = "attach_name")
    private String attachName;
    /**
     * 保完税标识 0 保税 1 非保税
     */
    @Column(name = "bond_mark")
    private String bondMark;
    /**
     * 数据来源 0大提单 1 小提单
     */
    @Column(name = "data_source")
    private String dataSource;
    /**
     * 报关标志 1报关 2非报关
     */
    @Column(name = "dclcus_mark")
    private String dclcusMark;
    /**
     * 报关类型
     */
    @Column(name = "dclcus_type")
    private String dclcusType;
    /**
     * 报关单类型
     */
    @Column(name = "entry_type")
    private String entryType;
    /**
     * 申请表编号/申报表编号
     */
    @Column(name = "apply_no")
    private String applyNo;
    /**
     * 关联核注清单编号
     */
    @Column(name = "rel_list_no")
    private String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @Column(name = "rel_ems_no")
    private String relEmsNo;
    /**
     * 境内货源地(行政区域)
     */
    @Column(name = "district_post_code")
    private String districtPostCode;
    /**
     * shipfrom代码
     */
    @Column(name = "ship_from")
    private String shipFrom;
    /**
     * 申报单位编码(基础参数)
     */
    @Column(name = "declare_code_customs")
    private String declareCodeCustoms;
    /**
     * 申报单位名称(基础参数)
     */
    @Column(name = "declare_name_customs")
    private String declareNameCustoms;
    /**
     *
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     *
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 币值
     */
    @Column(name = "head_curr")
    private String headCurr;
    /**
     * 计费重量
     */
    @Column(name = "c_weight")
    @JsonProperty("cWeight")
    private BigDecimal cWeight;
    /**
     * 总金额
     */
    @Column(name = "sum_dec_total")
    private BigDecimal sumDecTotal;
    /**
     * 表体提单内部编号
     */
    @Column(name = "list_ems_list_no")
    private String listEmsListNo;
    /**
     * 表体报完税标识 0 保税 1 非保税list_bond_mark
     */
    @Column(name = "list_bond_mark")
    private String listBondMark;
    /**
     * 表体物料类型标识
     */
    @Column(name = "list_g_mark")
    private String listGMark;
    /**
     * 表头SID
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * 流水号
     */
    @Column(name = "serial_no")
    private Integer serialNo;
    /**
     * 备案序号
     */
    @Column(name = "g_no")
    @JsonProperty("gNo")
    private Integer gNo;
    /**
     * 备案料号
     */
    @Column(name = "cop_g_no")
    private String copGNo;
    /**
     * 商品编码
     */
    @Column(name = "code_t_s")
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "g_name")
    @JsonProperty("gName")
    private String gName;
    /**
     * 申报规格型号
     */
    @Column(name = "g_model")
    @JsonProperty("gModel")
    private String gModel;
    /**
     * 计量单位
     */
    @Column(name = "unit")
    private String unit;
    /**
     * 法一单位
     */
    @Column(name = "unit_1")
    private String unit1;
    /**
     * 法二单位
     */
    @Column(name = "unit_2")
    private String unit2;
    /**
     * 原产国
     */
    @Column(name = "origin_country")
    private String originCountry;
    /**
     * 申报单价
     */
    @Column(name = "dec_price")
    private BigDecimal decPrice;
    /**
     * 申报总价
     */
    @Column(name = "dec_total")
    private BigDecimal decTotal;
    /**
     * 美元统计总金额
     */
    @Column(name = "usd_price")
    private BigDecimal usdPrice;
    /**
     * 币制
     */
    @Column(name = "curr")
    private String curr;
    /**
     * 法一数量
     */
    @Column(name = "qty_1")
    private BigDecimal qty1;
    /**
     * 法二数量
     */
    @Column(name = "qty_2")
    private BigDecimal qty2;
    /**
     * 重量比例因子
     */
    @Column(name = "factor_wt")
    private BigDecimal factorWt;
    /**
     * 第一比例因子
     */
    @Column(name = "factor_1")
    private BigDecimal factor1;
    /**
     * 第二比例因子
     */
    @Column(name = "factor_2")
    private BigDecimal factor2;
    /**
     * 申报数量
     */
    @Column(name = "qty")
    private BigDecimal qty;
    /**
     * 用途
     */
    @Column(name = "use_type")
    private String useType;
    /**
     * 征免方式
     */
    @Column(name = "duty_mode")
    private String dutyMode;
    /**
     * 单耗版本号
     */
    @Column(name = "exg_version")
    private String exgVersion;
    /**
     * 归并序号
     */
    @Column(name = "entry_g_no")
    private Integer entryGNo;
    /**
     * 账册企业内部编号
     */
    @Column(name = "cop_ems_no")
    private String copEmsNo;
    /**
     * 归类标记
     */
    @Column(name = "class_mark")
    private String classMark;
    /**
     * 企业物料名称
     */
    @Column(name = "cop_g_name")
    private String copGName;
    /**
     * 企业物料描述
     */
    @Column(name = "cop_g_model")
    private String copGModel;
    /**
     * 订单号码
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 企业料号
     */
    @Column(name = "fac_g_no")
    private String facGNo;
    /**
     * 关联单号
     */
    @Column(name = "linked_no")
    private String linkedNo;
    /**
     * 表体备注
     */
    @Column(name = "list_note")
    private String listNote;
    /**
     * 备注1
     */
    @Column(name = "note_1")
    private String note1;
    /**
     * 备注2
     */
    @Column(name = "note_2")
    private String note2;
    /**
     * 备注3
     */
    @Column(name = "note_3")
    private String note3;
    /**
     * 毛重
     */
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * 净重
     */
    @Column(name = "net_wt")
    private BigDecimal netWt;
    /**
     * 体积
     */
    @Column(name = "volume")
    private BigDecimal volume;
    /**
     * 入库关联编号
     */
    @Column(name = "in_out_no")
    private String inOutNo;
    /**
     * 成本中心
     */
    @Column(name = "cost_center")
    private String costCenter;
    /**
     * 关联单行号
     */
    @Column(name = "line_no")
    private String lineNo;
    /**
     * 表体发票号码
     */
    @Column(name = "list_invoice_no")
    private String listInvoiceNo;
    /**
     * 表体监管方式
     */
    @Column(name = "list_trade_mode")
    private String listTradeMode;
    /**
     * 表体备案号
     */
    @Column(name = "list_ems_no")
    private String listEmsNo;
    /**
     * 模拟项号
     */
    @Column(name = "item_no")
    private String itemNo;
    /**
     * 采购人员
     */
    @Column(name = "buyer")
    private String buyer;
    /**
     * 到货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "arrival_date")
    private Date arrivalDate;
    /**
     * 转关单号
     */
    @Column(name = "trans_no")
    private String transNo;
    /**
     * 破损标记0-正常 1-破损
     */
    @Column(name = "damage_mark")
    private String damageMark;
    /**
     * 运费
     */
    @Column(name = "freight")
    private BigDecimal freight;
    /**
     * 运费币制
     */
    @Column(name = "freight_curr")
    private String freightCurr;
    /**
     * 运输费用
     */
    @Column(name = "customs_fee")
    private BigDecimal customsFee;
    /**
     * 清单企业内部编号
     */
    @Column(name = "bill_ems_list_no")
    private String billEmsListNo;
    /**
     * 核注清单编号
     */
    @Column(name = "list_no")
    private String listNo;
    /**
     * 清单申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "bill_declare_date")
    private Date billDeclareDate;
    /**
     * 报关单申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "entry_declare_date")
    private Date entryDeclareDate;
    /**
     * -1 未发送 0 发送失败  1发送成功 2发送中 J1 通过 J3 退单 JDL 删单
     */
    @Column(name = "bill_status")
    private String billStatus;
    /**
     * 报关单号
     */
    @Column(name = "entry_no")
    private String entryNo;
    /**
     * 报关单统一编号
     */
    @Column(name = "seq_no")
    private String seqNo;
    /**
     * 特殊关系确认
     */
    @Column(name = "confirm_special")
    private String confirmSpecial;
    /**
     * 价格影响确认
     */
    @Column(name = "confirm_price")
    private String confirmPrice;
    /**
     * 支持特许权使用费确认
     */
    @Column(name = "confirm_royalties")
    private String confirmRoyalties;
    /**
     * 自报自缴
     */
    @Column(name = "duty_self")
    private String dutySelf;
    /**
     * 海关查验结果
     */
    @Column(name = "customs_check_result")
    private String customsCheckResult;
    /**
     * 总税款
     */
    @Column(name = "tax_total")
    private BigDecimal taxTotal;
    /**
     * 其他进口环节税
     */
    @Column(name = "other_tax")
    private BigDecimal otherTax;
    /**
     * 增值税
     */
    @Column(name = "tax_price")
    private BigDecimal taxPrice;
    /**
     * 关税
     */
    @Column(name = "duty_price")
    private BigDecimal dutyPrice;
    /**
     * 到港日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "arrival_port_date")
    private Date arrivalPortDate;
    /**
     * 放行日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "pass_date")
    private Date passDate;
    /**
     * 附加税
     */
    @Column(name = "add_tax_price")
    private BigDecimal addTaxPrice;
    /**
     * 到厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "delivery_date")
    private Date deliveryDate;
    /**
     * 到货通知日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "notice_date")
    private Date noticeDate;
    /**
     * 放行备注
     */
    @Column(name = "note_pass")
    private String notePass;
    /**
     * 报关费用
     */
    @Column(name = "logistics_fee")
    private BigDecimal logisticsFee;
    /**
     * 消费税
     */
    @Column(name = "excise_tax")
    private BigDecimal exciseTax;
    /**
     * 贸易条款
     */
    @Column(name = "trade_terms")
    private String tradeTerms;
    /**
     * 集装箱类型
     */
    @Column(name = "container_type")
    private String containerType;
    /**
     * 集装箱数量
     */
    @Column(name = "container_num")
    private Integer containerNum;
    /**
     * 包装种类2
     */
    @Column(name = "wrap_type2")
    private String wrapType2;
    /**
     * 内部备注
     */
    @Column(name = "remark")
    private String remark;
    /**
     * 包装种类名称
     */
    @Column(name = "wrap_type_name")
    private String wrapTypeName;
    /**
     * shipFrom名称
     */
    @Column(name = "ship_from_name")
    private String shipFromName;
    /**
     * 表体数量汇总
     */
    @Column(name = "qty_all")
    private BigDecimal qtyAll;
    /**
     * 表体金额汇总
     */
    @Column(name = "total_all")
    private BigDecimal totalAll;
    /**
     * 表体品名
     */
    @Column(name = "g_name_all")
    @JsonProperty("gNameAll")
    private String gNameAll;
    /**
     * 制单周期
     */
    @Column(name = "insert_week")
    private String insertWeek;
    /**
     * 表体完税价格
     */
    @Column(name = "dec_tax_price")
    private BigDecimal decTaxPrice;
    /**
     * 表体关税
     */
    @Column(name = "duty_value")
    private BigDecimal dutyValue;
    /**
     * 表体增值税
     */
    @Column(name = "tax_value")
    private BigDecimal taxValue;
    /**
     * 表体关税缓税利息
     */
    @Column(name = "duty_value_interest")
    private BigDecimal dutyValueInterest;
    /**
     * 表体增值税缓税利息
     */
    @Column(name = "tax_value_interest")
    private BigDecimal taxValueInterest;
    /**
     * 表体其他税
     */
    @Column(name = "dec_other_tax")
    private BigDecimal decOtherTax;
    /**
     * 表体税金汇总
     */
    @Column(name = "dec_tax_total")
    private BigDecimal decTaxTotal;
    /**
     * 报关单状态
     */
    @Column(name = "entry_status")
    private String entryStatus;
    /**
     * 报关单状态名称
     */
    @Column(name = "entry_status_name")
    private String entryStatusName;
    /**
     * 财务单据号
     */
    @Column(name = "finance_no")
    private String financeNo;
    /**
     * 表体sid
     */
    @Column(name = "erp_list_sid")
    private String erpListSid;
    /**
     * 转入口岸
     */
    @Column(name = "trans_port")
    private String transPort;
    /**
     * 完税日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "duty_date")
    private Date dutyDate;
    /**
     * 商检查验日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "check_date")
    private Date checkDate;
    /**
     * 海关查验日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "customs_check_date")
    private Date customsCheckDate;
    /**
     * 增值税缓征利息
     */
    @Column(name = "tax_interest")
    private BigDecimal taxInterest;
    /**
     * 公式价格确认
     */
    @Column(name = "confirm_formula_price")
    private String confirmFormulaPrice;
    /**
     * 暂定价格确认
     */
    @Column(name = "confirm_temp_price")
    private String confirmTempPrice;

    @Transient
    private String insertTimeFrom;
    @Transient
    private String insertTimeTo;
    @Transient
    private String iEDateFrom;
    @Transient
    private String iEDateTo;
    @Transient
    private String voyageDateFrom;
    @Transient
    private String voyageDateTo;
    @Transient
    private String shipDateFrom;
    @Transient
    private String shipDateTo;
    @Transient
    private String declareDateFrom;
    @Transient
    private String declareDateTo;

    @Transient
    private String apprDateFrom;
    @Transient
    private String apprDateTo;

    @Transient
    private String arrivalDateFrom;
    @Transient
    private String arrivalDateTo;
    @Transient
    private String billDeclareDateFrom;
    @Transient
    private String billDeclareDateTo;
    @Transient
    private String entryDeclareDateFrom;
    @Transient
    private String entryDeclareDateTo;
    @Transient
    private String arrivalPortDateFrom;
    @Transient
    private String arrivalPortDateTo;
    @Transient
    private String noticeDateFrom;
    @Transient
    private String noticeDateTo;
    @Transient
    private String dutyDateFrom;
    @Transient
    private String dutyDateTo;
    @Transient
    private String checkDateFrom;
    @Transient
    private String checkDateTo;
    @Transient
    private String customsCheckDateFrom;
    @Transient
    private String customsCheckDateTo;
    @Transient
    private String deliveryDateFrom;
    @Transient
    private String deliveryDateTo;
}
