package com.dcjet.cs_cus.delta.service;
import com.dcjet.cs_cus.delta.component.OcrCompareComponent;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBill;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrEmailInfo;
import com.xdo.common.exception.ArgumentException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto_cus.delta.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBookingMapper;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrBookingDtoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBooking;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Service
public class CgwDeltaOcrBookingService extends BaseService<CgwDeltaOcrBooking> {
    @Resource
    private CgwDeltaOcrBookingMapper mapper;
    @Resource
    private CgwDeltaOcrBookingDtoMapper dtoMapper;
    @Override
    public Mapper<CgwDeltaOcrBooking> getMapper() {
        return mapper;
    }

    @Resource
    private OcrCompareComponent ocrCompareComponent;
    @Resource
    private CgwDeltaOcrBillResService resService;

    @Resource
    private CgwDeltaOcrBillMapper billMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param cgwDeltaOcrBookingParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CgwDeltaOcrBookingDto>> getListPaged(CgwDeltaOcrBookingParam cgwDeltaOcrBookingParam, PageParam pageParam) {
        // 启用分页查询
        CgwDeltaOcrBooking cgwDeltaOcrBooking = dtoMapper.toPo(cgwDeltaOcrBookingParam);
        Page<CgwDeltaOcrBooking> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(cgwDeltaOcrBooking));
        List<CgwDeltaOcrBookingDto> cgwDeltaOcrBookingDtos = page.getResult().stream().map(head -> {
            CgwDeltaOcrBookingDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CgwDeltaOcrBookingDto>> paged = ResultObject.createInstance(cgwDeltaOcrBookingDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CgwDeltaOcrBookingDto> selectAll(CgwDeltaOcrBookingParam exportParam, UserInfoToken userInfo) {
        CgwDeltaOcrBooking cgwDeltaOcrBooking = dtoMapper.toPo(exportParam);
        cgwDeltaOcrBooking.setTradeCode(userInfo.getCompany());
        List<CgwDeltaOcrBookingDto> cgwDeltaOcrBookingDtos = new ArrayList<>();
        List<CgwDeltaOcrBooking> cgwDeltaOcrBookings = mapper.getList(cgwDeltaOcrBooking);
        if (CollectionUtils.isNotEmpty(cgwDeltaOcrBookings)) {
            cgwDeltaOcrBookingDtos = cgwDeltaOcrBookings.stream().map(head -> {
                CgwDeltaOcrBookingDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return cgwDeltaOcrBookingDtos;
    }

    /**
     *
     * @param enNo
     * @param token
     * @return
     */
    public CgwDeltaOcrBooking getBooking(String enNo, UserInfoToken token) {
        // todo 获取托书逻辑待开发
        return null;
    }

    /**
     * 托书比对
     * @param bill
     * @param token
     */
    public void compareBill(CgwDeltaOcrBill bill, UserInfoToken token) {
        CgwDeltaOcrBooking book = getOne(bill.getEnNo(), token);
        if (book == null) {
            book = getBooking(bill.getEnNo(), token);
            if (book == null) {
                throw new ArgumentException(400, xdoi18n.XdoI18nUtil.t("获取托书失败"));
            }
        }
        Boolean compareResult = compareToOcrBill(bill, book, token);
        bill.setStatus("4"); // 4 比对成功
        String compareResultStr = "0";
        if (compareResult) {
            compareResultStr = "1"; // 一致
        } else {
            compareResultStr = "2"; // 不一致
        }
        bill.setCompareTime(new Date());
        bill.setCompareResult(compareResultStr);
        billMapper.updateByPrimaryKey(bill);
    }

    /**
     * 比对功能
     * @param bill
     * @param book
     * @param token
     */
    public Boolean compareToOcrBill(CgwDeltaOcrBill bill, CgwDeltaOcrBooking book, UserInfoToken token) {
        // 删除旧比对结果
        resService.deleteByHeadId(bill.getSid(), token);

        Boolean compareResult;
        Boolean finalResult = true;
        // EN号
        compareResult = ocrCompareComponent.compareString(bill.getEnNo(), book.getEnNo(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("enNo", compareResult, bill, book, token);
        // shipTo
        compareResult = ocrCompareComponent.compareString(bill.getShipTo(), book.getShipTo(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("shipTo", compareResult, bill, book, token);
        // 收货人 consignee
        compareResult = ocrCompareComponent.compareString(bill.getConsignee(), book.getConsignee(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("consignee", compareResult, bill, book, token);
        // 通知人 notifyParty
        compareResult = ocrCompareComponent.compareString(bill.getNotifyParty(), book.getNotifyParty(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("notifyParty", compareResult, bill, book, token);
        // 启运港 despPort
        compareResult = ocrCompareComponent.compareString(bill.getDespPort(), book.getDespPort(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("despPort", compareResult, bill, book, token);
        // 标记唛码 markNo
        compareResult = ocrCompareComponent.compareString(bill.getMarkNo(), book.getMarkNo(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("markNo", compareResult, bill, book, token);
        // 外包装数量 outerPackaging
        compareResult = ocrCompareComponent.compareString(bill.getOuterPackaging(), book.getOuterPackaging(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("outerPackaging", compareResult, bill, book, token);
        // 主要品名 gName
        compareResult = ocrCompareComponent.compareString(bill.getGName(), book.getGName(), token);
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("gName", compareResult, bill, book, token);
        // 总毛重 (精确匹配) grossWt
        compareResult = ocrCompareComponent.compareDigital(bill.getGrossWt(), book.getGrossWt());
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("grossWt", compareResult, bill, book, token);
        // 总体积 valume
        compareResult = ocrCompareComponent.compareDigital(bill.getVolume(), book.getVolume(), BigDecimal.valueOf(0.05));
        finalResult = compareResult ? finalResult : false;
        resService.insertResRecord("volume", compareResult, bill, book, token);

        return finalResult;
    }


    public CgwDeltaOcrBooking getOne(String enNo, UserInfoToken token) {
        Example example = new Example(CgwDeltaOcrBooking.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andEqualTo("enNo", enNo);
        return mapper.selectOneByExample(example);
    }
}
