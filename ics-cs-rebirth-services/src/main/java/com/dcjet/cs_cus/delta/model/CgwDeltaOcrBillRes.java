package com.dcjet.cs_cus.delta.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Setter
@Getter
@Table(name = "t_cgw_delta_ocr_bill_res")
public class CgwDeltaOcrBillRes implements Serializable {
    private static final long serialVersionUID = 1L;

    public static String COMPARE_RESULT_TRUE = "1";
    public static String COMPARE_RESULT_FALSE = "2";

	/**
     * 主键
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * OCR识别后的提单表头SID
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 字段英文名称
     */
	@Column(name = "field_name")
	private  String fieldName;
	/**
     * OCR提单识别的值
     */
	@Column(name = "bill_value")
	private  String billValue;
	/**
     * 托书中的值
     */
	@Column(name = "booking_value")
	private  String bookingValue;
	/**
     * 比对结果(0.未比对 1.一致 2.不一致)
     */
	@Column(name = "compare_result")
	private  String compareResult;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;

	public CgwDeltaOcrBillRes initData(UserInfoToken token) {
		this.tradeCode = token.getCompany();
		this.insertUser = token.getUserNo();
		this.insertUserName = token.getUserName();
		this.insertTime = new Date();
		return this;
	}

	public void setUserInfoForUPdate(UserInfoToken token) {
		this.updateUser = token.getUserNo();
		this.updateUserName = token.getUserName();
		this.updateTime = new Date();
	}
}
