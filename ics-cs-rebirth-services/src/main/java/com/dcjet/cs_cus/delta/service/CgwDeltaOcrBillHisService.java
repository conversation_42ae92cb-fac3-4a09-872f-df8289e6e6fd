package com.dcjet.cs_cus.delta.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto_cus.delta.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillHisMapper;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrBillHisDtoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillHis;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Service
public class CgwDeltaOcrBillHisService extends BaseService<CgwDeltaOcrBillHis> {
    @Resource
    private CgwDeltaOcrBillHisMapper mapper;
    @Resource
    private CgwDeltaOcrBillHisDtoMapper dtoMapper;
    @Override
    public Mapper<CgwDeltaOcrBillHis> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param cgwDeltaOcrBillHisParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CgwDeltaOcrBillHisDto>> getListPaged(CgwDeltaOcrBillHisParam cgwDeltaOcrBillHisParam, PageParam pageParam) {
        // 启用分页查询
        CgwDeltaOcrBillHis cgwDeltaOcrBillHis = dtoMapper.toPo(cgwDeltaOcrBillHisParam);
        Page<CgwDeltaOcrBillHis> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(cgwDeltaOcrBillHis));
        List<CgwDeltaOcrBillHisDto> cgwDeltaOcrBillHisDtos = page.getResult().stream().map(head -> {
            CgwDeltaOcrBillHisDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CgwDeltaOcrBillHisDto>> paged = ResultObject.createInstance(cgwDeltaOcrBillHisDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param cgwDeltaOcrBillHisParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CgwDeltaOcrBillHisDto insert(CgwDeltaOcrBillHisParam cgwDeltaOcrBillHisParam, UserInfoToken userInfo) {
        CgwDeltaOcrBillHis cgwDeltaOcrBillHis = dtoMapper.toPo(cgwDeltaOcrBillHisParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        cgwDeltaOcrBillHis.setSid(sid);
        cgwDeltaOcrBillHis.setInsertUser(userInfo.getUserNo());
        cgwDeltaOcrBillHis.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(cgwDeltaOcrBillHis);
        return  insertStatus > 0 ? dtoMapper.toDto(cgwDeltaOcrBillHis) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param cgwDeltaOcrBillHisParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CgwDeltaOcrBillHisDto update(CgwDeltaOcrBillHisParam cgwDeltaOcrBillHisParam, UserInfoToken userInfo) {
        CgwDeltaOcrBillHis cgwDeltaOcrBillHis = mapper.selectByPrimaryKey(cgwDeltaOcrBillHisParam.getSid());
        dtoMapper.updatePo(cgwDeltaOcrBillHisParam, cgwDeltaOcrBillHis);
        cgwDeltaOcrBillHis.setUpdateUser(userInfo.getUserNo());
        cgwDeltaOcrBillHis.setUpdateTime(new Date());
        // 更新数据
        int update = mapper.updateByPrimaryKey(cgwDeltaOcrBillHis);
        return update > 0 ? dtoMapper.toDto(cgwDeltaOcrBillHis) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CgwDeltaOcrBillHisDto> selectAll(CgwDeltaOcrBillHisParam exportParam, UserInfoToken userInfo) {
        CgwDeltaOcrBillHis cgwDeltaOcrBillHis = dtoMapper.toPo(exportParam);
        cgwDeltaOcrBillHis.setTradeCode(userInfo.getCompany());
        List<CgwDeltaOcrBillHisDto> cgwDeltaOcrBillHisDtos = new ArrayList<>();
        List<CgwDeltaOcrBillHis> cgwDeltaOcrBillHiss = mapper.getList(cgwDeltaOcrBillHis);
        if (CollectionUtils.isNotEmpty(cgwDeltaOcrBillHiss)) {
            cgwDeltaOcrBillHisDtos = cgwDeltaOcrBillHiss.stream().map(head -> {
                CgwDeltaOcrBillHisDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return cgwDeltaOcrBillHisDtos;
    }
}
