package com.dcjet.cs_cus.delta.service;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.xdo.common.exception.ArgumentException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto_cus.delta.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrTransMapper;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrTransDtoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrTrans;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
//import io.sentry.event.User;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Service
public class CgwDeltaOcrTransService extends BaseService<CgwDeltaOcrTrans> {
    @Resource
    private CgwDeltaOcrTransMapper mapper;
    @Resource
    private CgwDeltaOcrTransDtoMapper dtoMapper;
    @Override
    public Mapper<CgwDeltaOcrTrans> getMapper() {
        return mapper;
    }

    ThreadLocal<Map<String, String>> cacheForDigitalTrans = new ThreadLocal<>();
    ThreadLocal<Map<String, String>> cacheForCharTrans = new ThreadLocal<>();
    /**
     *  数字类型正则
     */
    private String digitalRegex = "^-?0\\.\\d+$|^-?[1-9]+(\\.\\d+)?$";
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param cgwDeltaOcrTransParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CgwDeltaOcrTransDto>> getListPaged(CgwDeltaOcrTransParam cgwDeltaOcrTransParam, PageParam pageParam) {
        // 启用分页查询
        CgwDeltaOcrTrans cgwDeltaOcrTrans = dtoMapper.toPo(cgwDeltaOcrTransParam);
        Page<CgwDeltaOcrTrans> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(cgwDeltaOcrTrans));
        List<CgwDeltaOcrTransDto> cgwDeltaOcrTransDtos = page.getResult().stream().map(head -> {
            CgwDeltaOcrTransDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CgwDeltaOcrTransDto>> paged = ResultObject.createInstance(cgwDeltaOcrTransDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param cgwDeltaOcrTransParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CgwDeltaOcrTransDto insert(CgwDeltaOcrTransParam cgwDeltaOcrTransParam, UserInfoToken userInfo) {
        CgwDeltaOcrTrans cgwDeltaOcrTrans = dtoMapper.toPo(cgwDeltaOcrTransParam);
        if (checkForRepeat(cgwDeltaOcrTrans, userInfo)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("相同“适用栏位类型”下，转换前栏位不允许重复"));
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        cgwDeltaOcrTrans.setSid(sid);
        cgwDeltaOcrTrans.setTradeCode(userInfo.getCompany());
        cgwDeltaOcrTrans.setInsertUser(userInfo.getUserNo());
        cgwDeltaOcrTrans.setInsertUserName(userInfo.getUserName());
        cgwDeltaOcrTrans.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(cgwDeltaOcrTrans);
        return  insertStatus > 0 ? dtoMapper.toDto(cgwDeltaOcrTrans) : null;
    }


    /**
     * 功能描述:修改
     *
     * @param cgwDeltaOcrTransParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CgwDeltaOcrTransDto update(CgwDeltaOcrTransParam cgwDeltaOcrTransParam, UserInfoToken userInfo) {
        CgwDeltaOcrTrans cgwDeltaOcrTrans = mapper.selectByPrimaryKey(cgwDeltaOcrTransParam.getSid());
        dtoMapper.updatePo(cgwDeltaOcrTransParam, cgwDeltaOcrTrans);

        if (checkForRepeat(cgwDeltaOcrTrans, userInfo)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("相同“适用栏位类型”下，转换前栏位不允许重复"));
        }
        cgwDeltaOcrTrans.setUpdateUser(userInfo.getUserNo());
        cgwDeltaOcrTrans.setUpdateUserName(userInfo.getUserName());
        cgwDeltaOcrTrans.setUpdateTime(new Date());
        // 更新数据
        int update = mapper.updateByPrimaryKey(cgwDeltaOcrTrans);
        return update > 0 ? dtoMapper.toDto(cgwDeltaOcrTrans) : null;
    }

    /**
     * 重复性校验
     * @param cgwDeltaOcrTrans
     * @param token
     * @return
     */
    public Boolean checkForRepeat(CgwDeltaOcrTrans cgwDeltaOcrTrans, UserInfoToken token) {
        Example example = new Example(CgwDeltaOcrTrans.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andEqualTo("fieldType", cgwDeltaOcrTrans.getFieldType());
        criteria.andEqualTo("beforeConvert", cgwDeltaOcrTrans.getBeforeConvert());
        if (cgwDeltaOcrTrans.getSid() != null) {
            criteria.andNotEqualTo("sid", cgwDeltaOcrTrans.getSid());
        }

        int cnt = mapper.selectCountByExample(example);
        return cnt > 0;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CgwDeltaOcrTransDto> selectAll(CgwDeltaOcrTransParam exportParam, UserInfoToken userInfo) {
        CgwDeltaOcrTrans cgwDeltaOcrTrans = dtoMapper.toPo(exportParam);
        cgwDeltaOcrTrans.setTradeCode(userInfo.getCompany());
        List<CgwDeltaOcrTransDto> cgwDeltaOcrTransDtos = new ArrayList<>();
        List<CgwDeltaOcrTrans> cgwDeltaOcrTranss = mapper.getList(cgwDeltaOcrTrans);
        if (CollectionUtils.isNotEmpty(cgwDeltaOcrTranss)) {
            cgwDeltaOcrTransDtos = cgwDeltaOcrTranss.stream().map(head -> {
                CgwDeltaOcrTransDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return cgwDeltaOcrTransDtos;
    }

    /**
     * getOcrTransMap
     * @param type
     * @param token
     * @return
     */
    public Map<String, String> getOcrTransMap(String type, UserInfoToken token) {
        Example example = new Example(CgwDeltaOcrTrans.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andEqualTo("fieldType", type);

        List<CgwDeltaOcrTrans> cusParas = mapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(cusParas)) {
            return cusParas.stream().collect(Collectors.toMap(CgwDeltaOcrTrans::getBeforeConvert, CgwDeltaOcrTrans::getAfterConvert, (oldV, newV) -> newV));
        }
        return new HashMap<>();
    }
    /**
     * 转数字
     * @param tobeConvert
     * @return
     */
    public BigDecimal convertToDigital(String tobeConvert, UserInfoToken token) {
        BigDecimal converted = null;
        if (Pattern.matches(digitalRegex, tobeConvert)) {
            converted = new BigDecimal(tobeConvert);
        } else {
            if (cacheForDigitalTrans.get() == null) {
                cacheForDigitalTrans.set(getOcrTransMap(CgwDeltaOcrTrans.FIELD_TYPE_DIGITAL, token));
            }
            for(Map.Entry<String, String> kv : cacheForDigitalTrans.get().entrySet()) {
                tobeConvert = tobeConvert.replace(kv.getKey(), kv.getValue());
            }
            if (Pattern.matches(digitalRegex, tobeConvert)) {
                converted = new BigDecimal(tobeConvert);
            }
        }
        return converted;
    }

    /**
     * 字符转换
     * @param tobeConvert
     * @param token
     * @return
     */
    public String convertToChar(String tobeConvert, UserInfoToken token) {
        String converted = tobeConvert;
        if (cacheForCharTrans.get() == null) {
            cacheForCharTrans.set(getOcrTransMap(CgwDeltaOcrTrans.FIELD_TYPE_CHAR, token));
        }
        Map<String, String> cacheMap = cacheForCharTrans.get();
        for(Map.Entry<String, String> kv : cacheMap.entrySet()) {
            converted = converted.replace(kv.getKey(), kv.getValue());
        }
        return converted.toUpperCase();
    }


}
