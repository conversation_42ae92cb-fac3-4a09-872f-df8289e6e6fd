package com.dcjet.cs_cus.delta.dao;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrEmailInfo;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* CgwDeltaOcrEmailInfo
* <AUTHOR>
* @date: 2022-1-4
*/
public interface CgwDeltaOcrEmailInfoMapper extends Mapper<CgwDeltaOcrEmailInfo> {
    /**
     * 查询获取数据
     * @param cgwDeltaOcrEmailInfo
     * @return
     */
    List<CgwDeltaOcrEmailInfo> getList(CgwDeltaOcrEmailInfo cgwDeltaOcrEmailInfo);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
