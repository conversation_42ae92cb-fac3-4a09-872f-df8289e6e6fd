package com.dcjet.cs_cus.delta.component;

import com.dcjet.cs.common.dao.GwMailSendTaskAttachMapper;
import com.dcjet.cs.common.dao.GwMailSendTaskHeadMapper;
import com.dcjet.cs.common.model.GwMailSendTaskAttach;
import com.dcjet.cs.common.model.GwMailSendTaskHead;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs_cus.delta.dao.*;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrBillHisDtoMapper;
import com.dcjet.cs_cus.delta.model.*;
import com.dcjet.cs_cus.delta.model.email.DeltaOcrDataOut;
import com.dcjet.cs_cus.delta.model.email.DeltaOcrEmialAttach;
import com.dcjet.cs_cus.delta.model.email.DeltaOcrTDData;
import com.dcjet.cs_cus.delta.model.email.DeltaOcrEmailData;
import com.dcjet.cs_cus.delta.service.CgwDeltaOcrBookingService;
import com.dcjet.cs_cus.delta.service.CgwDeltaOcrReceLogService;
import com.dcjet.cs_cus.delta.service.CgwDeltaOcrTransService;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class OcrIdentificationComponent {

    @Resource
    private CgwDeltaOcrTransService transService;
    @Resource
    private CgwDeltaOcrBillHisMapper billHisMapper;
    @Resource
    private CgwDeltaOcrBillLocMapper billLocMapper;
    @Resource
    private CgwDeltaOcrBillMapper billMapper;

    @Resource
    private CgwDeltaOcrBillHisDtoMapper billHisDtoMapper;

    @Resource
    private OcrCompareComponent ocrCompareComponent;

    @Resource
    private CgwDeltaOcrBookingService bookingService;
    @Resource
    private CgwDeltaOcrReceLogService receLogService;
    @Resource
    private GwMailSendTaskHeadMapper mailHeadMapper;
    @Resource
    private GwMailSendTaskAttachMapper mailAttachMapper;
    @Resource
    private CgwDeltaOcrBillResMapper billResMapper;
    @Resource
    private CgwDeltaOcrEmailInfoMapper emailInfoMapper;

    /**
     * 识别ocr提单数据
     *
     * @param deltaOcrEmailData
     */
    @Transactional(rollbackFor = Exception.class)
    public void identify(DeltaOcrEmailData deltaOcrEmailData) {
        //
        UserInfoToken token = new UserInfoToken() {
            {
                setCompany(deltaOcrEmailData.getCorpCode());
                setUserName("kafka");
                setUserNo("kafka");
            }
        };

        // insert rece log
        String receLogData = JsonObjectMapper.getInstance().toJson(deltaOcrEmailData);
        String receLogId = receLogService.insertReceLog("BILL", receLogData, token);

        // 无附件
        if (!deltaOcrEmailData.isHasAttach()) {
            log.warn("delta提单ocr识别警告：原邮件无附件内容");
            // 发送邮件（内容：原邮件无附件）
            sendMail(deltaOcrEmailData, false, xdoi18n.XdoI18nUtil.t("OCR附件异常"), xdoi18n.XdoI18nUtil.t("原邮件无附件"), null, token);
            return;
        }

        //
        if (CollectionUtils.isNotEmpty(deltaOcrEmailData.getKafkaAttaches())) {
            //
            deltaOcrEmailData.getKafkaAttaches().stream().forEach(kafkaAttach -> {
                if (!kafkaAttach.isOciReturnType()) {
                    log.warn("OCR请求失败，recordId：" + kafkaAttach.getRecordId() + "，message："
                            + kafkaAttach.getCallResult());
                    // 发送邮件 （内容：邮件附件识别失败，无此识别模板。识别失败的文件放在附件）
                    sendMail(deltaOcrEmailData, false,
                            xdoi18n.XdoI18nUtil.t("OCR识别附件异常"),
                            xdoi18n.XdoI18nUtil.t("邮件附件识别失败，无此识别模板。识别失败的文件放在附件\r\n ") + "recordId：" + kafkaAttach.getRecordId() + "，message：" + kafkaAttach.getCallResult(),
                            Arrays.asList(kafkaAttach), token);
                    return;
                }
                // 识别
                String billDataJson = kafkaAttach.getOciReturnData();
                DeltaOcrDataOut ocrDataOut = JsonObjectMapper.getInstance().fromJson(billDataJson, DeltaOcrDataOut.class);
                // 处理数据
                if (ocrDataOut == null || ocrDataOut.getData() == null || ocrDataOut.getData().getTDList() == null) {
                    //
                    log.warn("OCR解析提单数据失败，提单数据为空 或 数据类型不合法");
                    return;
                }

                //
                List<DeltaOcrTDData> tdDataList = ocrDataOut.getData().getTDList();
                tdDataList.stream().forEach(tdData -> {

                    // bill历史记录
                    CgwDeltaOcrBillHis billHis = new CgwDeltaOcrBillHis().initData(token);
                    billHis.assembly(tdData);
                    billHis.setHeadId(receLogId);
                    billHisMapper.insert(billHis);

                    // location信息
                    CgwDeltaOcrBillLoc billLoc = new CgwDeltaOcrBillLoc().initData(billHis.getSid(), token);
                    billLoc.assembly(tdData);
                    billLocMapper.insert(billLoc);

                    // bill数据
                    billMapper.delete(new CgwDeltaOcrBill() {
                        {
                            setTradeCode(token.getCompany());
                            setEnNo(billHis.getEnNo());
                        }
                    });
                    CgwDeltaOcrBill bill = new CgwDeltaOcrBill().initData(billHis.getSid(), token); {
                        bill.setHeadId(receLogId);
                        // convert
                        bill.setShipTo(transService.convertToChar(billHis.getShipTo(), token));
                        bill.setConsignee(transService.convertToChar(billHis.getConsignee(), token));
                        bill.setNotifyParty(transService.convertToChar(billHis.getNotifyParty(), token));
                        bill.setDespPort(transService.convertToChar(billHis.getDespPort(), token));
                        bill.setMarkNo(transService.convertToChar(billHis.getMarkNo(), token));
                        bill.setOuterPackaging(transService.convertToChar(billHis.getOuterPackaging(), token));
                        bill.setGName(transService.convertToChar(billHis.getGName(), token));

                        bill.setGrossWt(transService.convertToDigital(billHis.getGrossWt(), token));
                        bill.setVolume(transService.convertToDigital(billHis.getVolume(), token));
                    }
                    billMapper.insert(bill);

                    // emialInfo记录
                    CgwDeltaOcrEmailInfo emailInfo = new CgwDeltaOcrEmailInfo().initData(billHis.getSid(), token); {
                        emailInfo.setEmailFrom(deltaOcrEmailData.getEmailFrom());
                        emailInfo.setEmailTo(deltaOcrEmailData.getEmailTo());
                        emailInfo.setEmailCc(deltaOcrEmailData.getEmailCc());
                        emailInfo.setSendMailIdentify(deltaOcrEmailData.getSendMailIdentify());
                        emailInfo.setFileName(kafkaAttach.getFileName());
                        emailInfo.setFileType(kafkaAttach.getFileType());
                        emailInfo.setFileUri(kafkaAttach.getFileUri());
                        emailInfo.setRecordId(kafkaAttach.getRecordId());
                    }
                    emailInfoMapper.insert(emailInfo);

                    // 获取托书
                    CgwDeltaOcrBooking booking = bookingService.getBooking(bill.getEnNo(), token);
                    if (booking != null) {
                        //
                        bill.setStatus("2"); // 托书已获取
                        bill.setBookingGetTime(booking.getInsertTime());
                    } else {
                        bill.setStatus("3"); // 托书获取失败
                    }
                    billMapper.updateByPrimaryKey(bill);

                    // 比对
                    String compareResultStr = "0"; // 未比对
                    Boolean compareResult = bookingService.compareToOcrBill(bill, booking, token);

                    if (compareResult) {
                        bill.setStatus("4"); // 4 比对成功
                        compareResultStr = "1"; // 一致
                    } else {
                        compareResultStr = "2"; // 不一致
                        bill.setStatus("5"); // 5 托书获取失败
                        bill.setMailSendFlag("1"); // 邮件通知状态：1 已通知
                        //  发送邮件
                        String html = getEmailHtml(bill.getSid(), token);
                        sendMail(deltaOcrEmailData, true, xdoi18n.XdoI18nUtil.t("提单比对不一致"), html, Arrays.asList(kafkaAttach), token);
                    }
                    bill.setCompareTime(new Date());
                    bill.setCompareResult(compareResultStr);
                    billMapper.updateByPrimaryKey(bill);

                });
            });
        }
    }

    /**
     * 发送邮件
     *
     * @param deltaOcrEmailData
     * @param token
     */
    public void sendMail(DeltaOcrEmailData deltaOcrEmailData, Boolean isHtml, String subject, String body,
            List<DeltaOcrEmialAttach> attachs, UserInfoToken token) {
        GwMailSendTaskHead mailHead = new GwMailSendTaskHead().initData(token);
        mailHead.setRecipient(deltaOcrEmailData.getEmailFrom());
        mailHead.setSender(deltaOcrEmailData.getSendMailIdentify());
        mailHead.setCc(deltaOcrEmailData.getEmailCc());
        mailHead.setBussinessType("BILL");
        mailHead.setBussinessType("RISK_WARRING");
        mailHead.setDatyType("DELTA_OCR");
        mailHead.setState("0"); // 0 待发送
        mailHead.setIsCustomized("1");
        mailHead.setIsHtml(isHtml ? "1" : "0");
        mailHead.setSubject(subject);
        mailHead.setBody(body);
        mailHeadMapper.insert(mailHead);

        if (CollectionUtils.isNotEmpty(attachs)) {
            attachs.stream().forEach(attach -> {
                GwMailSendTaskAttach mailAttach = new GwMailSendTaskAttach().initData(mailHead.getSid(), token);
                mailAttach.setFileName(attach.getFileName()+".xlsx");
                mailAttach.setUrl(attach.getFileUri());
                mailAttachMapper.insert(mailAttach);
            });
        }
    }

    /**
     * 邮件重发
     * @param emailInfo
     * @param isHtml
     * @param subject
     * @param body
     * @param token
     */
    public void reSendMail(CgwDeltaOcrEmailInfo emailInfo, Boolean isHtml, String subject, String body, UserInfoToken token) {
        GwMailSendTaskHead mailHead = new GwMailSendTaskHead().initData(token);
        mailHead.setRecipient(emailInfo.getEmailFrom());
        mailHead.setSender(emailInfo.getSendMailIdentify());
        mailHead.setCc(emailInfo.getEmailCc());
        mailHead.setBussinessType("BILL");
        mailHead.setBussinessType("RISK_WARRING");
        mailHead.setDatyType("DELTA_OCR");
        mailHead.setState("0"); // 0 待发送
        mailHead.setIsCustomized("1");
        mailHead.setIsHtml(isHtml ? "1" : "0");
        mailHead.setSubject(subject);
        mailHead.setBody(body);
        mailHeadMapper.insert(mailHead);

        GwMailSendTaskAttach mailAttach = new GwMailSendTaskAttach().initData(mailHead.getSid(), token);
        mailAttach.setFileName(emailInfo.getFileName()+".xlsx");
        mailAttach.setUrl(emailInfo.getFileUri());
        mailAttachMapper.insert(mailAttach);
    }

    /**
     * 获取html格式正文
     *
     * @param billSid
     * @return
     */
    public String getEmailHtml(String billSid, UserInfoToken token) {
        String transOne = xdoi18n.XdoI18nUtil.t("栏位信息");
        String transTwo = xdoi18n.XdoI18nUtil.t("托书");
        String transThree = xdoi18n.XdoI18nUtil.t("提单识别");
        StringBuilder sb = new StringBuilder(
                "<div>" +
                "  <style>\n" +
                "    table, th, td {\n" +
                "      border:1px solid lightgray\n" +
                "    }\n" +
                "  </style>" +
                "  <table style=\"border-spacing:0\">\n" +
                "    <tr>\n" +
                "      <th>"+transOne+"</th>\n" +
                "      <th>"+transTwo+"</th>\n" +
                "      <th>"+transThree+"</th>\n" +
                "    </tr>");
        List<CgwDeltaOcrBillRes> results = billResMapper.getList(new CgwDeltaOcrBillRes() {{
                setTradeCode(token.getCompany());
                setHeadId(billSid);
            }});
        if (CollectionUtils.isNotEmpty(results)) {
            results.stream().forEach(result -> {
                //
                if ("2".equals(result.getCompareResult())) {
                    sb.append("<tr style=\"color: red\">\n");
                } else {
                    sb.append("<tr>\n");
                }
                sb.append("<td>" + CommonEnum.DELTA_OCR_FILED_DESC.getValue(result.getFieldName()) + "</td>");
                sb.append("<td>" + result.getBookingValue() + "</td>");
                sb.append("<td>" + result.getBillValue() + "</td>");
                sb.append("</tr>");
            });
        }
        sb.append("</table></div>");
        return sb.toString();
    }

}
