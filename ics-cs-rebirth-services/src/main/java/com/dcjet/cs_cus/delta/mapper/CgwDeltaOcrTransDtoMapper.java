package com.dcjet.cs_cus.delta.mapper;
import com.dcjet.cs.dto_cus.delta.*;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrTrans;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CgwDeltaOcrTransDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CgwDeltaOcrTransDto toDto(CgwDeltaOcrTrans po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CgwDeltaOcrTrans toPo(CgwDeltaOcrTransParam param);
    /**
     * 数据库原始数据更新
     * @param cgwDeltaOcrTransParam
     * @param cgwDeltaOcrTrans
     */
    void updatePo(CgwDeltaOcrTransParam cgwDeltaOcrTransParam, @MappingTarget CgwDeltaOcrTrans cgwDeltaOcrTrans);
    default void patchPo(CgwDeltaOcrTransParam cgwDeltaOcrTransParam, CgwDeltaOcrTrans cgwDeltaOcrTrans) {
        // TODO 自行实现局部更新
    }
}
