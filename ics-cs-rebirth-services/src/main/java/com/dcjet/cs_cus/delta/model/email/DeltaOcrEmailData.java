package com.dcjet.cs_cus.delta.model.email;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("OCR邮件识别内容")
@Data
public class DeltaOcrEmailData {
    @ApiModelProperty("企业编码")
    String corpCode;
    @ApiModelProperty("企业名称")
    String corpName;
    @ApiModelProperty("发件人")
    String emailFrom;
    @ApiModelProperty("收件人")
    String emailTo;
    @ApiModelProperty("抄送人")
    String emailCc;
    @ApiModelProperty("收件标识，如GW")
    String sendMailIdentify;
    @ApiModelProperty("是否含有附件")
    boolean hasAttach;
    @ApiModelProperty("附件")
    List<DeltaOcrEmialAttach> kafkaAttaches;

}
