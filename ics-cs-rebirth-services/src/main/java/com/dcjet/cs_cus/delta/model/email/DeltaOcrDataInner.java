package com.dcjet.cs_cus.delta.model.email;

import com.dcjet.cs.ocrInvoice.model.ocr.OcrFPData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrXDData;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("OcrData")
public class DeltaOcrDataInner implements Serializable {

    @JsonProperty("TDList")
    private List<DeltaOcrTDData> TDList;
}
