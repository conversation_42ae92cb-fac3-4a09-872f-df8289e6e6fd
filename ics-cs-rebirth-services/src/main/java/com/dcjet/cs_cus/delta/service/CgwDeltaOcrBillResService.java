package com.dcjet.cs_cus.delta.service;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillLocMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBill;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillLoc;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBooking;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto_cus.delta.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillResMapper;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrBillResDtoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillRes;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Service
public class CgwDeltaOcrBillResService extends BaseService<CgwDeltaOcrBillRes> {
    @Resource
    private CgwDeltaOcrBillResMapper mapper;
    @Resource
    private CgwDeltaOcrBillLocMapper locMapper;
    @Resource
    private CgwDeltaOcrBillResDtoMapper dtoMapper;
    @Override
    public Mapper<CgwDeltaOcrBillRes> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param cgwDeltaOcrBillResParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CgwDeltaOcrBillResDto>> getListPaged(CgwDeltaOcrBillResParam cgwDeltaOcrBillResParam, PageParam pageParam) {
        // 启用分页查询
        CgwDeltaOcrBillRes cgwDeltaOcrBillRes = dtoMapper.toPo(cgwDeltaOcrBillResParam);
        Page<CgwDeltaOcrBillRes> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(cgwDeltaOcrBillRes));
        List<CgwDeltaOcrBillResDto> cgwDeltaOcrBillResDtos = page.getResult().stream().map(head -> {
            CgwDeltaOcrBillResDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CgwDeltaOcrBillResDto>> paged = ResultObject.createInstance(cgwDeltaOcrBillResDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param cgwDeltaOcrBillResParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CgwDeltaOcrBillResDto insert(CgwDeltaOcrBillResParam cgwDeltaOcrBillResParam, UserInfoToken userInfo) {
        CgwDeltaOcrBillRes billRes = dtoMapper.toPo(cgwDeltaOcrBillResParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        billRes.setSid(sid);
        billRes.setInsertUser(userInfo.getUserNo());
        billRes.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(billRes);
        return insertStatus > 0 ? dtoMapper.toDto(billRes) : null;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param queryParam
     * @param userInfo
     * @return
     */
    public List<CgwDeltaOcrBillResDto> selectAll(CgwDeltaOcrBillResParam queryParam, UserInfoToken userInfo) {
        CgwDeltaOcrBillRes billRes = dtoMapper.toPo(queryParam);
        billRes.setTradeCode(userInfo.getCompany());
        List<CgwDeltaOcrBillResDto> cgwDeltaOcrBillResDtos = new ArrayList<>();
        List<CgwDeltaOcrBillRes> cgwDeltaOcrBillRess = mapper.getList(billRes);
        if (CollectionUtils.isNotEmpty(cgwDeltaOcrBillRess)) {
            cgwDeltaOcrBillResDtos = cgwDeltaOcrBillRess.stream().map(head -> {
                CgwDeltaOcrBillResDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        // 设置loc
        CgwDeltaOcrBillLoc loc = locMapper.selectByPrimaryKey(queryParam.getHeadId());

        cgwDeltaOcrBillResDtos.stream().forEach(dto -> {
            try {
                Field f = CgwDeltaOcrBillLoc.class.getDeclaredField(dto.getFieldName()+"Loc");
                f.setAccessible(true);
                String fieldLoc = f.get(loc).toString();
                dto.setLoc(fieldLoc);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
        });
        return cgwDeltaOcrBillResDtos;
    }

    @SneakyThrows
    public void insertResRecord(String fieldName, Boolean compareResult,CgwDeltaOcrBill bill, CgwDeltaOcrBooking book,  UserInfoToken token) {
        CgwDeltaOcrBillRes res = new CgwDeltaOcrBillRes().initData(token);
        res.setSid(UUID.randomUUID().toString());
        res.setFieldName(fieldName);
        res.setHeadId(bill.getSid());
        // billValue
        Field billField = CgwDeltaOcrBill.class.getDeclaredField(fieldName);
        billField.setAccessible(true);
        res.setBillValue(billField.get(bill).toString());
        // bookingValue
        Field bookingField = CgwDeltaOcrBooking.class.getDeclaredField(fieldName);
        bookingField.setAccessible(true);
        res.setBookingValue(bookingField.get(book).toString());

        res.setCompareResult(compareResult? CgwDeltaOcrBillRes.COMPARE_RESULT_TRUE : CgwDeltaOcrBillRes.COMPARE_RESULT_FALSE);

        mapper.insert(res);
    }


    public Boolean deleteByHeadId(String headId, UserInfoToken token) {
        Example example = new Example(CgwDeltaOcrBillRes.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andEqualTo("headId", headId);

        int cnt = mapper.deleteByExample(example);
        return cnt > 0;
    }
}
