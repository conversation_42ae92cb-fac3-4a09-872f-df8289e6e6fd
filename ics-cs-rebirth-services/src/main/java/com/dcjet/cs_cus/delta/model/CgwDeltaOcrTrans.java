package com.dcjet.cs_cus.delta.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Setter
@Getter
@Table(name = "t_cgw_delta_ocr_trans")
public class CgwDeltaOcrTrans implements Serializable {
    private static final long serialVersionUID = 1L;

    public static String FIELD_TYPE_CHAR = "0";
	public static String FIELD_TYPE_DIGITAL = "1";

	/**
     * 主键
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 转换前的值
     */
	@Column(name = "before_convert")
	private  String beforeConvert;
	/**
     * 转换前的值
     */
	@Column(name = "after_convert")
	private  String afterConvert;
	/**
     * 所需要影响的字段类型(0.字符 1.数值)
     */
	@Column(name = "field_type")
	private  String fieldType;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;

	/**
	 * 转换
	 * @param str
	 * @return
	 */
	private String convert(String str) {
		return str.replace(this.beforeConvert, this.afterConvert);
	}
}
