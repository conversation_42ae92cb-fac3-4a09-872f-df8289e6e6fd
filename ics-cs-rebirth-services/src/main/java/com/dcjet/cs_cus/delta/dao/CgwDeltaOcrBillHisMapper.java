package com.dcjet.cs_cus.delta.dao;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillHis;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* CgwDeltaOcrBillHis
* <AUTHOR>
* @date: 2021-11-25
*/
public interface CgwDeltaOcrBillHisMapper extends Mapper<CgwDeltaOcrBillHis> {
    /**
     * 查询获取数据
     * @param cgwDeltaOcrBillHis
     * @return
     */
    List<CgwDeltaOcrBillHis> getList(CgwDeltaOcrBillHis cgwDeltaOcrBillHis);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
