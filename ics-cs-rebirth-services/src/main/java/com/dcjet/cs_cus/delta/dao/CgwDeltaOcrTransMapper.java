package com.dcjet.cs_cus.delta.dao;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrTrans;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* CgwDeltaOcrTrans
* <AUTHOR>
* @date: 2021-11-25
*/
public interface CgwDeltaOcrTransMapper extends Mapper<CgwDeltaOcrTrans> {
    /**
     * 查询获取数据
     * @param cgwDeltaOcrTrans
     * @return
     */
    List<CgwDeltaOcrTrans> getList(CgwDeltaOcrTrans cgwDeltaOcrTrans);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
