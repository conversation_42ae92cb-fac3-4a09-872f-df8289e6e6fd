package com.dcjet.cs_cus.delta.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import java.util.UUID;

import com.dcjet.cs.ocrInvoice.model.GwOcrLog;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrTDData;
import com.dcjet.cs_cus.delta.model.email.DeltaOcrTDData;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Setter
@Getter
@Table(name = "t_cgw_delta_ocr_bill")
public class CgwDeltaOcrBill implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * EN号码
     */
    @Column(name = "en_no")
    private String enNo;
    /**
     * SHIP TO
     */
    @Column(name = "ship_to")
    private String shipTo;
    /**
     * 收货人
     */
    @Column(name = "consignee")
    private String consignee;
    /**
     * 通知人
     */
    @Column(name = "notify_party")
    private String notifyParty;
    /**
     * 启运港
     */
    @Column(name = "desp_port")
    private String despPort;
    /**
     * 标记唛码
     */
    @Column(name = "mark_no")
    private String markNo;
    /**
     * 总毛重
     */
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * 总体积
     */
    @Column(name = "volume")
    private BigDecimal volume;
    /**
     * 外包装数量
     */
    @Column(name = "outer_packaging")
    private String outerPackaging;
    /**
     * 主要品名
     */
    @Column(name = "g_name")
    @JsonProperty("gName")
    private String gName;
    /**
     * 接收记录表头表中的SID
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * 数据状态(1.提单已识别 2.托书已获取 3.托书获取失败 4.比对成功 5.比对失败)
     */
    @Column(name = "status")
    private String status;
    /**
     * 比对完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "compare_time")
    private Date compareTime;
    /**
     * 比对结果(0.未比对 1.一致 2.不一致)
     */
    @Column(name = "compare_result")
    private String compareResult;
    /**
     * 托书获取时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "booking_get_time")
    private Date bookingGetTime;
    /**
     * 邮件通知状态(0.未通知 1.已通知)
     */
    @Column(name = "mail_send_flag")
    private String mailSendFlag;
    /**
     * 邮件发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "mail_send_time")
    private Date mailSendTime;
    /**
     * 提单识别时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "scan_time")
    private Date scanTime;
    /**
     * 提单识别时间-开始
     */
    @Transient
    private String scanTimeFrom;
    /**
     * 提单识别时间-结束
     */
    @Transient
    private String scanTimeTo;
    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;


    public CgwDeltaOcrBill initData(String sid, UserInfoToken token) {

        this.sid = sid;
        this.tradeCode = token.getCompany();
        this.insertUser = token.getUserNo();
        this.insertUserName = token.getUserName();
        this.insertTime = new Date();

        this.scanTime = this.insertTime;

        this.status = "1"; // 1 提单已识别
        this.compareResult = "0"; // 0 未比对
        this.mailSendFlag = "0"; // 0 未通知
        return this;

    }
}
