package com.dcjet.cs_cus.delta.model.email;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("KafkaAttach")
@Data
public class DeltaOcrEmialAttach {

    @ApiModelProperty("文件名称")
    String fileName;
    @ApiModelProperty("文件类型，本地文件或Obs")
    String fileType;
    @ApiModelProperty("路径，本地文件为文件夹路径，Obs是直接下载地址，时效为24小时")
    String fileUri;
    @ApiModelProperty("Oci的id标识")
    String recordId;
    @ApiModelProperty("Oci是否请求成功")
    boolean ociReturnType;
    @ApiModelProperty("oci返回数据结果")
    String ociReturnData;
    @ApiModelProperty("如果请求错误，此处返回请求错误内容")
    String callResult;
}
