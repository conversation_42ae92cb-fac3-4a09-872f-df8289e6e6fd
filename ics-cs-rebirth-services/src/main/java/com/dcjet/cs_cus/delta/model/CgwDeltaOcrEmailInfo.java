package com.dcjet.cs_cus.delta.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2022-1-4
 */
@Setter
@Getter
@Table(name = "t_cgw_delta_ocr_email_info")
public class CgwDeltaOcrEmailInfo implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键（同OCR提单ID）
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 发件人
     */
	@Column(name = "email_from")
	private  String emailFrom;
	/**
     * 收件人
     */
	@Column(name = "email_to")
	private  String emailTo;
	/**
     * 抄送人
     */
	@Column(name = "email_cc")
	private  String emailCc;
	/**
     * 收件标识， 如GW
     */
	@Column(name = "send_mail_identify")
	private  String sendMailIdentify;
	/**
     * 文件名称
     */
	@Column(name = "file_name")
	private  String fileName;
	/**
     * 文件类型，本地文件或Obs
     */
	@Column(name = "file_type")
	private  String fileType;
	/**
     * 文件路径
     */
	@Column(name = "file_uri")
	private  String fileUri;
	/**
     * Oci的id标识
     */
	@Column(name = "record_id")
	private  String recordId;
	/**
     * 企业十位编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 插入时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 插入人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 插入人姓名
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;

	public CgwDeltaOcrEmailInfo initData(String sid, UserInfoToken token) {
		this.setSid(sid);
		this.tradeCode = token.getCompany();
		this.insertUser = token.getUserNo();
		this.insertUserName = token.getUserName();
		this.insertTime = new Date();

		return this;
	}
}
