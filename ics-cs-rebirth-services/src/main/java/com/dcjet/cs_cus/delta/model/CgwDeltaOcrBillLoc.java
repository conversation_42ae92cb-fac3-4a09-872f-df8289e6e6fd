package com.dcjet.cs_cus.delta.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;

import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHeadLoc;
import com.dcjet.cs.ocrInvoice.model.GwOcrLog;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrTDData;
import com.dcjet.cs_cus.delta.model.email.DeltaOcrTDData;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Setter
@Getter
@Table(name = "t_cgw_delta_ocr_bill_loc")
public class CgwDeltaOcrBillLoc implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 对应中达OCR提单表头SID
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * EN号码
     */
	@Column(name = "en_no_loc")
	private  String enNoLoc;
	/**
     * SHIP TO
     */
	@Column(name = "ship_to_loc")
	private  String shipToLoc;
	/**
     * 收货人
     */
	@Column(name = "consignee_loc")
	private  String consigneeLoc;
	/**
     * 通知人
     */
	@Column(name = "notify_party_loc")
	private  String notifyPartyLoc;
	/**
     * 启运港
     */
	@Column(name = "desp_port_loc")
	private  String despPortLoc;
	/**
     * 标记唛码
     */
	@Column(name = "mark_no_loc")
	private  String markNoLoc;
	/**
     * 总毛重
     */
	@Column(name = "gross_wt_loc")
	private  String grossWtLoc;
	/**
     * 总体积
     */
	@Column(name = "volume_loc")
	private  String volumeLoc;
	/**
     * 外包装数量
     */
	@Column(name = "outer_packaging_loc")
	private  String outerPackagingLoc;
	/**
     * 主要品名
     */
	@Column(name = "g_name_loc")
	@JsonProperty("gNameLoc")
	private  String gNameLoc;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;

	public CgwDeltaOcrBillLoc initData(String sid, UserInfoToken token) {
		this.setSid(sid);
		this.tradeCode = token.getCompany();
		this.insertUser = token.getUserNo();
		this.insertUserName = token.getUserName();
		this.insertTime = new Date();

		return this;
	}

	public void assembly(DeltaOcrTDData ocrTDData) {
		// todo enNo ???
		if (ocrTDData.getTDMAWB() != null) {
			this.setEnNoLoc(ocrTDData.getTDMAWB().toJson());
		}
		// todo ship_to ???
		if (ocrTDData.getTDSHIPPER() != null) {
			this.setShipToLoc(ocrTDData.getTDSHIPPER().toJson());
		}
		if (ocrTDData.getTDCONSIGNEE() != null) {
			this.setConsigneeLoc(ocrTDData.getTDCONSIGNEE().toJson());
		}
		if (ocrTDData.getTDNOTIFY() != null) {
			this.setNotifyPartyLoc(ocrTDData.getTDNOTIFY().toJson());
		}
		if (ocrTDData.getTDDESPORT() != null) {
			this.setDespPortLoc(ocrTDData.getTDDESPORT().toJson());
		}
		if (ocrTDData.getTDMARKS() != null) {
			this.setMarkNoLoc(ocrTDData.getTDMARKS().toJson());
		}
		if (ocrTDData.getTDTOTAL_GW() != null) {
			this.setGrossWtLoc(ocrTDData.getTDTOTAL_GW().toJson());
		}
		if (ocrTDData.getTDTOTAL_MEAS() != null) {
			this.setVolumeLoc(ocrTDData.getTDTOTAL_MEAS().toJson());
		}
		// todo gName ???
//        this.setGName();
		// todo outerPackaging ???
//        this.setOuterPackaging();


	}
}
