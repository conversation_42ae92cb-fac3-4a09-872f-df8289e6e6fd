package com.dcjet.cs_cus.delta.mapper;
import com.dcjet.cs.dto_cus.delta.*;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBill;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CgwDeltaOcrBillDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CgwDeltaOcrBillDto toDto(CgwDeltaOcrBill po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CgwDeltaOcrBill toPo(CgwDeltaOcrBillParam param);
    /**
     * 数据库原始数据更新
     * @param cgwDeltaOcrBillParam
     * @param cgwDeltaOcrBill
     */
    void updatePo(CgwDeltaOcrBillParam cgwDeltaOcrBillParam, @MappingTarget CgwDeltaOcrBill cgwDeltaOcrBill);
    default void patchPo(CgwDeltaOcrBillParam cgwDeltaOcrBillParam, CgwDeltaOcrBill cgwDeltaOcrBill) {
        // TODO 自行实现局部更新
    }
}
