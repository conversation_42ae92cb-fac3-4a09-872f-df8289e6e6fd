package com.dcjet.cs_cus.delta.mapper;
import com.dcjet.cs.dto_cus.delta.*;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrReceLog;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-12-30
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CgwDeltaOcrReceLogDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CgwDeltaOcrReceLogDto toDto(CgwDeltaOcrReceLog po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CgwDeltaOcrReceLog toPo(CgwDeltaOcrReceLogParam param);
    /**
     * 数据库原始数据更新
     * @param cgwDeltaOcrReceLogParam
     * @param cgwDeltaOcrReceLog
     */
    void updatePo(CgwDeltaOcrReceLogParam cgwDeltaOcrReceLogParam, @MappingTarget CgwDeltaOcrReceLog cgwDeltaOcrReceLog);
    default void patchPo(CgwDeltaOcrReceLogParam cgwDeltaOcrReceLogParam, CgwDeltaOcrReceLog cgwDeltaOcrReceLog) {
        // TODO 自行实现局部更新
    }
}
