package com.dcjet.cs_cus.delta.component;

import com.dcjet.cs.util.emptyCheck.ElegantEmptyCheck;
import com.dcjet.cs.util.emptyCheck.EmptyHandlerType;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillResMapper;
import com.dcjet.cs_cus.delta.service.CgwDeltaOcrBillResService;
import com.dcjet.cs_cus.delta.service.CgwDeltaOcrTransService;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * OCR比对组件
 * <AUTHOR>
 * @date: 2021-11-29
 */
@Component
public class OcrCompareComponent {

    @Resource
    private CgwDeltaOcrTransService transService;

    /**
     * compareDigital
     * validScope 业务默认0.05
     * @param a
     * @param b
     * @param token
     * @return
     */
    @ElegantEmptyCheck(paramIndexs = {0, 1}, handlerType = EmptyHandlerType.FALSE_VALUE)
    public boolean compareDigital(String a, BigDecimal b, UserInfoToken token) {
        return compareDigital(a, b, BigDecimal.ZERO, token);
    }

    @ElegantEmptyCheck(paramIndexs = {0,1}, handlerType = EmptyHandlerType.FALSE_VALUE)
    public boolean compareDigital(String a, BigDecimal b, BigDecimal validScope, UserInfoToken token) {
        BigDecimal aConverted = transService.convertToDigital(a, token);
        if (aConverted == null) {
            return false;
        }
        return compareDigital(aConverted, b, validScope);
    }

    @ElegantEmptyCheck(paramIndexs = {0,1}, handlerType = EmptyHandlerType.FALSE_VALUE)
    public boolean compareDigital(BigDecimal a, BigDecimal b) {
        return compareDigital(a, b, BigDecimal.ZERO);
    }

    @ElegantEmptyCheck(paramIndexs = {0,1}, handlerType = EmptyHandlerType.FALSE_VALUE)
    public boolean compareDigital(BigDecimal a, BigDecimal b, BigDecimal validScope) {
        if (validScope == null) {
            validScope = BigDecimal.ZERO;
        }
        if (a.compareTo(b) == 0) {
            return true;
        }
        // 有效范围比较 ｜a-b｜ <= validScope * b
        if (Math.abs(a.subtract(b).doubleValue()) <= b.multiply(validScope).doubleValue()) {
            return true;
        }
        return false;
    }


    /**
     * compareString
     * @param a
     * @param b
     * @param token
     * @return compare result
     */
    @ElegantEmptyCheck(paramIndexs = {0,1}, handlerType = EmptyHandlerType.FALSE_VALUE)
    public boolean compareString(String a, String b, UserInfoToken token) {
        String aConverted = transService.convertToChar(a, token);
        String bConverted = transService.convertToChar(b, token);

        return aConverted.equals(bConverted);
    }

}
