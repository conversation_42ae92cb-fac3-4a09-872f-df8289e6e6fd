package com.dcjet.cs_cus.delta.dao;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrReceLog;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* CgwDeltaOcrReceLog
* <AUTHOR>
* @date: 2021-12-30
*/
public interface CgwDeltaOcrReceLogMapper extends Mapper<CgwDeltaOcrReceLog> {
    /**
     * 查询获取数据
     * @param cgwDeltaOcrReceLog
     * @return
     */
    List<CgwDeltaOcrReceLog> getList(CgwDeltaOcrReceLog cgwDeltaOcrReceLog);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
