package com.dcjet.cs_cus.delta.service;
import com.dcjet.cs_cus.delta.component.OcrIdentificationComponent;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrEmailInfoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrEmailInfo;
import com.xdo.common.exception.ArgumentException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto_cus.delta.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillMapper;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrBillDtoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBill;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Service
public class CgwDeltaOcrBillService extends BaseService<CgwDeltaOcrBill> {
    @Resource
    private CgwDeltaOcrBillMapper mapper;
    @Resource
    private CgwDeltaOcrBillDtoMapper dtoMapper;
    @Override
    public Mapper<CgwDeltaOcrBill> getMapper() {
        return mapper;
    }

    @Resource
    private CgwDeltaOcrBookingService bookingService;
    @Resource
    private OcrIdentificationComponent ocrIdentificationComponent;
    @Resource
    private CgwDeltaOcrEmailInfoMapper emailInfoMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param cgwDeltaOcrBillParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CgwDeltaOcrBillDto>> getListPaged(CgwDeltaOcrBillParam cgwDeltaOcrBillParam, PageParam pageParam) {
        // 启用分页查询
        CgwDeltaOcrBill cgwDeltaOcrBill = dtoMapper.toPo(cgwDeltaOcrBillParam);
        Page<CgwDeltaOcrBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(cgwDeltaOcrBill));
        List<CgwDeltaOcrBillDto> cgwDeltaOcrBillDtos = page.getResult().stream().map(head -> {
            CgwDeltaOcrBillDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CgwDeltaOcrBillDto>> paged = ResultObject.createInstance(cgwDeltaOcrBillDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CgwDeltaOcrBillDto> selectAll(CgwDeltaOcrBillParam exportParam, UserInfoToken userInfo) {
        CgwDeltaOcrBill cgwDeltaOcrBill = dtoMapper.toPo(exportParam);
        cgwDeltaOcrBill.setTradeCode(userInfo.getCompany());
        List<CgwDeltaOcrBillDto> cgwDeltaOcrBillDtos = new ArrayList<>();
        List<CgwDeltaOcrBill> cgwDeltaOcrBills = mapper.getList(cgwDeltaOcrBill);
        if (CollectionUtils.isNotEmpty(cgwDeltaOcrBills)) {
            cgwDeltaOcrBillDtos = cgwDeltaOcrBills.stream().map(head -> {
                CgwDeltaOcrBillDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return cgwDeltaOcrBillDtos;
    }


    /**
     * 批量获取托书
     * @param sids
     * @param token
     */
    public void batchObtainBooking(List<String> sids, UserInfoToken token) {
        List<CgwDeltaOcrBill> bills = getBillsByIds(sids, token);
        bills.stream().forEach(bill -> {
            bookingService.getBooking(bill.getEnNo(), token);
        });
    }

    /**
     * 批量比较
     * @param sids
     * @param token
     */
    public void batchCompare(List<String> sids, UserInfoToken token) {
        List<CgwDeltaOcrBill> bills = getBillsByIds(sids, token);
        bills.stream().forEach(bill -> {

            bookingService.compareBill(bill, token);
        });
    }

    /**
     * 邮件重发
     * @param sids
     * @param token
     */
    public void resendEmail(List<String> sids, UserInfoToken token) {
        List<CgwDeltaOcrBill> bills = getBillsByIds(sids, token);
        if (bills.stream().anyMatch(e-> !"4".equals(e.getStatus()) || !"2".equals(e.getCompareResult()))) {
            throw new ArgumentException(400, xdoi18n.XdoI18nUtil.t("只有“比对成功”且比对结果为“不一致”的数据可以重发！"));
        }
        bills.stream().forEach(bill -> {
            CgwDeltaOcrEmailInfo emailInfo = emailInfoMapper.selectByPrimaryKey(bill.getSid());
            if (emailInfo != null) {
                String subject = xdoi18n.XdoI18nUtil.t("EN号码") + bill.getEnNo() +  xdoi18n.XdoI18nUtil.t("不一致");
                String body = ocrIdentificationComponent.getEmailHtml(bill.getSid(), token);
                ocrIdentificationComponent.reSendMail(emailInfo, true, subject, body, token);
            }
        });
    }


    /**
     * 批量获取bill
     * @param sids
     * @param token
     * @return
     */
    private List<CgwDeltaOcrBill> getBillsByIds(List<String> sids, UserInfoToken token) {
        Example example = new Example(CgwDeltaOcrBill.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andIn("sid", sids);
        return mapper.selectByExample(example);
    }




}
