package com.dcjet.cs_cus.delta.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto_cus.delta.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrReceLogMapper;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrReceLogDtoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrReceLog;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-12-30
 */
@Service
public class CgwDeltaOcrReceLogService extends BaseService<CgwDeltaOcrReceLog> {
    @Resource
    private CgwDeltaOcrReceLogMapper mapper;
    @Resource
    private CgwDeltaOcrReceLogDtoMapper dtoMapper;
    @Override
    public Mapper<CgwDeltaOcrReceLog> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param cgwDeltaOcrReceLogParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CgwDeltaOcrReceLogDto>> getListPaged(CgwDeltaOcrReceLogParam cgwDeltaOcrReceLogParam, PageParam pageParam) {
        // 启用分页查询
        CgwDeltaOcrReceLog cgwDeltaOcrReceLog = dtoMapper.toPo(cgwDeltaOcrReceLogParam);
        Page<CgwDeltaOcrReceLog> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(cgwDeltaOcrReceLog));
        List<CgwDeltaOcrReceLogDto> cgwDeltaOcrReceLogDtos = page.getResult().stream().map(head -> {
            CgwDeltaOcrReceLogDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CgwDeltaOcrReceLogDto>> paged = ResultObject.createInstance(cgwDeltaOcrReceLogDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param cgwDeltaOcrReceLogParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CgwDeltaOcrReceLogDto insert(CgwDeltaOcrReceLogParam cgwDeltaOcrReceLogParam, UserInfoToken userInfo) {
        CgwDeltaOcrReceLog cgwDeltaOcrReceLog = dtoMapper.toPo(cgwDeltaOcrReceLogParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        cgwDeltaOcrReceLog.setSid(sid);
        cgwDeltaOcrReceLog.setInsertUser(userInfo.getUserNo());
        cgwDeltaOcrReceLog.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(cgwDeltaOcrReceLog);
        return  insertStatus > 0 ? dtoMapper.toDto(cgwDeltaOcrReceLog) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param cgwDeltaOcrReceLogParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CgwDeltaOcrReceLogDto update(CgwDeltaOcrReceLogParam cgwDeltaOcrReceLogParam, UserInfoToken userInfo) {
        CgwDeltaOcrReceLog cgwDeltaOcrReceLog = mapper.selectByPrimaryKey(cgwDeltaOcrReceLogParam.getSid());
        dtoMapper.updatePo(cgwDeltaOcrReceLogParam, cgwDeltaOcrReceLog);
        cgwDeltaOcrReceLog.setUpdateUser(userInfo.getUserNo());
        cgwDeltaOcrReceLog.setUpdateTime(new Date());
        // 更新数据
        int update = mapper.updateByPrimaryKey(cgwDeltaOcrReceLog);
        return update > 0 ? dtoMapper.toDto(cgwDeltaOcrReceLog) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CgwDeltaOcrReceLogDto> selectAll(CgwDeltaOcrReceLogParam exportParam, UserInfoToken userInfo) {
        CgwDeltaOcrReceLog cgwDeltaOcrReceLog = dtoMapper.toPo(exportParam);
        // cgwDeltaOcrReceLog.setTradeCode(userInfo.getCompany());
        List<CgwDeltaOcrReceLogDto> cgwDeltaOcrReceLogDtos = new ArrayList<>();
        List<CgwDeltaOcrReceLog> cgwDeltaOcrReceLogs = mapper.getList(cgwDeltaOcrReceLog);
        if (CollectionUtils.isNotEmpty(cgwDeltaOcrReceLogs)) {
            cgwDeltaOcrReceLogDtos = cgwDeltaOcrReceLogs.stream().map(head -> {
                CgwDeltaOcrReceLogDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return cgwDeltaOcrReceLogDtos;
    }

    public String insertReceLog(String btype, String data, UserInfoToken token) {
        CgwDeltaOcrReceLog receLog = new CgwDeltaOcrReceLog();
        receLog.setSid(UUID.randomUUID().toString());
        receLog.setReceiveData(data);
        receLog.setBussinessType(btype);
        receLog.setTradeCode(token.getCompany());
        receLog.setInsertTime(new Date());
        receLog.setInsertUser(token.getUserNo());
        receLog.setInsertUserName(token.getUserName());
        mapper.insert(receLog);

        return receLog.getSid();
    }
}
