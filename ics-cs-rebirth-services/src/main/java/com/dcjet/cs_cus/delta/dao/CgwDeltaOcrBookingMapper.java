package com.dcjet.cs_cus.delta.dao;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBooking;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* CgwDeltaOcrBooking
* <AUTHOR>
* @date: 2021-11-25
*/
public interface CgwDeltaOcrBookingMapper extends Mapper<CgwDeltaOcrBooking> {
    /**
     * 查询获取数据
     * @param cgwDeltaOcrBooking
     * @return
     */
    List<CgwDeltaOcrBooking> getList(CgwDeltaOcrBooking cgwDeltaOcrBooking);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
