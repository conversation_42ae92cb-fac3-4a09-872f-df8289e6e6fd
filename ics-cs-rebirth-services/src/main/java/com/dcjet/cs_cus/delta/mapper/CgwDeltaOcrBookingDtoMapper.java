package com.dcjet.cs_cus.delta.mapper;
import com.dcjet.cs.dto_cus.delta.*;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBooking;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CgwDeltaOcrBookingDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CgwDeltaOcrBookingDto toDto(CgwDeltaOcrBooking po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CgwDeltaOcrBooking toPo(CgwDeltaOcrBookingParam param);
    /**
     * 数据库原始数据更新
     * @param cgwDeltaOcrBookingParam
     * @param cgwDeltaOcrBooking
     */
    void updatePo(CgwDeltaOcrBookingParam cgwDeltaOcrBookingParam, @MappingTarget CgwDeltaOcrBooking cgwDeltaOcrBooking);
    default void patchPo(CgwDeltaOcrBookingParam cgwDeltaOcrBookingParam, CgwDeltaOcrBooking cgwDeltaOcrBooking) {
        // TODO 自行实现局部更新
    }
}
