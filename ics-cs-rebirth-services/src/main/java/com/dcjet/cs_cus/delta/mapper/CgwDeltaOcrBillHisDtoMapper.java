package com.dcjet.cs_cus.delta.mapper;
import com.dcjet.cs.dto_cus.delta.*;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBill;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillHis;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CgwDeltaOcrBillHisDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CgwDeltaOcrBillHisDto toDto(CgwDeltaOcrBillHis po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CgwDeltaOcrBillHis toPo(CgwDeltaOcrBillHisParam param);
    /**
     * 数据库原始数据更新
     * @param cgwDeltaOcrBillHisParam
     * @param cgwDeltaOcrBillHis
     */
    void updatePo(CgwDeltaOcrBillHisParam cgwDeltaOcrBillHisParam, @MappingTarget CgwDeltaOcrBillHis cgwDeltaOcrBillHis);
    default void patchPo(CgwDeltaOcrBillHisParam cgwDeltaOcrBillHisParam, CgwDeltaOcrBillHis cgwDeltaOcrBillHis) {
        // TODO 自行实现局部更新
    }

    void updateBill(CgwDeltaOcrBillHis cgwDeltaOcrBillHis, @MappingTarget CgwDeltaOcrBill cgwDeltaOcrBill);
}
