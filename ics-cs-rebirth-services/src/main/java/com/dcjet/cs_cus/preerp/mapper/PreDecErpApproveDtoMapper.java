package com.dcjet.cs_cus.preerp.mapper;

import com.dcjet.cs.dto_cus.preerp.PreDecErpApproveDto;
import com.dcjet.cs_cus.preerp.model.PreDecErpApprove;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PreDecErpApproveDtoMapper{


    PreDecErpApproveDto toDto(PreDecErpApprove po);
}
