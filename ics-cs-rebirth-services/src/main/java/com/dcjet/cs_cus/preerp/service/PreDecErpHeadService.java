package com.dcjet.cs_cus.preerp.service;

import com.dcjet.cs.base.service.ImportService;
import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.erp.DecInsertListI;
import com.dcjet.cs.dto.notice.NoticeTaskEventParam;
import com.dcjet.cs.dto_cus.preerp.*;
import com.dcjet.cs.erp.mapper.DecErpIHeadNDtoMapper;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.erp.service.DecErpIHeadNService;
import com.dcjet.cs.gwstd.dao.GwstdAgentConfigMapper;
import com.dcjet.cs.gwstd.model.GwstdAgentConfig;
import com.dcjet.cs.notice.service.event.NoticeTaskEvent;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ValidatorUtil;
import com.dcjet.cs_cus.preerp.dao.PreDecErpApproveMapper;
import com.dcjet.cs_cus.preerp.dao.PreDecErpContainerMapper;
import com.dcjet.cs_cus.preerp.dao.PreDecErpHeadMapper;
import com.dcjet.cs_cus.preerp.mapper.PreDecErpContainerDtoMapper;
import com.dcjet.cs_cus.preerp.mapper.PreDecErpHeadDtoMapper;
import com.dcjet.cs_cus.preerp.model.PreDecErpApprove;
import com.dcjet.cs_cus.preerp.model.PreDecErpContainer;
import com.dcjet.cs_cus.preerp.model.PreDecErpEvent;
import com.dcjet.cs_cus.preerp.model.PreDecErpHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.common.util.DateUtils;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@Service
public class PreDecErpHeadService implements ImportService<PreDecErpHeadSupplementImportParam> {

    @Resource
    private PreDecErpHeadMapper preDecErpHeadMapper;
    @Resource
    private PreDecErpContainerMapper preDecErpContainerMapper;
    @Resource
    private PreDecErpApproveMapper preDecErpApproveMapper;
    @Resource
    private PreDecErpHeadDtoMapper preDecErpHeadDtoMapper;
    @Resource
    private PreDecErpContainerDtoMapper preDecErpContainerDtoMapper;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private DecErpIHeadNService decErpIHeadNService;
    @Resource
    private DecErpIHeadNDtoMapper decErpIHeadNDtoMapper;
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private ValidatorUtil validatorUtil;
    /***
     * 根据id获取详情
     *
     * @param sid
     * @param token
     * @return
     */
    public PreDecErpHeadDto get(@Nonnull String sid, UserInfoToken token) {
        PreDecErpHead head = preDecErpHeadMapper.selectByPrimaryKey(sid);
        PreDecErpHeadDto dto = preDecErpHeadDtoMapper.toDto(head);
        if (dto != null) {
            PreDecErpContainer containerParam = new PreDecErpContainer();
            containerParam.setHeadId(sid);
            List<PreDecErpContainer> containerList = preDecErpContainerMapper.getList(containerParam);
            dto.setContainerList(containerList.stream().map(preDecErpContainerDtoMapper::toDto).collect(Collectors.toList()));
        }
        return dto;
    }

    /**
     * 获取分页信息 货代拉取数据, 货代也是登录账号，货代登录系统后查询数据
     *
     * <AUTHOR>
     * @param preDecErpHeadParam
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject<List<PreDecErpHeadDto>> getListPaged(PreDecErpHeadSearchParam preDecErpHeadParam, PageParam pageParam, UserInfoToken token) {
        // 启用分页查询
        PreDecErpHead preDecErpHead = preDecErpHeadDtoMapper.toPo(preDecErpHeadParam);
        preDecErpHead.setForwardCode(token.getCompany());
        return getByPage(preDecErpHead, pageParam);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param preDecErpHeadParam
     * @param userInfo
     * @return
     */
    public List<PreDecErpHeadDto> selectAll(PreDecErpHeadSearchParam preDecErpHeadParam, UserInfoToken userInfo) {
        PreDecErpHead preDecErpHead = preDecErpHeadDtoMapper.toPo(preDecErpHeadParam);
        preDecErpHead.setForwardCode(userInfo.getCompany());
        return getByListForward(preDecErpHead);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param preDecErpHeadParam
     * @param userInfo
     * @return
     */
    public List<PreDecErpHeadDto> gwSelectAll(PreDecErpHeadSearchParam preDecErpHeadParam, UserInfoToken userInfo) {
        PreDecErpHead preDecErpHead = preDecErpHeadDtoMapper.toPo(preDecErpHeadParam);
        preDecErpHead.setTradeCode(userInfo.getCompany());
        preDecErpHead.setStatusList(Arrays.asList(PreDecErpHead.STATUS_PRE_DISTRIBUTE, PreDecErpHead.STATUS_DISTRIBUTE, PreDecErpHead.STATE_DONE, PreDecErpHead.STATUS_BACK));
        return getByList(preDecErpHead);
    }

    /***
     * 克诺尔登录系统后查询数据
     *
     * @param preDecErpHeadParam
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject<List<PreDecErpHeadDto>> getGwList(PreDecErpHeadSearchParam preDecErpHeadParam, PageParam pageParam, UserInfoToken token) {
        // 启用分页查询
        PreDecErpHead preDecErpHead = preDecErpHeadDtoMapper.toPo(preDecErpHeadParam);
        // 关务进入后按照货主单位拉取数据
        preDecErpHead.setTradeCode(token.getCompany());
        preDecErpHead.setStatusList(Arrays.asList(PreDecErpHead.STATUS_PRE_DISTRIBUTE, PreDecErpHead.STATUS_DISTRIBUTE, PreDecErpHead.STATE_DONE, PreDecErpHead.STATUS_BACK));
        return getByPage(preDecErpHead, pageParam);
    }

    private ResultObject<List<PreDecErpHeadDto>> getByPage(PreDecErpHead queryParam, PageParam pageParam) {
        Page<PreDecErpHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> preDecErpHeadMapper.getList(queryParam));

        final List<PreDecErpHead> poList = page.getResult();
        if (poList.size() == 0) {
            return ResultObject.createInstance(new ArrayList<PreDecErpHeadDto>(),  (int) page.getTotal(), page.getPageNum());
        }
        final List<String> idList = poList.stream().map(PreDecErpHead::getSid).collect(Collectors.toList());
        final List<PreDecErpApprove> approveList = preDecErpApproveMapper.getLastNote(idList);
        final List<PreDecErpContainer> containers = preDecErpContainerMapper.getHeadIdList(idList);
        final Map<String, String> approveMap = approveList.stream().collect(Collectors.toMap(PreDecErpApprove::getHeadId, PreDecErpApprove::getNote, (a, b) -> a));
        final List<PreDecErpContainerDto> dtoContainerList = containers.stream().map(el -> {
            PreDecErpContainerDto dto = preDecErpContainerDtoMapper.toDto(el);
            return dto;
        }).collect(Collectors.toList());
        final List<PreDecErpHeadDto> dtoList = poList.stream().map(el -> {
            PreDecErpHeadDto dto = preDecErpHeadDtoMapper.toDto(el);
            if (approveMap.containsKey(el.getSid())) {
                dto.setApproveNote(approveMap.get(el.getSid()));
            }
            dto.setContainerList(dtoContainerList.stream().filter(x -> (dto.getSid().equals(x.getHeadId()))).collect(Collectors.toList()));
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<PreDecErpHeadDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    private List<PreDecErpHeadDto> getByList(PreDecErpHead queryParam) {
        List<PreDecErpHead> preDecErpHeads = preDecErpHeadMapper.getList(queryParam);
        if (preDecErpHeads.size() == 0) {
            return null;
        }
        final List<String> idList = preDecErpHeads.stream().map(PreDecErpHead::getSid).collect(Collectors.toList());
        final List<PreDecErpApprove> approveList = preDecErpApproveMapper.getLastNote(idList);
        final Map<String, String> approveMap = approveList.stream().collect(Collectors.toMap(PreDecErpApprove::getHeadId, PreDecErpApprove::getNote, (a, b) -> a));
        final List<PreDecErpHeadDto> dtoList = preDecErpHeads.stream().map(el -> {
            PreDecErpHeadDto dto = preDecErpHeadDtoMapper.toDto(el);
            if (approveMap.containsKey(el.getSid())) {
                dto.setApproveNote(approveMap.get(el.getSid()));
            }
            return dto;
        }).collect(Collectors.toList());
        return dtoList;
    }

    private List<PreDecErpHeadDto> getByListForward(PreDecErpHead queryParam) {
        List<PreDecErpHead> preDecErpHeads = preDecErpHeadMapper.getListForward(queryParam);
        if (preDecErpHeads.size() == 0) {
            return null;
        }
        final List<String> idList = preDecErpHeads.stream().map(PreDecErpHead::getSid).collect(Collectors.toList());
        final List<PreDecErpApprove> approveList = preDecErpApproveMapper.getLastNote(idList);
        final Map<String, String> approveMap = approveList.stream().collect(Collectors.toMap(PreDecErpApprove::getHeadId, PreDecErpApprove::getNote, (a, b) -> a));
        final List<PreDecErpHeadDto> dtoList = preDecErpHeads.stream().map(el -> {
            PreDecErpHeadDto dto = preDecErpHeadDtoMapper.toDto(el);
            if (approveMap.containsKey(el.getSid())) {
                dto.setApproveNote(approveMap.get(el.getSid()));
            }
            return dto;
        }).collect(Collectors.toList());
        return dtoList;
    }

    /**
     * 功能描述:新增
     *
     * @param preDecErpHeadParam
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PreDecErpHeadDto insert(PreDecErpHeadParam preDecErpHeadParam, UserInfoToken token) {
        PreDecErpHead head = preDecErpHeadDtoMapper.toPo(preDecErpHeadParam);
        PreDecErpHeadDto dto = insert(head, token);

        dto.setContainerList(saveContainer(head.getSid(), preDecErpHeadParam.getContainerList(), token));
        PreDecErpApprove approve = new PreDecErpApprove(head.getSid(), head.getStatus(), "新增", token);
        applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    public PreDecErpHeadDto copy(@Nonnull String sid, UserInfoToken token) {
        PreDecErpHead head = preDecErpHeadMapper.selectByPrimaryKey(sid);
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到需要复制的数据"));
        }
        String note = xdoi18n.XdoI18nUtil.t("复制编号") + head.getPreEmsListNo() + xdoi18n.XdoI18nUtil.t("的数据");

        PreDecErpHeadDto dto = insert(head, token);
        PreDecErpContainer sqlParam = new PreDecErpContainer();
        sqlParam.setForwardCode(token.getCompany());
        sqlParam.setHeadId(sid);
        List<PreDecErpContainer> containerList = preDecErpContainerMapper.getList(sqlParam);
        for (PreDecErpContainer preDecErpContainer : containerList) {
            preDecErpContainer.setHeadId(head.getSid());
            preDecErpContainer.preInsert(token);
            preDecErpContainerMapper.insert(preDecErpContainer);
        }

        PreDecErpApprove approve = new PreDecErpApprove(head.getSid(), head.getStatus(), note, token);
        applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));
        return dto;
    }

    /**
     * 功能描述:修改
     *
     * @param preDecErpHeadParam
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PreDecErpHeadDto update(@Nonnull String sid, PreDecErpHeadParam preDecErpHeadParam, UserInfoToken token) {
        PreDecErpHead head = preDecErpHeadMapper.selectByPrimaryKey(sid);
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择有效的数据"));
        }

        if (!head.canUpdate()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("只有暂存的数据才可以修改"));
        }

        preDecErpHeadDtoMapper.updatePo(preDecErpHeadParam, head);
        head.setStatus(PreDecErpHead.STATUS_TS);
        head.preUpdate(token);
        int update = preDecErpHeadMapper.updateByPrimaryKey(head);
        if (update > 0) {

            PreDecErpHeadDto dto = preDecErpHeadDtoMapper.toDto(head);

            preDecErpContainerMapper.deleteByHeadId(head.getSid());
            dto.setContainerList(saveContainer(head.getSid(), preDecErpHeadParam.getContainerList(), token));

            String note = xdoi18n.XdoI18nUtil.t("修改");
            PreDecErpApprove approve = new PreDecErpApprove(head.getSid(), head.getStatus(), note, token);
            applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));

            return dto;
        }
        return null;
    }


    /***
     * 允许多个同时提交，点击“提交预报单”，预报单状态变为“待分单”，只有暂存状态允许提交。
     * 其它状态下提交，提示：“当前状态不允许提交，只有暂存数据允许提交”预报单提交时校验必填字段，
     * 如果存在必填数据未维护数据。提示“存在必填项数据未维护。请维护数据”。
     * 如果运输工具为铁路运输或水路运输时，整箱/拼箱栏位必填。
     * 如果是整箱则，集装箱信息必须维护。预报单提交时弹出提示信息“如果提交，生成的预报单桨不允许删除。”
     *
     * @param idList
     * @param token
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(List<String> idList, UserInfoToken token) {
        List<PreDecErpHead> headList = preDecErpHeadMapper.getByIdList(idList);
        if (headList.size() == 0 || headList.size() != idList.size()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前id列表无法匹配数据！"));
        }
        headList.forEach(item -> {
            String errMsg = item.canSubmit();
            if (!Strings.isNullOrEmpty(errMsg)) {
                throw new ArgumentException(errMsg);
            }

            if (PCodeType.TRAF_MODE_WATERWAY.equals(item.getTrafMode()) ||
                    PCodeType.TRAF_MODE_RAILWAY.equals(item.getTrafMode())) {
                if (Strings.isNullOrEmpty(item.getContainerLcl())) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("运输工具为铁路运输或水路运输时，整箱/拼箱栏位必填。"));
                }

                if (PreDecErpHead.CONTAINER_FCL.equals(item.getContainerLcl())) {
                    PreDecErpContainer sqlParam = new PreDecErpContainer();
                    sqlParam.setForwardCode(token.getCompany());
                    sqlParam.setHeadId(item.getSid());
                    List<PreDecErpContainer> containerList = preDecErpContainerMapper.getList(sqlParam);
                    if (containerList.size() == 0) {
                        throw new ArgumentException(xdoi18n.XdoI18nUtil.t("整箱时，集装箱信息必须维护。"));
                    }

                    if (!Strings.isNullOrEmpty(validatorUtil.validationList(containerList))) {
                        throw new ArgumentException(xdoi18n.XdoI18nUtil.t("集装箱型号、集装箱号、拼箱标识不能为空"));
                    }
                }
            }

            item.preSubmit(token);
            item.setPreCreateTime(new Date());
            preDecErpHeadMapper.updateByPrimaryKey(item);

            String note = xdoi18n.XdoI18nUtil.t("提交预分单");
            PreDecErpApprove approve = new PreDecErpApprove(item.getSid(), item.getStatus(), note, token);
            applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void back(List<String> idList, PreDecErpHeadBackParam param, UserInfoToken token) {
        List<PreDecErpHead> headList = preDecErpHeadMapper.getByIdList(idList);
        if (headList.size() == 0 || headList.size() != idList.size()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前id列表无法匹配数据！"));
        }
        headList.forEach(item -> {
            if (!item.cnaBack()) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("只有提交预分单的数据才能退回，请重新提交"));
            }

            item.preBack(token);
            preDecErpHeadMapper.updateByPrimaryKey(item);

            String note = param.getNote();
            PreDecErpApprove approve = new PreDecErpApprove(item.getSid(), item.getStatus(), note, token);
            applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void distribute(List<String> idList, @Valid PreDecErpHeadDistributeParam param, UserInfoToken token) {
        List<PreDecErpHead> headList = preDecErpHeadMapper.getByIdList(idList);
        if (headList.size() == 0 || headList.size() != idList.size()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前id列表无法匹配数据！"));
        }

        headList.forEach(item -> {
            if (!item.canDistribute()) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("只有提交预分单的数据才能分单，请重新提交"));
            }
            item.setCustomerCode(param.getCustomerCode());
            item.preDistribute(token);

            DecInsertListI decInsertListI = preDecErpHeadDtoMapper.toDec(item);

            DecErpIHeadN decErpIHeadN = decErpIHeadNDtoMapper.toPo(decInsertListI.getDecErpIHeadNParam());
            decErpIHeadN.setDataSource(CommonEnum.dataSourceEnum.SOURCE_8.getCode());
            ResultObject<DecErpIHeadN> result = null;
            try {
                result = decErpIHeadNService.insertAll(decErpIHeadN,null,false, token,false,null,decInsertListI.getAttachedHeadId(), null);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            if(result.isSuccess()) {
                preDecErpHeadMapper.updateByPrimaryKey(item);
                applicationContext.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam("DEC_I_SENDED", token.getCompany(), result.getData().getSid())));
            }else {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("分单失败！"));
            }

            String note = xdoi18n.XdoI18nUtil.t("分单");
            PreDecErpApprove approve = new PreDecErpApprove(item.getSid(), item.getStatus(), note, token);
            applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public PreDecErpHeadDto supplement(String sid, PreDecErpHeadSupplementParam param, UserInfoToken token) {
        PreDecErpHead head = preDecErpHeadMapper.selectByPrimaryKey(sid);
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择有效的数据"));
        }

        if (!head.canSupplement()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经预报完结的数据无法进行信息补充"));
        }

        head.setAtaDate(param.getAtaDate());
        head.setWrapType(param.getWrapType());
        head.preDone(token);

        preDecErpHeadMapper.updateByPrimaryKey(head);

        String note = xdoi18n.XdoI18nUtil.t("补充信息");
        PreDecErpApprove approve = new PreDecErpApprove(head.getSid(), head.getStatus(), note, token);
        applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));

        return preDecErpHeadDtoMapper.toDto(head);
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        List<PreDecErpHead> headList = preDecErpHeadMapper.getByIdList(sids);
        for (PreDecErpHead head : headList) {
           if (!head.canDelete()) {
               throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前状态不允许删除。只有暂存、退回状态允许删除。"));
           }
        }
		preDecErpHeadMapper.deleteBySids(sids, userInfo.getCompany());
    }


    @Override
    public ImportValidation<PreDecErpHeadSupplementImportParam> importValidation(ImportData<PreDecErpHeadSupplementImportParam> importData, ExcelImportParam param, UserInfoToken token) {
        List<PreDecErpHeadSupplementImportParam> correctList = importData.getCorrectData();

        List<String> preEmsListNoList = correctList.stream().map(it -> it.getPreEmsListNo()).collect(Collectors.toList());;
        List<PreDecErpHead> headList = preDecErpHeadMapper.selectByPreEmsListNoIn(preEmsListNoList, token.getCompany());
        Map<String, PreDecErpHead> map = headList.stream().collect(Collectors.toMap(it -> it.getPreEmsListNo(), Function.identity(), (oldVal, newVal) -> newVal));
        List<PreDecErpHeadSupplementImportParam> corrects = new ArrayList<>(correctList.size());
        List<PreDecErpHeadSupplementImportParam> errors = new ArrayList<>(correctList.size());
        for (PreDecErpHeadSupplementImportParam val : correctList) {
            if (!map.containsKey(val.getPreEmsListNo())) {
                val.setErrMsg(xdoi18n.XdoI18nUtil.t("预报单编号不存在"));
                errors.add(val);
                continue;
            }

            PreDecErpHead head = map.get(val.getPreEmsListNo());
            if (!head.canSupplement()) {
                val.setErrMsg(xdoi18n.XdoI18nUtil.t("已经预报完结的数据无法进行信息补充"));
                errors.add(val);
                continue;
            }

            corrects.add(val);
        }
        return new ImportValidation<>(corrects, errors);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importSave(ImportData<PreDecErpHeadSupplementImportParam> data, UserInfoToken token) {
        Map<String, PreDecErpHeadSupplementImportParam> map = data.getCorrectData().stream().collect(Collectors.toMap(it -> it.getPreEmsListNo(), Function.identity(), (oldVal, newVal) -> newVal));
        List<PreDecErpHead> headList = preDecErpHeadMapper.selectByPreEmsListNoIn(map.keySet(), token.getCompany());
        headList.forEach(it -> {
            if (map.containsKey(it.getPreEmsListNo())) {
                PreDecErpHeadSupplementImportParam param = map.get(it.getPreEmsListNo());
                it.setAtaDate(param.getAtaDate());
                it.setWrapType(param.getWrapType());
                it.preDone(token);
                preDecErpHeadMapper.updateByPrimaryKey(it);

                String note = xdoi18n.XdoI18nUtil.t("导入补充信息");
                PreDecErpApprove approve = new PreDecErpApprove(it.getSid(), it.getStatus(), note, token);
                applicationEventPublisher.publishEvent(new PreDecErpEvent(approve));
            }
        });

        return headList.size();
    }
    @Resource
    private GwstdAgentConfigMapper gwstdAgentConfigMapper;
    private PreDecErpHeadDto insert(@Nonnull PreDecErpHead head, UserInfoToken token) {
        // 当前登录的企业是货代企业
        // 这个值是个写死的值，还有新增的时候也是写死的
        String HardCode=token.getCompany();
        GwstdAgentConfig config = new GwstdAgentConfig();
        config.setPreDeclareCode(token.getCompany());
        config.setUserNo(token.getUserNo());
        List<GwstdAgentConfig> configList = gwstdAgentConfigMapper.getList(config);
        if(configList.size()>0)
        {
            HardCode=configList.get(0).getTradeCode();
        }
        // 预报单的数据目前为克诺尔
        head.setPreEmsListNo(getByPreEmsListNo(HardCode));
        head.preInsert(token,HardCode);

        if (existPreEmsListNo(head.getPreEmsListNo(), HardCode)) {
            head.setPreEmsListNo(getByPreEmsListNo(HardCode));
            if (existPreEmsListNo(head.getPreEmsListNo(), HardCode)) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前系统繁忙，无法生成预报单编号，请重试"));
            }
        }

        // 新增数据
        int insertStatus = preDecErpHeadMapper.insert(head);
        return  insertStatus > 0 ? preDecErpHeadDtoMapper.toDto(head) : null;
    }

    private List<PreDecErpContainerDto> saveContainer(String headId, List<PreDecErpContainerParam> containerParamList, UserInfoToken token) {
        if (containerParamList != null) {
            List<PreDecErpContainerDto> containerList = new ArrayList<>(containerParamList.size());
            String HardCode=token.getCompany();
            GwstdAgentConfig config = new GwstdAgentConfig();
            config.setPreDeclareCode(token.getCompany());
            config.setUserNo(token.getUserNo());
            List<GwstdAgentConfig> configList = gwstdAgentConfigMapper.getList(config);
            if(configList.size()>0)
            {
                HardCode=configList.get(0).getTradeCode();
            }
            int serialNo = 1;
            for (PreDecErpContainerParam item : containerParamList) {
                if (Strings.isNullOrEmpty(item.getContainerModel())) {
                    continue;
                }
                PreDecErpContainer container = new PreDecErpContainer();
                container.setHeadId(headId);
                container.setSerialNo(serialNo);
                preDecErpContainerDtoMapper.updatePo(item, container);
                container.preInsert(token,HardCode);
                preDecErpContainerMapper.insert(container);
                containerList.add(preDecErpContainerDtoMapper.toDto(container));
                serialNo++;
            }
            return containerList;
        }
        return null;
    }

    /***
     * 存在true
     *
     * @param preEmsListNo
     * @return
     */
    private boolean existPreEmsListNo(String preEmsListNo, String tradeCode) {
        PreDecErpHead param = new PreDecErpHead();
        param.setPreEmsListNo(preEmsListNo);
        param.setTradeCode(tradeCode);
        List<PreDecErpHead> headList = preDecErpHeadMapper.select(param);
        return headList.size() > 0;
    }

    private String getByPreEmsListNo(String tradeCode) {
        String prefix = String.format("Y%s", DateUtils.dateToString(new Date(), "yyyyMM"));
        Page<String> page = PageHelper.startPage(1, 1).doSelectPage(() -> preDecErpHeadMapper.getMaxPreEmsListNo(prefix, tradeCode));
        int serialNumber = 1;
        if (page.getResult().size() > 0) {
            serialNumber = Integer.parseInt(page.get(0).substring(7));
            serialNumber++;
        }
        return prefix + String.format("%04d", serialNumber);
    }

    /**
     * 校验分单号重复
     *
     * @param preDecErpHeadParam
     * @param userInfo
     * @return
     */
    public ResultObject<List<String>> upiqueCheck(PreDecErpHeadParam preDecErpHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "");

        List<String> checkResult = new ArrayList<>();
        if (StringUtils.isNotBlank(preDecErpHeadParam.getHawb())) {
            boolean hawb = hawbCheck(preDecErpHeadParam, userInfo);
            if (hawb) {
                checkResult.add(xdoi18n.XdoI18nUtil.t("分单号重复"));
            }
        }

        if (checkResult.size() > 0) {
            result.setData(checkResult);
        }
        return result;
    }

    /**
     * 统计分运单号
     *
     * @param preDecErpHeadParam
     * @param userInfo
     * @return
     */
    private boolean hawbCheck(PreDecErpHeadParam preDecErpHeadParam, UserInfoToken userInfo) {
        Integer sum = preDecErpHeadMapper.hawbCheck(preDecErpHeadParam.getSid(),preDecErpHeadParam.getHawb(),userInfo.getCompany());
        return sum >0;
    }

    public ResultObject<List<PreDecErpContainerDto>> getContainerlist(PreDecErpHeadSearchParam preDecErpHeadParam, PageParam pageParam, UserInfoToken token) {
        // 启用分页查询
        PreDecErpContainer preDecErpContainer = new PreDecErpContainer();
        preDecErpContainer.setPreEmsListNo(preDecErpHeadParam.getPreEmsListNo());
        // 关务进入后按照货主单位拉取数据
        preDecErpContainer.setTradeCode(token.getCompany());
        Page<PreDecErpContainer> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> preDecErpHeadMapper.getContainerList(preDecErpContainer));
        List<PreDecErpContainerDto> preDecErpHeadDtos = page.getResult().stream().map(head -> {
            return preDecErpContainerDtoMapper.toDto(head);
        }).collect(Collectors.toList());
        return (ResultObject<List<PreDecErpContainerDto>>) ResultObject.createInstance(preDecErpHeadDtos, (int) page.getTotal(), page.getPageNum());
    }

    public List<PreDecErpContainerDto> selectContainerAll(PreDecErpHeadSearchParam preDecErpHeadParam, UserInfoToken userInfo) {
        PreDecErpContainer preDecErpContainer = new PreDecErpContainer();
        preDecErpContainer.setPreEmsListNo(preDecErpHeadParam.getPreEmsListNo());
        // 关务进入后按照货主单位拉取数据
        preDecErpContainer.setTradeCode(userInfo.getCompany());

        List<PreDecErpContainerDto> preDecErpContainerDtos = new ArrayList<>();
        List<PreDecErpContainer> preDecErpContainers = preDecErpHeadMapper.getContainerList(preDecErpContainer);
        if (CollectionUtils.isNotEmpty(preDecErpContainers)) {
            preDecErpContainerDtos = preDecErpContainers.stream().map(head -> {
                return preDecErpContainerDtoMapper.toDto(head);
            }).collect(Collectors.toList());
        }
        return preDecErpContainerDtos;
    }
}
