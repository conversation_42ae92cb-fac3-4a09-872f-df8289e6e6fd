package com.dcjet.cs_cus.preerp.model;

import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.gwstd.dao.GwstdAgentConfigMapper;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Resource;
import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@Setter
@Getter
@Table(name = "t_pre_dec_erp_approve")
public class PreDecErpApprove extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 表头主键
     */
	@Column(name = "head_id")
	private  String headId;

	/**
	 * 货代企业编码
	 */
	@Column(name = "forward_code")
	private String forwardCode;
	/**
     * 审批状态
     */
	@Column(name = "status")
	private  String status;
	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;

	public PreDecErpApprove() {

	}

	public PreDecErpApprove(String headId, String status, String note, UserInfoToken token) {
		setHeadId(headId);
		setStatus(status);
		setNote(note);
		preInsert(token);
	}
	@Resource
	private GwstdAgentConfigMapper gwstdAgentConfigMapper;
	@Override
	public void preInsert(UserInfoToken token,String newTradeCode) {
		super.preInsert(token,newTradeCode);

		setForwardCode(token.getCompany());
	}
}
