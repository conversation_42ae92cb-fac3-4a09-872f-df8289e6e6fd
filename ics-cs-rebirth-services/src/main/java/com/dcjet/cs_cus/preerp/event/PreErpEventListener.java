package com.dcjet.cs_cus.preerp.event;

import com.dcjet.cs_cus.preerp.dao.PreDecErpApproveMapper;
import com.dcjet.cs_cus.preerp.model.PreDecErpEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PreErpEventListener {

    private static final Logger log = LoggerFactory.getLogger(PreErpEventListener.class);

    @Resource
    private PreDecErpApproveMapper preDecErpApproveMapper;


    @Async
    @EventListener(value = PreDecErpEvent.class)
    public void onApproveEvent(PreDecErpEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("监听到onApproveEvent事件 {}", event);
        }
        preDecErpApproveMapper.insert(event.getApprove());
    }
}

