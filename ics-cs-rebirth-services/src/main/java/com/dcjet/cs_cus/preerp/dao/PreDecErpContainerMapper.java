package com.dcjet.cs_cus.preerp.dao;

import com.dcjet.cs_cus.preerp.model.PreDecErpContainer;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* PreDecErpContainer
* <AUTHOR>
* @date: 2021-3-8
*/
public interface PreDecErpContainerMapper extends Mapper<PreDecErpContainer> {
    /**
     * 查询获取数据
     * @param preDecErpContainer
     * @return
     */
    List<PreDecErpContainer> getList(PreDecErpContainer preDecErpContainer);


    /***
     *
     * @param headId
     */
    void deleteByHeadId(@Param("headId") String headId);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);


    List<PreDecErpContainer> getHeadIdList(@Param("headIdList") List<String> headIdList);
}
