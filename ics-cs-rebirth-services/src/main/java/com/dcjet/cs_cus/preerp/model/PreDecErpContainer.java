package com.dcjet.cs_cus.preerp.model;

import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.gwstd.dao.GwstdAgentConfigMapper;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Resource;
import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate 神码
 *
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@Setter
@Getter
@Table(name = "t_pre_dec_erp_container")
public class PreDecErpContainer extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 货代企业编码
	 */
	@Column(name = "forward_code")
	private String forwardCode;


	/**
     * 表头主键
     */
	@Column(name = "head_id")
	private  String headId;

	/**
	 * 序号
	 */
	@Column(name = "serial_no")
	private Integer serialNo;

	/**
     * 集装箱型号
     */
	@Column(name = "container_model")
    @NotEmpty(message = "{集装箱型号不能为空}")
	private  String containerModel;
	/**
     * 集装箱号
     */
	@Column(name = "container_md")
    @NotEmpty(message = "{集装箱号不能为空}")
	private  String containerMd;

	/**
	 * 拼箱标识
	 */
	@Column(name = "CONTAINER_LCL")
	@NotEmpty(message = "{拼箱标识不能为空}")
	private  String containerLcl;

	/**
	 * 集装箱自重
	 */
	@Column(name = "CONTAINER_WT")
	private BigDecimal containerWt;

	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;

	@Resource
	private GwstdAgentConfigMapper gwstdAgentConfigMapper;
	@Override
	public void preInsert(UserInfoToken token,String newTradeCode) {
		super.preInsert(token,newTradeCode);

		setForwardCode(token.getCompany());
	}
	@Transient
	private  String preEmsListNo;
}
