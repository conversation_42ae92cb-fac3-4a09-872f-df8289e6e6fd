package com.dcjet.cs_cus.preerp.dao;

import com.dcjet.cs_cus.preerp.model.PreDecErpApprove;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* PreDecErpApprove
* <AUTHOR>
* @date: 2021-3-8
*/
public interface PreDecErpApproveMapper extends Mapper<PreDecErpApprove> {
    /**
     * 查询获取数据
     * @param preDecErpApprove
     * @return
     */
    List<PreDecErpApprove> getList(PreDecErpApprove preDecErpApprove);

    /****
     *
     * @return
     */
    List<PreDecErpApprove> getLastNote(@Param("headIdList") List<String> headIdList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
