package com.dcjet.cs_cus.preerp.mapper;

import com.dcjet.cs.dto_cus.preerp.PreDecErpContainerDto;
import com.dcjet.cs.dto_cus.preerp.PreDecErpContainerParam;
import com.dcjet.cs_cus.preerp.model.PreDecErpContainer;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PreDecErpContainerDtoMapper {


    PreDecErpContainerDto toDto(PreDecErpContainer preDecErpContainer);


    void updatePo(PreDecErpContainerParam item, @MappingTarget PreDecErpContainer container);
}
