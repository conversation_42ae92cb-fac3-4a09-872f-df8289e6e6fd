package com.dcjet.cs_cus.preerp.model;

import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.gwstd.dao.GwstdAgentConfigMapper;
import com.dcjet.cs.util.DateUtil;
import com.dcjet.cs.util.ValidatorUtil;
import com.dcjet.cs.util.bulkSql.SpringContextUtil;
import com.google.common.base.Strings;
import com.xdo.common.token.UserInfoToken;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Resource;
import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 *
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@Setter
@Getter
@Table(name = "t_pre_dec_erp_head")
public class PreDecErpHead extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

	/***
	 * 暂存
	 */
	public static final String STATUS_TS = "0";

	/***
	 * 待分单
	 */
	public static final String STATUS_PRE_DISTRIBUTE = "1";

	/**
	 * 已分单
	 */
	public static final String STATUS_DISTRIBUTE = "2";

	/***
	 * 预报单完结
	 */
	public static final String STATE_DONE = "3";

	/***
	 * 退回
	 */
	public static final String STATUS_BACK = "-1";

	/***
	 * 未受托
	 */
	public static final String ENTRUST_STATUS_NOT = "0";

	/***
	 * 已受托
	 */
	public static final String ENTRUST_STATUS_ACCEPTED = "1";


	/***
	 * 整箱
	 */
	public static final String CONTAINER_FCL = "0";

	/**
	 * 货代企业编码
	 */
	@Column(name = "forward_code")
	private String forwardCode;


	/**
	 * 货代企业名称
	 */
	@Column(name = "forward_name")
	private String forwardName;

	/**
     * 预报单编号
     */
	@Column(name = "pre_ems_list_no")
	private  String preEmsListNo;
	/**
     * 关务人员
     */
	@Column(name = "gw_user")
	private  String gwUser;
	/**
     * SA
     */
	@Column(name = "sa")
	private  String sa;
	/**
     * 境外发货人
     */
	@Column(name = "overseas_shipper")
	@NotEmpty(message = "{境外发货人不能为空}")
	private String overseasShipper;
	@Transient
	private String overseasShipperName;
	/**
     * 运输方式
     */
	@Column(name = "traf_mode")
    @NotEmpty(message = "{运输方式不能为空}")
	private  String trafMode;
	/**
     * 主运单号
     */
	@Column(name = "mawb")
	private  String mawb;
	/**
     * 分运单号
     */
	@Column(name = "hawb")
	@NotEmpty(message = "{分运单号不能为空}")
	private  String hawb;
	/**
     * 包装信息
     */
	@Column(name = "wrap_type")
	private  String wrapType;
	/**
     * 件数
     */
	@Column(name = "pack_num")
    @NotNull(message = "{件数不能为空}")
	private  BigDecimal packNum;
	/**
     * 计费重量
     */
	@Column(name = "weight")
	@NotNull(message = "{计费重量不能为空}")
	private  BigDecimal weight;
	/**
     * 体积
     */
	@Column(name = "volume")
    @NotNull(message = "{体积不能为空}")
	private BigDecimal volume;
	/**
     * 毛重（总毛重）
     */
	@Column(name = "gross_wt")
	@NotNull(message = "{毛重不能为空}")
	private  BigDecimal grossWt;
	/**
     * 净重（总净重）
     */
	@Column(name = "net_wt")
	private  BigDecimal netWt;

	/**
     * 报关行
	 * 脑子一抽，写成这个了，就这么用吧，在这个系统里面的这个地方，这个就是报关行
     */
	@Column(name = "customer_code")
	private  String customerCode;
	@Transient
	private  String customerName;
	/**
     * 提货日期
     */
	@Column(name = "delivery_date")
	private  Date deliveryDate;

	/**
     * ETD日期
     */
	@Column(name = "etd_date")
    @NotNull(message = "{ETD日期不能为空}")
	private  Date etdDate;

	/**
     * ETA日期
     */
	@Column(name = "eta_date")
	@NotNull(message = "{ETA日期不能为空}")
	private  Date etaDate;

	/**
     * ATA日期
     */
	@Column(name = "ata_date")
	private  Date ataDate;

	/**
     * 启运国
     */
	@Column(name = "trade_country")
    @NotEmpty(message = "{启运国不能为空}")
	private  String tradeCountry;
	/**
     * 运费
     */
	@Column(name = "fee_rate")
    @NotNull(message = "{运费不能为空}")
	private  BigDecimal feeRate;
	/**
     * 运费币制
     */
	@Column(name = "fee_curr")
	@NotEmpty(message = "{运费币制不能为空}")
	private  String feeCurr;
	/**
     * 杂费币制
     */
	@Column(name = "other_curr")
	@NotEmpty(message = "{杂费币制不能为空}")
	private  String otherCurr;
	/**
     * 杂费
     */
	@NotNull(message = "{杂费不能为空}")
	@Column(name = "other_rate")
	private  BigDecimal otherRate;
	/**
     * 启运港
     */
	@Column(name = "desp_port")
	@NotEmpty(message = "{启运港不能为空}")
	private  String despPort;
	/**
     * 经停港
     */
	@Column(name = "dest_port")
	@NotEmpty(message = "{经停港不能为空}")
	private  String destPort;
	/**
     * 入境口岸
     */
	@Column(name = "entry_port")
    @NotEmpty(message = "{入境口岸不能为空}")
	private  String entryPort;
	/**
     * 状态 0 暂存、-1 退回、1 预报单提交、2 关务分单、3 接收委托、8 预报单完结
     */
	@Column(name = "status")
	private  String status;

	/**
	 * 受托标记 0 未受托, 1 已受托 默认未受托
	 */
	@Column(name = "entrust_status")
	private  String entrustStatus;
	/**
     * 受托时间
     */
	@Column(name = "entrust_date")
	private Date entrustDate;

	/**
	 * 拼箱标识  0 整箱  1 拼箱
	 */
	@Column(name = "container_lcl")
	private  String containerLcl;

	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;

	/***
	 * 提交分单时间
	 */
	@Column(name = "submit_date")
	private Date submitDate;

	/***
	 * 分单时间
	 */
	@Column(name = "distribute_date")
	private Date distributeDate;


	@Column(name = "pre_create_time")
	private Date preCreateTime;

	@Transient
	private String preCreateTimeFrom;

	@Transient
	private String preCreateTimeTo;


	/**
	 * ETD日期-开始
	 */
	@Transient
	private Date etdDateFrom;
	/**
	 * ETD日期-结束
	 */
	@Transient
	private Date etdDateTo;

	/**
	 * 提货日期-开始
	 */
	@Transient
	private Date deliveryDateFrom;
	/**
	 * 提货日期-结束
	 */
	@Transient
	private Date deliveryDateTo;

	/**
	 * ETA日期-开始
	 */
	@Transient
	private Date etaDateFrom;
	/**
	 * ETA日期-结束
	 */
	@Transient
	private Date etaDateTo;

	/**
	 * ATA日期-开始
	 */
	@Transient
	private Date ataDateFrom;
	/**
	 * ATA日期-结束
	 */
	@Transient
	private Date ataDateTo;

	/**
	 * 受托时间-开始
	 */
	@Transient
	private Date entrustDateFrom;
	/**
	 * 受托时间-结束
	 */
	@Transient
	private Date entrustDateTo;

	/**
	 * 预报日期-开始
	 */
	@ApiModelProperty("预报日期-开始")
	@Transient
	private String insertTimeFrom;
	/**
	 * 预报日期-结束
	 */
	@Transient
	@ApiModelProperty("预报日期-结束")
	private String insertTimeTo;

	/***
	 * 提交分单时间
	 */
	@Transient
	private Date submitDateFrom;

	/***
	 * 提交分单时间
	 */
	@Transient
	private Date submitDateTo;

	public void setEntrustDateTo(Date entrustDateTo) {
		this.entrustDateTo = DateUtil.plusDay(entrustDate, 1);
	}

	public void setSubmitDateTo(Date submitDateTo) {
		this.submitDateTo = DateUtil.plusDay(submitDateTo, 1);
	}

	@Transient
	private List<String> statusList;

	@Resource
	private GwstdAgentConfigMapper gwstdAgentConfigMapper;

	@Override
	public void preInsert(UserInfoToken token,String newTradeCode) {
		super.preInsert(token,newTradeCode);

		setForwardCode(token.getCompany());
		setForwardName(token.getCompanyName());
		status = STATUS_TS;
		entrustStatus = ENTRUST_STATUS_NOT;
	}

	/***
	 * 提交分单
	 * @param token
	 */
	public void preSubmit(UserInfoToken token) {
		submitDate = new Date();
		status = PreDecErpHead.STATUS_PRE_DISTRIBUTE;
		preUpdate(token);
	}

	public void preDistribute(UserInfoToken token) {
		distributeDate = new Date();
		status = STATUS_DISTRIBUTE;
		gwUser = token.getUserName();
		preUpdate(token);
		if (canDone()) {
			preDone(token);
		}
	}

	/***
	 * 退回
	 *
	 * @param token
	 */
	public void preBack(UserInfoToken token) {
		status = STATUS_BACK;
		gwUser = token.getUserName();
		preUpdate(token);
	}

	public void preDone(UserInfoToken token) {
		if (canDone()) {
			status = STATE_DONE;
		}
		preUpdate(token);
	}

	/***
	 * 能否分单
	 *
	 * @return
	 */
	public boolean canDistribute() {
		return STATUS_PRE_DISTRIBUTE.equals(status);
	}

	/**
	 * 能否提交
	 * @return
	 */
	public String canSubmit() {
		if (!STATUS_TS.equals(status)) {
			return xdoi18n.XdoI18nUtil.t("当前状态不允许提交，只有暂存数据允许提交");
		}
		return ((ValidatorUtil)SpringContextUtil.getBean("validatorUtil")).validation(this);
		//return ValidatorUtil.validation(this);
	}

	/***
	 * 能否更新
	 *
	 * @return
	 */
	public boolean canUpdate() {
		return STATUS_TS.equals(status) || STATUS_BACK.equals(status);
	}

	/***
	 * 能补充信息
	 *
	 * @return
	 */
	public boolean canSupplement() {
		return !STATE_DONE.equals(status);
	}


	/***
	 * 能否退回
	 *
	 * @return
	 */
	public boolean cnaBack() {
		return STATUS_PRE_DISTRIBUTE.equals(status);
	}

	/***
	 * 能否删除
	 *
	 * @return
	 */
	public boolean canDelete() {
		return STATUS_TS.equals(status) || STATUS_BACK.equals(status);
	}

	/***
	 * 问否完结
	 *
	 * @return
	 */
	public boolean canDone() {
		if (ataDate == null) {
			return false;
		}

		if (Strings.isNullOrEmpty(wrapType)) {
			return false;
		}

		return STATUS_DISTRIBUTE.equals(status);
	}



}
