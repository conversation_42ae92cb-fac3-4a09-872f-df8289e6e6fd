package com.dcjet.cs_cus.preerp.service;

import com.dcjet.cs.dto_cus.preerp.PreDecErpApproveDto;
import com.dcjet.cs_cus.preerp.dao.PreDecErpApproveMapper;
import com.dcjet.cs_cus.preerp.mapper.PreDecErpApproveDtoMapper;
import com.dcjet.cs_cus.preerp.model.PreDecErpApprove;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PreDecErpApproveService {

    @Resource
    private PreDecErpApproveMapper preDecErpApproveMapper;

    @Resource
    private PreDecErpApproveDtoMapper preDecErpApproveDtoMapper;

    public List<PreDecErpApproveDto> list(String headId, UserInfoToken token) {
        PreDecErpApprove queryParam = new PreDecErpApprove();
        queryParam.setHeadId(headId);
        queryParam.setForwardCode(token.getCompany());
        List<PreDecErpApprove> approveList = preDecErpApproveMapper.getList(queryParam);
        return approveList.stream().map(preDecErpApproveDtoMapper::toDto).collect(Collectors.toList());
    }


}
