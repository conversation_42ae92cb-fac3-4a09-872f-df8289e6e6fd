package com.dcjet.cs_cus.preerp.dao;

import com.dcjet.cs_cus.preerp.model.PreDecErpContainer;
import com.dcjet.cs_cus.preerp.model.PreDecErpHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Collection;
import java.util.List;

/**
* generated by Generate 神码
* PreDecErpHead
* <AUTHOR>
* @date: 2021-3-8
*/
public interface PreDecErpHeadMapper extends Mapper<PreDecErpHead> {

    /**
     * 查询获取数据
     * @param preDecErpHead
     * @return
     */
    List<PreDecErpHead> getList(PreDecErpHead preDecErpHead);
    List<PreDecErpHead> getListForward(PreDecErpHead preDecErpHead);


    /***
     *
     * @param list
     * @param forwardCode
     * @return
     */
    List<PreDecErpHead> selectByPreEmsListNoIn(@Param("list") Collection<String> list, @Param("forwardCode") String forwardCode);


    /***
     *
     * @param idList
     * @return
     */
    List<PreDecErpHead> getByIdList(@Param("idList") List<String> idList);

    /***
     *
     * @param preEmsListNo
     * @return
     */
    List<String> getMaxPreEmsListNo(@Param("preEmsListNo") String preEmsListNo, @Param("tradeCode") String tradeCode);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("sids") List<String> sids, @Param("forwardCode") String forwardCode);

    /**
     * 统计分运单号
     * @param hawb
     * @param tradeCode
     * @return
     */
    Integer hawbCheck(@Param("sid")String sid,@Param("hawb")String hawb, @Param("tradeCode") String tradeCode);

    int updateStatusByPreEmsListNo(@Param("preEmsListNo")String preEmsListNo,@Param("status")String status, @Param("tradeCode") String tradeCode);

    List<PreDecErpContainer> getContainerList(PreDecErpContainer preDecErpContainer);

    int getContainerListCount(PreDecErpContainer preDecErpContainer);
}
