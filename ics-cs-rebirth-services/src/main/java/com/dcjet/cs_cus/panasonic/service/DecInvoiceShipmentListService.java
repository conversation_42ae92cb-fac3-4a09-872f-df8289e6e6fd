package com.dcjet.cs_cus.panasonic.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto_cus.panasonic.DecInvoiceShipmentListDto;
import com.dcjet.cs.erp.dao.DecErpIListNMapper;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: WJ
 * @createDate: 2020/7/10 15:08
 */
@Service
public class DecInvoiceShipmentListService {

    @Resource
    private DecErpIListNMapper decErpIListNMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    public ResultObject<List<DecInvoiceShipmentListDto>> getListPaged(String invoiceNo, PageParam pageParam, UserInfoToken userInfo) {
        Page<DecInvoiceShipmentListDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decErpIListNMapper.getShipmentList(invoiceNo, userInfo.getCompany()));

        List<DecInvoiceShipmentListDto> dtos = page.getResult().stream().map(dto -> {
            if (!Strings.isNullOrEmpty(dto.getTrafMode())) {
                dto.setTrafMode(pCodeHolder.getValue(PCodeType.TRANSF, dto.getTrafMode()));
            }
            if(!Strings.isNullOrEmpty(dto.getUnit())) {
                dto.setUnit(pCodeHolder.getValue(PCodeType.UNIT, dto.getUnit()));
            }
            if(!Strings.isNullOrEmpty(dto.getUnit1())) {
                dto.setUnit1(pCodeHolder.getValue(PCodeType.UNIT, dto.getUnit1()));
            }
            if (!Strings.isNullOrEmpty(dto.getOriginCountry())) {
                dto.setOriginCountry(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, dto.getOriginCountry()));
            }
            if (!Strings.isNullOrEmpty(dto.getBondMark())) {
                if (CommonEnum.BondMarkEnum.BOND.getCode().equals(dto.getBondMark())) {
                    dto.setBondMark("免");
                }
                if (CommonEnum.BondMarkEnum.NOBOND.getCode().equals(dto.getBondMark())) {
                    dto.setBondMark("课");
                }
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecInvoiceShipmentListDto>> paged = ResultObject.createInstance(dtos,
                (int) page.getTotal(), page.getPageNum());
        return paged;
    }
}
