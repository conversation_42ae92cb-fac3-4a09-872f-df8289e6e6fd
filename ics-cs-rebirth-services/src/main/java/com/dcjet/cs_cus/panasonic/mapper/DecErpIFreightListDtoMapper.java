package com.dcjet.cs_cus.panasonic.mapper;

import com.dcjet.cs.dto_cus.panasonic.DecErpIFreightListDto;
import com.dcjet.cs.dto_cus.panasonic.DecErpIFreightListParam;
import com.dcjet.cs_cus.panasonic.model.DecErpIFreightList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecErpIFreightListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecErpIFreightListDto toDto(DecErpIFreightList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecErpIFreightList toPo(DecErpIFreightListParam param);
    /**
     * 数据库原始数据更新
     * @param decErpIFreightListParam
     * @param decErpIFreightList
     */
    void updatePo(DecErpIFreightListParam decErpIFreightListParam, @MappingTarget DecErpIFreightList decErpIFreightList);
    default void patchPo(DecErpIFreightListParam decErpIFreightListParam, DecErpIFreightList decErpIFreightList) {
        // TODO 自行实现局部更新
    }
}
