package com.dcjet.cs_cus.panasonic.dao;

import com.dcjet.cs_cus.panasonic.model.DecErpIFreightList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* DecErpIFreightList
* <AUTHOR>
* @date: 2020-7-13
*/
public interface DecErpIFreightListMapper extends Mapper<DecErpIFreightList> {
    /**
     * 查询获取数据
     * @param decErpIFreightList
     * @return
     */
    List<DecErpIFreightList> getList(DecErpIFreightList decErpIFreightList);

}
