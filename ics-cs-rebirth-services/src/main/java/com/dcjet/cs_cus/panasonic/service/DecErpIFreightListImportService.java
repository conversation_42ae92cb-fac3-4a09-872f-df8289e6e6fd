package com.dcjet.cs_cus.panasonic.service;

import com.dcjet.cs_cus.panasonic.dao.DecErpIFreightListMapper;
import com.dcjet.cs_cus.panasonic.model.DecErpIFreightList;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: WJ
 * @createDate: 2020/7/13 17:12
 */
@Service
public class DecErpIFreightListImportService implements ImportHandlerInterface {

    @Resource
    private DecErpIFreightListMapper decErpIFreightListMapper;

    @Override
    public String getTaskCode() {
        return "I_FREIGHT";
    }

    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        if(mapObjectList.size() <= 0){
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();

        ExcelImportDto<DecErpIFreightList> dto = new ExcelImportDto<>();
        // 正确的个数
        int correctNumber = 0;
        // 错误的个数
        int wrongNumber = 0;
        // 用来存放正确的对象的
        List<DecErpIFreightList> correctList = new ArrayList<>();
        // 用来存放错误的对象
        List<DecErpIFreightList> wrongList = new ArrayList<>();
        for (int i = 0;i < objectList.size();i++) {
            DecErpIFreightList decErpIFreight = (DecErpIFreightList)objectList.get(i);
            Example ex = new Example(DecErpIFreightList.class);
            Example.Criteria criteria = ex.createCriteria();
            criteria.andEqualTo("tradeCode", tradeCode);
            criteria.andEqualTo("invoiceNo", decErpIFreight.getInvoiceNo());
            DecErpIFreightList decErpIFreightList = decErpIFreightListMapper.selectOneByExample(ex);
            if (null == decErpIFreightList) {
                XdoImportLogger.log("发票号[" + decErpIFreight.getInvoiceNo() + "]不存在");
                decErpIFreight.setTempRemark(xdoi18n.XdoI18nUtil.t("发票号不存在"));
                wrongList.add(decErpIFreight);
                wrongNumber++;
            } else {
                correctList.add(decErpIFreight);
                correctNumber++;
            }
        }
//        dto.setTempOwnerId(UUID.randomUUID().toString());
        dto.setCorrectList(correctList);
        dto.setCorrectNumber(correctNumber);
        dto.setWrongList(wrongList);
        dto.setWrongNumber(wrongNumber);
        excelImportDtoList.add(dto);

        return excelImportDtoList;
    }

    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));

        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();
        Date date = new Date();
        for (int i = 0;i < objectList.size();i++) {
            DecErpIFreightList decErpIFreightList = (DecErpIFreightList)objectList.get(i);
            decErpIFreightList.setUpdateUserName(insertUserName);
            decErpIFreightList.setUpdateUser(insertUser);
            decErpIFreightList.setUpdateTime(date);
            Example example = new Example(DecErpIFreightList.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("tradeCode", tradeCode);
            criteria.andEqualTo("invoiceNo", decErpIFreightList.getInvoiceNo());
            decErpIFreightListMapper.updateByExampleSelective(decErpIFreightList, example);
        }

        return resultObject;
    }
}
