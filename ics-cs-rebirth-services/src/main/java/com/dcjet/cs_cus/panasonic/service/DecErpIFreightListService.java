package com.dcjet.cs_cus.panasonic.service;

import com.dcjet.cs.dto_cus.panasonic.DecErpIFreightListDto;
import com.dcjet.cs.dto_cus.panasonic.DecErpIFreightListParam;
import com.dcjet.cs.erp.dao.DecErpIListNMapper;
import com.dcjet.cs_cus.panasonic.dao.DecErpIFreightListMapper;
import com.dcjet.cs_cus.panasonic.mapper.DecErpIFreightListDtoMapper;
import com.dcjet.cs_cus.panasonic.model.DecErpIFreightList;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-7-13
 */
@Service
public class DecErpIFreightListService extends BaseService<DecErpIFreightList> {

    @Resource
    private DecErpIFreightListMapper decErpIFreightListMapper;
    @Resource
    private DecErpIFreightListDtoMapper decErpIFreightListDtoMapper;
    @Override
    public Mapper<DecErpIFreightList> getMapper() {
        return decErpIFreightListMapper;
    }
    @Resource
    private DecErpIListNMapper decErpIListNMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decErpIFreightListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<DecErpIFreightListDto>> getListPaged(DecErpIFreightListParam decErpIFreightListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        decErpIFreightListParam.setTradeCode(userInfo.getCompany());
        DecErpIFreightList decErpIFreightList = decErpIFreightListDtoMapper.toPo(decErpIFreightListParam);
        List<DecErpIFreightList> page = decErpIFreightListMapper.getList(decErpIFreightList);
        List<DecErpIFreightListDto> dtos = new ArrayList<>();

        int pageNum = pageParam.getPage();
        int pageSize = pageParam.getLimit();
        if (CollectionUtils.isNotEmpty(page)) {
            List<DecErpIFreightListDto> decErpIFreightListDtos = page.stream().map(head -> {
                // 币制赋值
                setCurr(userInfo, head);

                // 判断发票号是否存在
                Example example = new Example(DecErpIFreightList.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("invoiceNo", head.getInvoiceNo());
                criteria.andEqualTo("tradeCode", userInfo.getCompany());
                DecErpIFreightList list = decErpIFreightListMapper.selectOneByExample(example);
                if (null == list) {
                    head.setSid(UUID.randomUUID().toString());
                    head.setTradeCode(userInfo.getCompany());
                    head.setTradeName(userInfo.getCompanyName());
                    head.setInsertTime(new Date());
                    head.setInsertUser(userInfo.getUserNo());
                    head.setInsertUserName(userInfo.getUserName());
                    decErpIFreightListMapper.insert(head);
                }

                DecErpIFreightListDto dto = decErpIFreightListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());

            if (page.size() > (pageNum-1)*pageSize) {
                if (page.size() > pageNum*pageSize) {
                    dtos.addAll(decErpIFreightListDtos.subList((pageNum-1)*pageSize, pageNum*pageSize));
                } else {
                    dtos.addAll(decErpIFreightListDtos.subList((pageNum-1)*pageSize, page.size()));
                }
            }
        }

        return ResultObject.createInstance(dtos, page.size(), pageNum);
    }

    /**
     * 功能描述:修改
     *
     * @param decErpIFreightListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpIFreightListDto update(DecErpIFreightListParam decErpIFreightListParam, UserInfoToken userInfo) {
        DecErpIFreightList decErpIFreightList =
                decErpIFreightListMapper.selectByPrimaryKey(decErpIFreightListParam.getSid());
        decErpIFreightListDtoMapper.updatePo(decErpIFreightListParam, decErpIFreightList);
        decErpIFreightList.setTradeCode(userInfo.getCompany());
        decErpIFreightList.setTradeName(userInfo.getCompanyName());
        decErpIFreightList.setUpdateUser(userInfo.getUserNo());
        decErpIFreightList.setUpdateUserName(userInfo.getUserName());
        decErpIFreightList.setUpdateTime(new Date());
        // 更新数据
        int update = decErpIFreightListMapper.updateByPrimaryKey(decErpIFreightList);
        return update > 0 ? decErpIFreightListDtoMapper.toDto(decErpIFreightList) : null;
    }


    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecErpIFreightListDto> selectAll(DecErpIFreightListParam exportParam, UserInfoToken userInfo) {
        DecErpIFreightList decErpIFreightList = decErpIFreightListDtoMapper.toPo(exportParam);
        decErpIFreightList.setTradeCode(userInfo.getCompany());
        List<DecErpIFreightListDto> decErpIFreightListDtos = new ArrayList<>();
        List<DecErpIFreightList> decErpIFreightLists = decErpIFreightListMapper.getList(decErpIFreightList);
        if (CollectionUtils.isNotEmpty(decErpIFreightLists)) {
            decErpIFreightListDtos = decErpIFreightLists.stream().map(head -> {
                setCurr(userInfo, head);
                DecErpIFreightListDto dto = decErpIFreightListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decErpIFreightListDtos;
    }

    private void setCurr(UserInfoToken userInfo, DecErpIFreightList head) {
        List<String> currs = decErpIListNMapper.getCurrListByInvoiceNo(head.getInvoiceNo(), userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(currs)) {
            if (currs.size() == 1) {
                head.setCurr(currs.get(0));
            }
        }
    }
}
