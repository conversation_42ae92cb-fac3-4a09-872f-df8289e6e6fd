package com.dcjet.cs_cus.panasonic.dao;

import com.dcjet.cs_cus.panasonic.model.DecErpEFreightList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* DecErpEFreightList
* <AUTHOR>
* @date: 2020-7-13
*/
public interface DecErpEFreightListMapper extends Mapper<DecErpEFreightList> {
    /**
     * 查询获取数据
     * @param decErpEFreightList
     * @return
     */
    List<DecErpEFreightList> getList(DecErpEFreightList decErpEFreightList);

}
