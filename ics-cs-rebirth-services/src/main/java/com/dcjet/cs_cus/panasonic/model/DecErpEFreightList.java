package com.dcjet.cs_cus.panasonic.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-7-13
 */
@Setter
@Getter
@Table(name = "T_DEC_ERP_E_FREIGHT_LIST")
public class DecErpEFreightList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 唯一键
	 */
	@Id
	@Column(name = "SID")
	private  String sid;
	/**
	 * 发票号码
	 */
	@Column(name = "INVOICE_NO")
	private  String invoiceNo;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 经营企业名称
     */
	@Column(name = "TRADE_NAME")
	private  String tradeName;

	/**
     * 总金额
     */
	@Transient
	private  BigDecimal sumDecTotal;
	/**
     * 币制
     */
	@Transient
	private  String curr;
	/**
     * 总毛重
     */
	@Transient
	private  BigDecimal grossWt;
	/**
     * 体积
     */
	@Transient
	private  BigDecimal volume;
	/**
     * 运输方式
     */
	@Transient
	private  String trafMode;
	/**
     * 成交方式
     */
	@Transient
	private  String transMode;
	/**
     * 集卡费
     */
	@Column(name = "CARD_COST")
	private  BigDecimal cardCost;
	/**
     * 报关费
     */
	@Column(name = "CUSTOMS_COST")
	private  BigDecimal customsCost;
	/**
     * 订舱费
     */
	@Column(name = "BOOKING_COST")
	private  BigDecimal bookingCost;
	/**
     * 操作费
     */
	@Column(name = "OPERATION_COST")
	private  BigDecimal operationCost;
	/**
     * 磁检费
     */
	@Column(name = "MAGNETIC_COST")
	private  BigDecimal magneticCost;
	/**
     * 查验费
     */
	@Column(name = "CHECK_COST")
	private  BigDecimal checkCost;
	/**
     * 其它物流费
     */
	@Column(name = "OTHER_LOGISTICS_COST")
	private  BigDecimal otherLogisticsCost;
	/**
     * 运费合计(CNY)
     */
	@Column(name = "FREIGHT_TOTAL")
	private  BigDecimal freightTotal;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 制单人姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 贸易条款
     */
	@Transient
	private  String tradeTerms;

	/**
	 * 导入错误原因
	 */
	@Transient
	private String tempRemark;
}
