package com.dcjet.cs_cus.panasonic.service;

import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListDto;
import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListParam;
import com.dcjet.cs.erp.dao.DecErpEListNMapper;
import com.dcjet.cs_cus.panasonic.dao.DecErpEFreightListMapper;
import com.dcjet.cs_cus.panasonic.mapper.DecErpEFreightListDtoMapper;
import com.dcjet.cs_cus.panasonic.model.DecErpEFreightList;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-7-13
 */
@Service
public class DecErpEFreightListService extends BaseService<DecErpEFreightList> {

    @Resource
    private DecErpEFreightListMapper decErpEFreightListMapper;
    @Resource
    private DecErpEFreightListDtoMapper decErpEFreightListDtoMapper;
    @Override
    public Mapper<DecErpEFreightList> getMapper() {
        return decErpEFreightListMapper;
    }
    @Resource
    private DecErpEListNMapper decErpEListNMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decErpEFreightListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<DecErpEFreightListDto>> getListPaged(DecErpEFreightListParam decErpEFreightListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        decErpEFreightListParam.setTradeCode(userInfo.getCompany());
        DecErpEFreightList decErpEFreightList = decErpEFreightListDtoMapper.toPo(decErpEFreightListParam);
        List<DecErpEFreightList> page = decErpEFreightListMapper.getList(decErpEFreightList);
        List<DecErpEFreightListDto> dtos = new ArrayList<>();

        int pageNum = pageParam.getPage();
        int pageSize = pageParam.getLimit();
        if (CollectionUtils.isNotEmpty(page)) {
            List<DecErpEFreightListDto> decErpEFreightListDtos = page.stream().map(
                head -> {
                    // 币制赋值
                    setCurr(userInfo, head);
                    // 判断发票号是否存在
                    Example example = new Example(DecErpEFreightList.class);
                    Example.Criteria criteria = example.createCriteria();
                    criteria.andEqualTo("invoiceNo", head.getInvoiceNo());
                    criteria.andEqualTo("tradeCode", userInfo.getCompany());
                    DecErpEFreightList list = decErpEFreightListMapper.selectOneByExample(example);
                    if (null == list) {
                        head.setSid(UUID.randomUUID().toString());
                        head.setTradeCode(userInfo.getCompany());
                        head.setTradeName(userInfo.getCompanyName());
                        head.setInsertTime(new Date());
                        head.setInsertUser(userInfo.getUserNo());
                        head.setInsertUserName(userInfo.getUserName());
                        decErpEFreightListMapper.insert(head);
                    }

                    DecErpEFreightListDto dto = decErpEFreightListDtoMapper.toDto(head);
                    return dto;
                })
            .collect(Collectors.toList());

            if (page.size() > (pageNum-1)*pageSize) {
                if (page.size() > pageNum*pageSize) {
                    dtos.addAll(decErpEFreightListDtos.subList((pageNum-1)*pageSize, pageNum*pageSize));
                } else {
                    dtos.addAll(decErpEFreightListDtos.subList((pageNum-1)*pageSize, page.size()));
                }
            }
        }

        return ResultObject.createInstance(dtos, page.size(), pageNum);
    }

    /**
     * 功能描述:修改
     *
     * @param decErpEFreightListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpEFreightListDto update(DecErpEFreightListParam decErpEFreightListParam, UserInfoToken userInfo) {
        DecErpEFreightList decErpEFreightList =
                decErpEFreightListMapper.selectByPrimaryKey(decErpEFreightListParam.getSid());
        decErpEFreightListDtoMapper.updatePo(decErpEFreightListParam, decErpEFreightList);
        decErpEFreightList.setTradeCode(userInfo.getCompany());
        decErpEFreightList.setTradeName(userInfo.getCompanyName());
        decErpEFreightList.setUpdateUser(userInfo.getUserNo());
        decErpEFreightList.setUpdateUserName(userInfo.getUserName());
        decErpEFreightList.setUpdateTime(new Date());
        // 更新数据
        int update = decErpEFreightListMapper.updateByPrimaryKey(decErpEFreightList);
        return update > 0 ? decErpEFreightListDtoMapper.toDto(decErpEFreightList) : null;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecErpEFreightListDto> selectAll(DecErpEFreightListParam exportParam, UserInfoToken userInfo) {
        DecErpEFreightList decErpEFreightList = decErpEFreightListDtoMapper.toPo(exportParam);
        decErpEFreightList.setTradeCode(userInfo.getCompany());
        List<DecErpEFreightListDto> decErpEFreightListDtos = new ArrayList<>();
        List<DecErpEFreightList> decErpEFreightLists = decErpEFreightListMapper.getList(decErpEFreightList);
        if (CollectionUtils.isNotEmpty(decErpEFreightLists)) {
            decErpEFreightListDtos = decErpEFreightLists.stream().map(head -> {
                setCurr(userInfo, head);
                DecErpEFreightListDto dto = decErpEFreightListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decErpEFreightListDtos;
    }

    private void setCurr(UserInfoToken userInfo, DecErpEFreightList head) {
        List<String> currs = decErpEListNMapper.getCurrListByInvoiceNo(head.getInvoiceNo(), userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(currs)) {
            if (currs.size() == 1) {
                head.setCurr(currs.get(0));
            }
        }
    }
}
