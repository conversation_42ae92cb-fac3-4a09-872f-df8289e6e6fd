package com.dcjet.cs_cus.optorun.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-9-1
 */
@Setter
@Getter
@Table(name = "T_CGW_OPTORUN_BALANCE_PRE_LIST")
public class BalancePreList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 预录入单表体sid
     */
	@Column(name = "SID")
	private  String sid;
	/**
     * 备案号
     */
	@Column(name = "EMS_NO")
	private  String emsNo;
	/**
     * 提单内部编号
     */
	@Column(name = "EMS_LIST_NO")
	private  String emsListNo;
	/**
     * 企业料号
     */
	@Column(name = "FAC_G_NO")
	private  String facGNo;
	/**
     * 备案料号(取制单时的备案料号)
     */
	@Column(name = "COP_G_NO")
	private  String copGNo;
	/**
     * 保完税标识(0.保税 1.非保税)
     */
	@Column(name = "BOND_MARK")
	private  String bondMark;
	/**
     * 料件成品标记
     */
	@Column(name = "G_MARK")
	@JsonProperty("gMark")
	private  String gMark;
	/**
     * 申报数量
     */
	@Column(name = "QTY")
	private  BigDecimal qty;
	/**
     * 单耗版本号
     */
	@Column(name = "EXG_VERSION")
	private  String exgVersion;
	/**
     * 对应清单是否已删单(0.未删单 1.已删单)
     */
	@Column(name = "IS_DEL")
	private  String isDel;
	/**
     * 监管方式
     */
	@Column(name = "TRADE_MODE")
	private  String tradeMode;
	/**
     * 进出口方式(I.进口 E.出口)
     */
	@Column(name = "I_E_MARK")
	@JsonProperty("iEMark")
	private  String iEMark;
	/**
     * 提交计算的登录账号
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 提交计算时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 提交计算的姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
}
