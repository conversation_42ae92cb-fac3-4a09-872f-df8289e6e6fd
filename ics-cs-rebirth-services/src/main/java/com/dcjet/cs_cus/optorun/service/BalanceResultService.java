package com.dcjet.cs_cus.optorun.service;

import com.dcjet.cs.dto_cus.optorun.BalanceResultDto;
import com.dcjet.cs.dto_cus.optorun.BalanceResultParam;
import com.dcjet.cs.util.BatchUtil;
import com.dcjet.cs.util.LoggerUtil;
import com.dcjet.cs_cus.optorun.dao.BalancePreBomMapper;
import com.dcjet.cs_cus.optorun.dao.BalancePreListMapper;
import com.dcjet.cs_cus.optorun.dao.BalanceResultMapper;
import com.dcjet.cs_cus.optorun.mapper.BalanceResultDtoMapper;
import com.dcjet.cs_cus.optorun.model.BalancePreBom;
import com.dcjet.cs_cus.optorun.model.BalanceResult;
import com.dcjet.cs_cus.optorun.model.CalcBalanceParam;
import com.dcjet.cs_cus.optorun.model.MatInfoWithNoRecord;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-8-31
 */
@Service
//@Slf4j
public class BalanceResultService extends BaseService<BalanceResult> {

    private final Logger log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private BalanceResultMapper mapper;
    @Resource
    private BalanceResultDtoMapper dtoMapper;
    @Override
    public Mapper<BalanceResult> getMapper() {
        return mapper;
    }

    @Resource
    private BalancePreListMapper preListMapper;
    @Resource
    private BalancePreBomMapper preBomMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param balanceResultParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BalanceResultDto>> getListPaged(BalanceResultParam balanceResultParam, PageParam pageParam) {
        // 启用分页查询
        BalanceResult balanceResult = dtoMapper.toPo(balanceResultParam);
        Page<BalanceResult> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(balanceResult));
        List<BalanceResultDto> balanceResultDtos = page.getResult().stream().map(head -> {
            BalanceResultDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BalanceResultDto>> paged = ResultObject.createInstance(balanceResultDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BalanceResultDto> selectAll(BalanceResultParam exportParam, UserInfoToken userInfo) {
        BalanceResult balanceResult = dtoMapper.toPo(exportParam);
        // cgwOptorunBalanceResult.setTradeCode(userInfo.getCompany());
        List<BalanceResultDto> balanceResultDtos = new ArrayList<>();
        List<BalanceResult> balanceResults = mapper.getList(balanceResult);
        if (CollectionUtils.isNotEmpty(balanceResults)) {
            balanceResultDtos = balanceResults.stream().map(head -> {
                BalanceResultDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return balanceResultDtos;
    }

    /**
     *
     * @param param
     */
    public void clacBalance(CalcBalanceParam param) {
        // 数据初始化
        preBomMapper.initData(param);
        LoggerUtil.logInfo(log, param.getEmsNo() + "---BOM 初始化完毕---");

        preListMapper.initData(param);
        LoggerUtil.logInfo(log, param.getEmsNo() + "---LIST 初始化完毕---");

        // 折料 （ 单耗 = 净耗/(1-有形损耗-无形损耗) ）
        preListMapper.imgReturn(param);
        LoggerUtil.logInfo(log, param.getEmsNo() + "---LIST BOM折料完毕---");

        // 开始计算
        mapper.initData1(param);
        LoggerUtil.logInfo(log, param.getEmsNo() + "---RESULT 理论结余计算完成---");

        // !! 计算未出口成品 !!
        initDataFinally(param);
        LoggerUtil.logInfo(log, param.getEmsNo() + "---RESULT 未出口成品计算完成---");

        // 更新未进出口所有料件
        mapper.insertForImgWithNoRecord(param);
        LoggerUtil.logInfo(log, param.getEmsNo() + "---RESULT 未出口料件计算完成---");

        // 更新最终剩余
        mapper.clacRemainFinally(param);
        log.info(param.getEmsNo() + "---RESULT 最终剩余计算完毕。平衡表计算结束---");
    }


    /**
     * 初始化bom
     * @param param
     */
    public void initBomData(CalcBalanceParam param) {
        preBomMapper.initData(param);
    }

    /**
     * 初始化进出口数据
     * @param param
     */
    public void initPreListData(CalcBalanceParam param) {
        preListMapper.initData(param);
    }

    /**
     * 折料（进出口的成品）
     * @param param
     */
    public void imgReturn(CalcBalanceParam param) {
        preListMapper.imgReturn(param);
    }

    /**
     * 计算
     * @param param
     */
    public void initData1(CalcBalanceParam param) {
        mapper.initData1(param);
    }

    public void initDataFinally(CalcBalanceParam param) {

        // 获取未出口的所有成品
        List<MatInfoWithNoRecord> matsWithNoRecord = getExgInfoWithNoRecord(param);
        if(CollectionUtils.isEmpty(matsWithNoRecord)) {
            return;
        }

        List<String> facGNos = matsWithNoRecord.stream().map(MatInfoWithNoRecord::getFacGNo).collect(Collectors.toList());

        // 获取bom信息，一个成品有多个bom版本号，取最大bom版本
        List<BalancePreBom> maxBoms = getPreBomWithMaxExgVersion(facGNos, param);
        if(CollectionUtils.isEmpty(maxBoms)) {
            return;
        }

        // 插入结果表
        insertBalanceResultForNoRecord(matsWithNoRecord, maxBoms, param);
    }

    /**
     * 获取未出口的所有成品
     * @param param
     * @return
     */
    private List<MatInfoWithNoRecord> getExgInfoWithNoRecord(CalcBalanceParam param) {
        return mapper.getExgInfoWithNoRecord(param);
    }

    /**
     * 获取最大BOM
     * @param facGNos
     * @param param
     * @return
     */
    private List<BalancePreBom> getPreBomWithMaxExgVersion(List<String> facGNos, CalcBalanceParam param) {
        return BatchUtil.batchPage(facGNos, 500).map(items ->
            mapper.getPreBomWithMaxExgVersion(items, param)
        ).reduce((a, b) -> {
            a.addAll(b);
            return a;
        }).orElse(null);
    }

    /**
     * 插入平衡result表（未进/出口的料件记录）
     * @param exgInfos
     * @param maxBoms
     * @param param
     */
    private void insertBalanceResultForNoRecord(List<MatInfoWithNoRecord> exgInfos,
                                                List<BalancePreBom> maxBoms,
                                                CalcBalanceParam param) {
        Map<String, List<BalancePreBom>> mapFromExgToBom =
                maxBoms.stream().collect(Collectors.groupingBy(BalancePreBom::getCopExgNo));


        Set<String> facGNos = maxBoms.stream().map(BalancePreBom::getCopImgNo).collect(Collectors.toSet());

        Map<String, MatInfoWithNoRecord> mapForMatInfo = getMatInfo(new ArrayList<>(facGNos), param);

        // noRecordMap 无进出口记录料件
        Map<String, BalanceResult> noRecordMap = new HashMap<>();

        // bom 折料
        exgInfos.stream().forEach(exgInfo -> {
            String copExgNo = exgInfo.getFacGNo();
            if(mapFromExgToBom.containsKey(copExgNo)) {
                List<BalancePreBom> theBom = mapFromExgToBom.get(copExgNo);

                    theBom.stream().forEach(bom -> {
                        String copImgNo = bom.getCopImgNo();

                        BigDecimal qty = clacDmAll(bom);

                        if (noRecordMap.containsKey(copImgNo)) {
                            BalanceResult result = noRecordMap.get(copImgNo);
                            result.setNoExpQty(exgInfo, qty);
                        } else {
                            BalanceResult result = new BalanceResult().initRecord(param.getToken());
                            // 组装物料信息
                            if (mapForMatInfo.containsKey(copImgNo)) {
                                dtoMapper.updatePo(mapForMatInfo.get(copImgNo),  result);
                            }
                            result.setEmsNo(param.getEmsNo());
                            result.setNoExpQty(exgInfo, qty);
                            noRecordMap.put(copImgNo, result);
                        }
                    });
//
            }
        });

        // 插入结果表（存在更新，不存在插入）
        List<BalanceResult> listAll = new ArrayList<>(noRecordMap.values());
        if (CollectionUtils.isNotEmpty(listAll)) {
            listAll.stream().forEach(result -> {
                BalanceResult model = getByUniqueKey(result.getTradeCode(), result.getEmsNo(), result.getFacGNo());
                if (model == null) {
                    mapper.insert(result);
                } else {
                    model.setIsRecord(result.getIsRecord());
                    model.setNoRecord(result.getNoRecord());
                    model.setUpdateUser(param.getToken().getUserNo());
                    model.setUpdateUserName(param.getToken().getUserNo());
                    model.setUpdateTime(new Date());

                    mapper.updateByPrimaryKey(model);
                }
            });
        }


    }

    /**
     * 获取备案信息（包括未备案通过的）
     * @param facGNos
     * @param param
     * @return
     */
    private Map<String, MatInfoWithNoRecord> getMatInfo(List<String> facGNos, CalcBalanceParam param) {

        if (CollectionUtils.isEmpty(facGNos)) {
            return new HashMap<>();
        }
        List<MatInfoWithNoRecord> matInfos = mapper.getMatInfo(facGNos, param);

        if(CollectionUtils.isNotEmpty(matInfos)) {
            return matInfos.stream().collect(Collectors.toMap(it -> it.getFacGNo(), Function.identity(), (oldVal, newVal) -> newVal));
        }
        return null;

    }


    /**
     * 计算单耗
     * @param bom
     * @return
     */
    private BigDecimal clacDmAll(BalancePreBom bom) {
        return BigDecimal.ONE.multiply(bom.getDecCm().divide(
                BigDecimal.ONE.subtract(bom.getDecDmInvisiable().divide(new BigDecimal(100)))
                        .subtract(bom.getDecDmVisiable().divide(new BigDecimal(100))), 5, BigDecimal.ROUND_HALF_UP));
    }


    /**
     * 查询
     * @param tradeCode
     * @param emsNo
     * @param facGNo
     * @return
     */
    private BalanceResult getByUniqueKey(String tradeCode, String emsNo, String facGNo) {
        return mapper.getByUniqueKey(tradeCode, emsNo, facGNo);
    }





}
