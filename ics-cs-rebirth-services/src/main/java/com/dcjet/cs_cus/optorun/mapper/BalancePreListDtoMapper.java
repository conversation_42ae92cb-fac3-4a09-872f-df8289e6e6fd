package com.dcjet.cs_cus.optorun.mapper;

import com.dcjet.cs.dto_cus.optorun.BalancePreListDto;
import com.dcjet.cs.dto_cus.optorun.BalancePreListParam;
import com.dcjet.cs_cus.optorun.model.BalancePreList;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BalancePreListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BalancePreListDto toDto(BalancePreList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BalancePreList toPo(BalancePreListParam param);
    default void patchPo(BalancePreListParam balancePreListParam, BalancePreList balancePreList) {
        // TODO 自行实现局部更新
    }
}
