package com.dcjet.cs_cus.optorun.service;

import com.dcjet.cs.dto_cus.optorun.BalancePreBomDto;
import com.dcjet.cs.dto_cus.optorun.BalancePreBomParam;
import com.dcjet.cs_cus.optorun.dao.BalancePreBomMapper;
import com.dcjet.cs_cus.optorun.mapper.BalancePreBomDtoMapper;
import com.dcjet.cs_cus.optorun.model.BalancePreBom;
import com.dcjet.cs_cus.optorun.model.CalcBalanceParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-1
 */
@Service
public class BalancePreBomService extends BaseService<BalancePreBom> {
    @Resource
    private BalancePreBomMapper mapper;
    @Resource
    private BalancePreBomDtoMapper dtoMapper;
    @Override
    public Mapper<BalancePreBom> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param balancePreBomParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BalancePreBomDto>> getListPaged(BalancePreBomParam balancePreBomParam, PageParam pageParam) {
        // 启用分页查询
        BalancePreBom balancePreBom = dtoMapper.toPo(balancePreBomParam);
        Page<BalancePreBom> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(balancePreBom));
        List<BalancePreBomDto> balancePreBomDtos = page.getResult().stream().map(head -> {
            BalancePreBomDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BalancePreBomDto>> paged = ResultObject.createInstance(balancePreBomDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BalancePreBomDto> selectAll(BalancePreBomParam exportParam, UserInfoToken userInfo) {
        BalancePreBom balancePreBom = dtoMapper.toPo(exportParam);
        // cgwOptorunBalancePreBom.setTradeCode(userInfo.getCompany());
        List<BalancePreBomDto> balancePreBomDtos = new ArrayList<>();
        List<BalancePreBom> balancePreBoms = mapper.getList(balancePreBom);
        if (CollectionUtils.isNotEmpty(balancePreBoms)) {
            balancePreBomDtos = balancePreBoms.stream().map(head -> {
                BalancePreBomDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return balancePreBomDtos;
    }


    /**
     *   初始化BOM
     * @param param
     */
    public void initData(CalcBalanceParam param) {
        mapper.initData(param);
    }
}
