package com.dcjet.cs_cus.optorun.service;

import com.dcjet.cs.dto_cus.optorun.BalancePreListDto;
import com.dcjet.cs.dto_cus.optorun.BalancePreListParam;
import com.dcjet.cs_cus.optorun.dao.BalancePreListMapper;
import com.dcjet.cs_cus.optorun.mapper.BalancePreListDtoMapper;
import com.dcjet.cs_cus.optorun.model.BalancePreList;
import com.dcjet.cs_cus.optorun.model.CalcBalanceParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-1
 */
@Service
public class BalancePreListService extends BaseService<BalancePreList> {
    @Resource
    private BalancePreListMapper mapper;
    @Resource
    private BalancePreListDtoMapper dtoMapper;
    @Override
    public Mapper<BalancePreList> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param balancePreListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BalancePreListDto>> getListPaged(BalancePreListParam balancePreListParam, PageParam pageParam) {
        // 启用分页查询
        BalancePreList balancePreList = dtoMapper.toPo(balancePreListParam);
        Page<BalancePreList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(balancePreList));
        List<BalancePreListDto> balancePreListDtos = page.getResult().stream().map(head -> {
            BalancePreListDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BalancePreListDto>> paged = ResultObject.createInstance(balancePreListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BalancePreListDto> selectAll(BalancePreListParam exportParam, UserInfoToken userInfo) {
        BalancePreList balancePreList = dtoMapper.toPo(exportParam);
        // cgwOptorunBalancePreList.setTradeCode(userInfo.getCompany());
        List<BalancePreListDto> balancePreListDtos = new ArrayList<>();
        List<BalancePreList> balancePreLists = mapper.getList(balancePreList);
        if (CollectionUtils.isNotEmpty(balancePreLists)) {
            balancePreListDtos = balancePreLists.stream().map(head -> {
                BalancePreListDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return balancePreListDtos;
    }


    /**
     *   初始化PRE LIST
     * @param param
     */
    public void initData(CalcBalanceParam param) {
        mapper.initData(param);
    }
}
