package com.dcjet.cs_cus.optorun.dao;

import com.dcjet.cs.base.repository.BatchMapper;
import com.dcjet.cs_cus.optorun.model.BalancePreBom;
import com.dcjet.cs_cus.optorun.model.BalanceResult;
import com.dcjet.cs_cus.optorun.model.CalcBalanceParam;
import com.dcjet.cs_cus.optorun.model.MatInfoWithNoRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* CgwOptorunBalanceResult
* <AUTHOR>
* @date: 2021-8-31
*/
public interface BalanceResultMapper extends Mapper<BalanceResult>, BatchMapper<BalanceResult> {
    /**
     * 查询获取数据
     * @param balanceResult
     * @return
     */
    List<BalanceResult> getList(BalanceResult balanceResult);


    void initData1(CalcBalanceParam param);


    List<MatInfoWithNoRecord> getExgInfoWithNoRecord(CalcBalanceParam param);

    List<BalancePreBom> getPreBomWithMaxExgVersion(@Param("copExgNos")List<String> copExgNos, @Param("param") CalcBalanceParam param);

    List<MatInfoWithNoRecord> getMatInfo(@Param("copImgNos")List<String> copImgNos, @Param("param") CalcBalanceParam param);

    @Select("select * from T_CGW_OPTORUN_BALANCE_RESULT where TRADE_CODE = #{tradeCode} and EMS_NO = #{emsNo} and FAC_G_NO = #{facGNo}")
    BalanceResult getByUniqueKey(@Param("tradeCode") String tradeCode,
                                 @Param("emsNo") String emsNo,
                                 @Param("facGNo") String facGNo);
    @Update("update T_CGW_OPTORUN_BALANCE_RESULT set LAST_SURPLUS = THEORY_SURPLUS - IS_RECORD - NO_RECORD where TRADE_CODE = #{token.company} and EMS_NO = #{emsNo}")
    void clacRemainFinally(CalcBalanceParam param);


    void insertForImgWithNoRecord(CalcBalanceParam param);
}
