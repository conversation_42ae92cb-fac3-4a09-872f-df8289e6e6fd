package com.dcjet.cs_cus.optorun.mapper;

import com.dcjet.cs.dto_cus.optorun.BalanceResultDto;
import com.dcjet.cs.dto_cus.optorun.BalanceResultParam;
import com.dcjet.cs_cus.optorun.model.BalanceResult;
import com.dcjet.cs_cus.optorun.model.MatInfoWithNoRecord;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-31
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BalanceResultDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BalanceResultDto toDto(BalanceResult po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BalanceResult toPo(BalanceResultParam param);
    default void patchPo(BalanceResultParam balanceResultParam, BalanceResult balanceResult) {
        // TODO 自行实现局部更新
    }

    void updatePo(MatInfoWithNoRecord param, @MappingTarget BalanceResult result);
}
