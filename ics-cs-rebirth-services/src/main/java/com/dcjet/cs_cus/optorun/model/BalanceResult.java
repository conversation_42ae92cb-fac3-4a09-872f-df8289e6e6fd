package com.dcjet.cs_cus.optorun.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-8-31
 */
@Setter
@Getter
@Table(name = "T_CGW_OPTORUN_BALANCE_RESULT")
public class BalanceResult implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一键
     */
	@Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 备案号
     */
	@Column(name = "EMS_NO")
	private  String emsNo;
	/**
     * 手/账册内部编号
     */
	@Column(name = "COP_EMS_NO")
	private  String copEmsNo;
	/**
     * 企业料号
     */
	@Column(name = "FAC_G_NO")
	private  String facGNo;
	/**
     * 备案料号
     */
	@Column(name = "COP_G_NO")
	private  String copGNo;
	/**
     * 备案序号
     */
	@Column(name = "SERIAL_NO")
	private  long serialNo;
	/**
     * 商品名称
     */
	@Column(name = "G_NAME")
	@JsonProperty("gName")
	private  String gname;
	/**
     * 商品编码
     */
	@Column(name = "CODE_T_S")
	private  String codeTS;
	/**
     * 余料转入
     */
	@Column(name = "YL_IMP_QTY")
	private  BigDecimal ylImpQty;
	/**
     * 实际进口数量
     */
	@Column(name = "ACTUAL_IMP_QTY")
	private  BigDecimal actualImpQty;
	/**
     * 深加工结转转入
     */
	@Column(name = "SJG_IMP_QTY")
	private  BigDecimal sjgImpQty;
	/**
     * 料件退换进口
     */
	@Column(name = "LJTH_IMP_QTY")
	private  BigDecimal ljthImpQty;
	/**
     * 成品退换进口耗用
     */
	@Column(name = "EXG_IMP_CON")
	private  BigDecimal exgImpCon;
	/**
     * 料件内销
     */
	@Column(name = "IMG_SALE_QTY")
	private  BigDecimal imgSaleQty;
	/**
     * 料件复出
     */
	@Column(name = "IMG_RETURN_QTY")
	private  BigDecimal imgReturnQty;
	/**
     * 料件销毁
     */
	@Column(name = "IMG_DESTROY_QTY")
	private  BigDecimal imgDestroyQty;
	/**
     * 成品直接出口耗用
     */
	@Column(name = "EXP_DIRECT_CON")
	private  BigDecimal expDirectCon;
	/**
     * 成品退换出口耗用
     */
	@Column(name = "EXP_RETURN_CON")
	private  BigDecimal expReturnCon;
	/**
     * 损耗耗用
     */
	@Column(name = "DM_CON")
	private  BigDecimal dmCon;
	/**
     * 余料转出
     */
	@Column(name = "YL_EXP_QTY")
	private  BigDecimal ylExpQty;
	/**
     * 料件退换进口
     */
	@Column(name = "LJTH_EXP_QTY")
	private  BigDecimal ljthExpQty;
	/**
     * 理论结余
     */
	@Column(name = "THEORY_SURPLUS")
	private  BigDecimal theorySurplus;
	/**
     * 已备案未出口
     */
	@Column(name = "IS_RECORD")
	private  BigDecimal isRecord;
	/**
     * 未备案未出口
     */
	@Column(name = "NO_RECORD")
	private  BigDecimal noRecord;
	/**
     * 最后剩余
     */
	@Column(name = "LAST_SURPLUS")
	private  BigDecimal lastSurplus;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 创建人名称
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;

	public BalanceResult initRecord(UserInfoToken token) {
		this.sid = UUID.randomUUID().toString();

		this.ylImpQty = BigDecimal.ZERO;
		this.actualImpQty = BigDecimal.ZERO;
		this.sjgImpQty = BigDecimal.ZERO;
		this.ljthImpQty = BigDecimal.ZERO;
		this.exgImpCon = BigDecimal.ZERO;
		this.imgSaleQty = BigDecimal.ZERO;
		this.imgReturnQty = BigDecimal.ZERO;
		this.imgDestroyQty = BigDecimal.ZERO;
		this.expDirectCon = BigDecimal.ZERO;
		this.expReturnCon = BigDecimal.ZERO;
		this.dmCon = BigDecimal.ZERO;
		this.ylExpQty = BigDecimal.ZERO;
		this.ljthExpQty = BigDecimal.ZERO;
		this.theorySurplus = BigDecimal.ZERO;
		this.isRecord = BigDecimal.ZERO;
		this.noRecord = BigDecimal.ZERO;
		this.lastSurplus = BigDecimal.ZERO;

		this.tradeCode = token.getCompany();
		this.insertUser = token.getUserNo();
		this.insertUserName = token.getUserNo();
		this.insertTime = new Date();

		return this;
	}

	public void setNoExpQty(MatInfoWithNoRecord matInfo, BigDecimal qty) {
		if(matInfo.isForTheRecord()) {
			this.isRecord = Optional.ofNullable(this.isRecord).orElse(BigDecimal.ZERO).add(qty);
		} else {
			this.noRecord = Optional.ofNullable(this.noRecord).orElse(BigDecimal.ZERO).add(qty);
		}
	}
}
