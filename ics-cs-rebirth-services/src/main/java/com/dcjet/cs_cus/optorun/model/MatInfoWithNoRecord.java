package com.dcjet.cs_cus.optorun.model;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("成品料件 simpleinfo")
public class MatInfoWithNoRecord {

    private String facGNo;

    private String apprStatus;

    private String emsNo;

    private String copEmsNo;

    private String copGNo;

    private long serialNo;

    private String gname;

    private String codeTS;

    private String modifyMark;

    /**
     * 是否备案判断逻辑
     * @return
     */
    public Boolean isForTheRecord() {
        return "JC".equals(this.apprStatus);
    }

}
