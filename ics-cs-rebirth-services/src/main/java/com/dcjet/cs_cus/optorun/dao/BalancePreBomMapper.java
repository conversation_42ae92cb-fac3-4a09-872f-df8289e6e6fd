package com.dcjet.cs_cus.optorun.dao;

import com.dcjet.cs_cus.optorun.model.BalancePreBom;
import com.dcjet.cs_cus.optorun.model.CalcBalanceParam;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* CgwOptorunBalancePreBom
* <AUTHOR>
* @date: 2021-9-1
*/
public interface BalancePreBomMapper extends Mapper<BalancePreBom> {
    /**
     * 查询获取数据
     * @param balancePreBom
     * @return
     */
    List<BalancePreBom> getList(BalancePreBom balancePreBom);


    void initData(CalcBalanceParam param);

}
