package com.dcjet.cs_cus.optorun.dao;

import com.dcjet.cs_cus.optorun.model.BalancePreList;
import com.dcjet.cs_cus.optorun.model.CalcBalanceParam;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* CgwOptorunBalancePreList
* <AUTHOR>
* @date: 2021-9-1
*/
public interface BalancePreListMapper extends Mapper<BalancePreList> {
    /**
     * 查询获取数据
     * @param balancePreList
     * @return
     */
    List<BalancePreList> getList(BalancePreList balancePreList);

    void initData(CalcBalanceParam param);

    void imgReturn(CalcBalanceParam param);
}
