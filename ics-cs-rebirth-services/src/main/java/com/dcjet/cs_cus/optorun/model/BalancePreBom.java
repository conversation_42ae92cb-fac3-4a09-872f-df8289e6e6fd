package com.dcjet.cs_cus.optorun.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-9-1
 */
@Setter
@Getter
@Table(name = "T_CGW_OPTORUN_BALANCE_PRE_BOM")
public class BalancePreBom implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	@Column(name = "SID")
	private  String sid;
	/**
     * 手账册编号
     */
	@Column(name = "EMS_NO")
	private  String emsNo;
	/**
     * 成品料号
     */
	@Column(name = "COP_EXG_NO")
	private  String copExgNo;
	/**
     * 料件料号
     */
	@Column(name = "COP_IMG_NO")
	private  String copImgNo;
	/**
     * 单耗版本号
     */
	@Column(name = "EXG_VERSION")
	private  String exgVersion;
	/**
     * 净耗
     */
	@Column(name = "DEC_CM")
	private  BigDecimal decCm;
	/**
     * 有形损耗率%
     */
	@Column(name = "DEC_DM_VISIABLE")
	private  BigDecimal decDmVisiable;
	/**
     * 无形损耗率%
     */
	@Column(name = "DEC_DM_INVISIABLE")
	private  BigDecimal decDmInvisiable;
	/**
     * 提交计算的登录账号
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 提交计算时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 提交计算的姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;


}
