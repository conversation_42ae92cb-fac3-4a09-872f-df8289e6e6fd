package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs_cus.daikin.model.CollectionHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * CollectionManagementHead
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
public interface CollectionHeadMapper extends Mapper<CollectionHead> {
    /**
     * 查询获取数据
     *
     * @param collectionHead
     * @return
     */
    List<CollectionHead> getList(CollectionHead collectionHead);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 查询数据是否存在
     * @param sid
     * @param emsListNo
     * @param company
     * @return
     */
    Integer chekExist(@Param("sid")String sid, @Param("emsListNo")String emsListNo, @Param("tradeCode")String company);
}
