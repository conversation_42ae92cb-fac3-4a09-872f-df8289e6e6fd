package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto_cus.daikin.OrderListDto;
import com.dcjet.cs.dto_cus.daikin.OrderListParam;
import com.dcjet.cs.util.LoggerUtil;
import com.dcjet.cs_cus.daikin.dao.OrderListMapper;
import com.dcjet.cs_cus.daikin.mapper.OrderListDtoMapper;
import com.dcjet.cs_cus.daikin.model.OrderList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Service
public class OrderListService extends BaseService<OrderList> {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private OrderListMapper orderListMapper;
    @Resource
    private OrderListDtoMapper orderListDtoMapper;
    @Resource
    private PCodeHolder pCodeHolder;

    @Override
    public Mapper<OrderList> getMapper() {
        return orderListMapper;
    }

    /**
     * 功能描述: grid分页查询
     *
     * @param orderListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<OrderListDto>> selectAllPaged(OrderListParam orderListParam, PageParam pageParam) {
        // 启用分页查询
        if (StringUtils.isNotBlank(orderListParam.getCustomerOrderNo())) {
            orderListParam.setCustomerOrderNo(orderListParam.getCustomerOrderNo().replace("，", ","));
            orderListParam.setCustomerOrderNo(orderListParam.getCustomerOrderNo().replaceAll(" ", ","));
            orderListParam.setCustomerOrderNo(orderListParam.getCustomerOrderNo().replaceAll("[',']+", ","));
        }
        if (StringUtils.isNotBlank(orderListParam.getFacGNo())) {
            orderListParam.setFacGNo(orderListParam.getFacGNo().replace("，", ","));
            orderListParam.setFacGNo(orderListParam.getFacGNo().replaceAll(" ", ","));
            orderListParam.setFacGNo(orderListParam.getFacGNo().replaceAll("[',']+", ","));
        }
        OrderList orderList = orderListDtoMapper.toPo(orderListParam);
        Page<OrderList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> orderListMapper.getList(orderList));
        List<OrderListDto> orderListDtos = page.getResult().stream().map(head -> {
            OrderListDto dto = orderListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<OrderListDto>> paged = ResultObject.createInstance(orderListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public OrderListDto insert(OrderListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        OrderList orderList = orderListDtoMapper.toPo(model);
        orderList.setSid(sid);
        orderList.setInsertUser(userInfo.getUserNo());
        orderList.setInsertTime(new Date());
        orderList.setInsertUserName(userInfo.getUserName());
        orderList.setTradeCode(userInfo.getCompany());
        int insertStatus = orderListMapper.insert(orderList);
        return insertStatus > 0 ? orderListDtoMapper.toDto(orderList) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param orderListParam
     * @param userInfo
     * @return
     */
    public OrderListDto update(OrderListParam orderListParam, UserInfoToken userInfo) {
        OrderList orderList = orderListMapper.selectByPrimaryKey(orderListParam.getSid());
        orderListParam.setTradeCode(userInfo.getCompany());
        orderListDtoMapper.updatePo(orderListParam, orderList);
        orderList.setUpdateUser(userInfo.getUserNo());
        orderList.setUpdateTime(new Date());
        orderList.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = orderListMapper.updateByPrimaryKey(orderList);
        return update > 0 ? orderListDtoMapper.toDto(orderList) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids) {
        orderListMapper.deleteBySids(sids);
    }

    /**
     * 报表查询
     *
     * @param orderListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<OrderListDto>> getReport(OrderListParam orderListParam, PageParam pageParam) {
        // 启用分页查询
        Page<OrderListDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> orderListMapper.getReport(orderListParam));
        convertForPrint(page.getResult());
        ResultObject<List<OrderListDto>> paged = ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    private void convertForPrint(List<OrderListDto> list) {
        for (OrderListDto item : list) {
            if (StringUtils.isNotBlank(item.getCustomerCode()) && StringUtils.isNotBlank(item.getCustomerName())) {
                item.setCustomerCode(item.getCustomerCode() + " " + item.getCustomerName());
            }
            if (StringUtils.isNotBlank(item.getCurr())) {
                item.setCurr(item.getCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
            }
        }
    }

    /**
     * 获取所有类别
     */
    public ResultObject<List<String>> getAllTypes(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        List<String> types = orderListMapper.getAllTypes(userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(types)) {
            resultObject.setData(types);
        }
        return resultObject;
    }

    /**
     * 计算已出口数量及申报数量
     */
    public void calData(String tradeCode) {
        //更新申报数量
        LoggerUtil.logInfo(logger, "***********企业【" + tradeCode + "】开始更新订单数量************");
        orderListMapper.updateOrderQty(tradeCode);
        LoggerUtil.logInfo(logger, "***********企业【" + tradeCode + "】更新订单数量结束************");
        //计算已出口数量
        LoggerUtil.logInfo(logger, "***********企业【" + tradeCode + "】开始更新已出口数量************");
        Map<String, Object> pa = new HashMap<>(4);
        pa.put("p_trade_code", tradeCode);
        pa.put("p_ret_code", "");
        pa.put("p_ret_str", "");
        Map<String, String> map = orderListMapper.updateExportQty(pa);
        if (pa.get("p_ret_code") != null && !pa.get("p_ret_code").toString().isEmpty()) {
            throw new ErrorException(400, pa.get("p_ret_code") + ":" + pa.get("p_ret_str"));
        } else if (map != null && map.get("p_ret_code") != null && !map.get("p_ret_code").isEmpty()) {
            throw new ErrorException(400, map.get("p_ret_code") + ":" + map.get("p_ret_str"));
        }
        LoggerUtil.logInfo(logger, "***********企业【" + tradeCode + "】更新已出口数量结束************");
    }
}
