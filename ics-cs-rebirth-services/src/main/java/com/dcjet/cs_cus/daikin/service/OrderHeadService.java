package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.dto_cus.daikin.OrderHeadDto;
import com.dcjet.cs.dto_cus.daikin.OrderHeadParam;
import com.dcjet.cs_cus.daikin.dao.OrderHeadMapper;
import com.dcjet.cs_cus.daikin.dao.OrderListMapper;
import com.dcjet.cs_cus.daikin.mapper.OrderHeadDtoMapper;
import com.dcjet.cs_cus.daikin.model.OrderHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Service
public class OrderHeadService extends BaseService<OrderHead> {
    @Resource
    private OrderHeadMapper orderHeadMapper;
    @Resource
    private OrderHeadDtoMapper orderHeadDtoMapper;
    @Resource
    private OrderListMapper orderListMapper;

    @Override
    public Mapper<OrderHead> getMapper() {
        return orderHeadMapper;
    }

    /**
     * 获取分页信息
     *
     * @param orderHeadParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<OrderHeadDto>> getListPaged(OrderHeadParam orderHeadParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        OrderHead orderHead = orderHeadDtoMapper.toPo(orderHeadParam);
        orderHead.setTradeCode(userInfo.getCompany());
        Page<OrderHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> orderHeadMapper.getList(orderHead));
        List<OrderHeadDto> orderHeadDtos = page.getResult().stream().map(head -> {
            OrderHeadDto dto = orderHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<OrderHeadDto>> paged = ResultObject.createInstance(orderHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param orderHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderHeadDto insert(OrderHeadParam orderHeadParam, UserInfoToken userInfo) {
        OrderHead orderHead = orderHeadDtoMapper.toPo(orderHeadParam);
        //校验唯一性
        OrderHead param = new OrderHead();
        param.setTradeCode(userInfo.getCompany());
        param.setManageOrderNo(orderHeadParam.getManageOrderNo());
        List<OrderHead> orderHeads = orderHeadMapper.getList(param);
        if (CollectionUtils.isNotEmpty(orderHeads)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("订单管理号已存在"));
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        orderHead.setSid(sid);
        orderHead.setInsertUser(userInfo.getUserNo());
        orderHead.setInsertTime(new Date());
        orderHead.setTradeCode(userInfo.getCompany());
        orderHead.setInsertUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = orderHeadMapper.insert(orderHead);
        return insertStatus > 0 ? orderHeadDtoMapper.toDto(orderHead) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param orderHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderHeadDto update(OrderHeadParam orderHeadParam, UserInfoToken userInfo) {
        OrderHead orderHead = orderHeadMapper.selectByPrimaryKey(orderHeadParam.getSid());
        orderHeadDtoMapper.updatePo(orderHeadParam, orderHead);
        orderHead.setUpdateUser(userInfo.getUserNo());
        orderHead.setUpdateTime(new Date());
        orderHead.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = orderHeadMapper.updateByPrimaryKey(orderHead);
        return update > 0 ? orderHeadDtoMapper.toDto(orderHead) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
        orderHeadMapper.deleteBySids(sids);
        orderListMapper.deleteByHeadIds(sids);
    }
}
