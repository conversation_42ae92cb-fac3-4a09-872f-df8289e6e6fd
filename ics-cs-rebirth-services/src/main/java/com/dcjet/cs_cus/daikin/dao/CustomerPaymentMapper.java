package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs_cus.daikin.model.CustomerPayment;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * CustomerPayment
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
public interface CustomerPaymentMapper extends Mapper<CustomerPayment> {
    /**
     * 查询获取数据
     *
     * @param customerPayment
     * @return
     */
    List<CustomerPayment> getList(CustomerPayment customerPayment);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 查询客户代码是否存在
     *
     * @param customerCode
     * @param tradeCode
     * @return
     */
    Integer chekcustomerCode(@Param("customerCode") String customerCode, @Param("tradeCode") String tradeCode, @Param("sid") String sid);
}
