package com.dcjet.cs_cus.daikin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Setter
@Getter
@Table(name = "T_CGW_DAIKIN_ORDER_LIST")
public class OrderList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Column(name = "SID")
    @Id
    private String sid;
    /**
     * 订单管理表头SID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 客户订单号
     */
    @Column(name = "CUSTOMER_ORDER_NO")
    private String customerOrderNo;
    /**
     * 客户料号(PARTS NO)
     */
    @Column(name = "CUSTOMER_FAC_G_NO")
    private String customerFacGNo;
    /**
     * 申报图号（即企业成品料号）
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 英文品名
     */
    @Column(name = "COP_G_NAME_EN")
    private String copGNameEn;
    /**
     * INTERNAL ORDER NO
     */
    @Column(name = "INTERNAL_ORDER_NO")
    private String internalOrderNo;
    /**
     * 申报数量
     */
    @Column(name = "ORDER_QTY")
    private BigDecimal orderQty;
    /**
     * 注文要求纳期(ETD)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @Column(name = "ETD")
    private Date etd;
    /**
     * 调整纳期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @Column(name = "CHANGE_ETD")
    private Date changeEtd;
    /**
     * 最终纳期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @Column(name = "LAST_ETD")
    private Date lastEtd;
    /**
     * 已出口数
     */
    @Column(name = "EXPORT_QTY")
    private BigDecimal exportQty;
    /**
     * 申报剩余数
     */
    @Column(name = "SURPLUS_QTY")
    private BigDecimal surplusQty;
    /**
     * 单价
     */
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 订单管理号
     */
    @Column(name = "MANAGE_ORDER_NO")
    private String manageOrderNo;
    /**
     * 订单图号
     */
    @Column(name = "ORDER_FAC_G_NO")
    private String orderFacGNo;
    /**
     * 订单数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;

    @Transient
    private String type;
}
