package com.dcjet.cs_cus.daikin.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Setter
@Getter
@Table(name = "T_CGW_DAIKIN_COLLECTION_HEAD")
public class CollectionHead extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 发票号
     */
    @Column(name = "INVOICE_NO")
    private String invoiceNo;
    /**
     * 单据内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 境外收货人
     */
    @Column(name = "OVERSEAS_SHIPPER")
    private String overseasShipper;
    /**
     * 境外收货人名称
     */
    @Column(name = "OVERSEAS_SHIPPER_NAME")
    private String overseasShipperName;
    /**
     * ATD/验收日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ATD_ACCEPTANCE_DATE")
    private Date atdAcceptanceDate;
    /**
     * ATD/验收日期-开始
     */
    @Transient
    private String atdAcceptanceDateFrom;
    /**
     * ATD/验收日期-结束
     */
    @Transient
    private String atdAcceptanceDateTo;
    /**
     * 入金截止日
     */
    @Column(name = "DEPOSIT_DEADLINE_DATE")
    private Date depositDeadlineDate;
    /**
     * 开票申请日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "LNV_APPLY_DATE")
    private Date lnvApplyDate;
    /**
     * 实际入金日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ACTUAL_DEPOSIT_DATE")
    private Date actualDepositDate;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 结算日期
     */
    @Transient
    private String settlementDate;
    @Transient
    private String settlementYear;
}
