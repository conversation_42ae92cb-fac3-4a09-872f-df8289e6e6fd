package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.VCollectionHeadListDto;
import com.dcjet.cs.dto_cus.daikin.VCollectionHeadListParam;
import com.dcjet.cs_cus.daikin.model.VCollectionHeadList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VCollectionHeadListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    VCollectionHeadListDto toDto(VCollectionHeadList po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    VCollectionHeadList toPo(VCollectionHeadListParam param);

    /**
     * 数据库原始数据更新
     *
     * @param vCollectionHeadListParam
     * @param vCollectionHeadList
     */
    void updatePo(VCollectionHeadListParam vCollectionHeadListParam, @MappingTarget VCollectionHeadList vCollectionHeadList);

    default void patchPo(VCollectionHeadListParam vCollectionHeadListParam, VCollectionHeadList vCollectionHeadList) {
        // TODO 自行实现局部更新
    }
}
