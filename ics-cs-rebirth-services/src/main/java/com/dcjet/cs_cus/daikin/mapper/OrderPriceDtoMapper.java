package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.OrderPriceDto;
import com.dcjet.cs.dto_cus.daikin.OrderPriceParam;
import com.dcjet.cs_cus.daikin.model.OrderPrice;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderPriceDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    OrderPriceDto toDto(OrderPrice po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    OrderPrice toPo(OrderPriceParam param);

    /**
     * 数据库原始数据更新
     *
     * @param orderPriceParam
     * @param orderPrice
     */
    void updatePo(OrderPriceParam orderPriceParam, @MappingTarget OrderPrice orderPrice);

    default void patchPo(OrderPriceParam orderPriceParam, OrderPrice orderPrice) {
        // TODO 自行实现局部更新
    }
}
