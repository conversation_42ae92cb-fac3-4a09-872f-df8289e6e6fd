package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.TmpDaikinCollectionHeadParam;
import com.dcjet.cs_cus.daikin.model.CollectionHead;
import com.dcjet.cs_cus.daikin.model.TmpDaikinCollectionHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-12-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TmpDaikinCollectionHeadDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    TmpDaikinCollectionHead toPo(TmpDaikinCollectionHeadParam param);

    /**
     * 数据库原始数据更新
     *
     * @param tmpDaikinCollectionHeadParam
     * @param tmpDaikinCollectionHead
     */
    void updatePo(TmpDaikinCollectionHeadParam tmpDaikinCollectionHeadParam, @MappingTarget TmpDaikinCollectionHead tmpDaikinCollectionHead);

    default void patchPo(TmpDaikinCollectionHeadParam tmpDaikinCollectionHeadParam, TmpDaikinCollectionHead tmpDaikinCollectionHead) {
        // TODO 自行实现局部更新
    }

    /**
     * 数据库原始数据更新
     *
     * @param po
     * @param model
     */
    void updatePo(TmpDaikinCollectionHead po, @MappingTarget CollectionHead model);

}
