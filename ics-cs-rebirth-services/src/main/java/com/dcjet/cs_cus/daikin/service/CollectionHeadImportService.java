package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs_cus.daikin.dao.CollectionHeadMapper;
import com.dcjet.cs_cus.daikin.dao.TmpDaikinCollectionHeadMapper;
import com.dcjet.cs_cus.daikin.mapper.TmpDaikinCollectionHeadDtoMapper;
import com.dcjet.cs_cus.daikin.model.CollectionHead;
import com.dcjet.cs_cus.daikin.model.TmpDaikinCollectionHead;
import com.google.common.base.Strings;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.exception.ErrorException;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


@Service
public class CollectionHeadImportService implements ImportHandlerInterface {

    @Resource
    private TmpDaikinCollectionHeadMapper tmpDaikinCollectionHeadMapper;

    @Resource
    private TmpDaikinCollectionHeadDtoMapper tmpDaikinCollectionHeadDtoMapper;

    @Resource
    private CollectionHeadMapper collectionHeadMapper;

    @Resource
    private BulkInsertConfig bulkInsertConfig;
    public IBulkInsert getBulkInsertInstance() throws Exception{
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    // 0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal
    public final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();

    @Override
    public String getTaskCode() {
        String strTaskCode = "DAIKIN_COLLECTION";
        return strTaskCode;
    }


    /**
     * 业务校验方法（临时表校验） 必须实现
     *
     * @param mapBasicParam    任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList    实体列表map，可根据xmlContent的sheet的id获取某类实体列表
     * @return 业务校验结果，head根据xmlContent的sheet的field获取，无需设置；当采用临时表校验方式时tempOwnerId必须设置，否则插入正确数据时无法进行操作
     */
    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        //校验必填参数
        if (!mapBusinessParam.containsKey("insertUser")) {
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();
        String guid = UUID.randomUUID().toString();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);
        List<TmpDaikinCollectionHead> tmpDaikinCollectionHeads = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        Date currDate = new Date();
        //删除临时表所有数据
        tmpDaikinCollectionHeadMapper.deleteList();
        objectList.forEach(pa -> {
            TmpDaikinCollectionHead el = (TmpDaikinCollectionHead) pa;
            el.setTradeCode(tradeCode);
            el.setInsertUser(insertUser);
            el.setInsertUserName(insertUserName);
            el.setInsertTime(currDate);
            el.setTempOwner(guid);
            check(el,objectList);
            if (Strings.isNullOrEmpty(el.getTempRemark())) {
                el.setTempRemark("");
                el.setTempFlag(CORRECT_FLAG);
            }
            tmpDaikinCollectionHeads.add(el);
        });

        XdoImportLogger.log("共获取插入实体：" + tmpDaikinCollectionHeads.size() + ";执行快速入库操作");

        IBulkInsert iBulkInsert = getBulkInsertInstance();
        int intEffectCount = 0;
        try {
            intEffectCount = iBulkInsert.fastImport(tmpDaikinCollectionHeads);
        } catch (BulkInsertException ex) {
            intEffectCount = ex.getCommitRowCount();
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        } catch (Exception ex) {
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        }
        ExcelImportDto<TmpDaikinCollectionHead> excelImportDto = new ExcelImportDto<>();
        excelImportDto.setTempOwnerId(guid);

        //获取正确、错误、警告数据列表(警告数据根据各自业务需求设置,可不加)
        List<TmpDaikinCollectionHead> correctList = selectDataList(guid, CORRECT_FLAG);
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);

        List<TmpDaikinCollectionHead> wrongList = selectDataList(guid, WRONG_FLAG);
        excelImportDto.setWrongNumber(wrongList.size());
        if (wrongList.size() > 0 && correctList.size() > 0) {
            wrongList.addAll(correctList);
        }
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log("correct：" + excelImportDto.getCorrectList().size()
                + ";wrong：" + excelImportDto.getWrongList().size());
        excelImportDtoList.add(excelImportDto);
        return excelImportDtoList;
    }

    /**
     *
     * @param el
     * @param objectList
     */
    private void check(TmpDaikinCollectionHead el, List<Object> objectList) {
        //根据发票号查询此条数据是否存在
        TmpDaikinCollectionHead tmpDaikinCollectionHead = tmpDaikinCollectionHeadMapper.selectlist(el.getInvoiceNo(),el.getEmsListNo(),el.getTradeCode());

        if (tmpDaikinCollectionHead!=null){
            //校验发票号是否存在
            AtomicInteger i = new AtomicInteger();
            if (StringUtils.isNotEmpty(tmpDaikinCollectionHead.getInvoiceNo())){
                objectList.forEach(pa -> {
                    TmpDaikinCollectionHead daiKin = (TmpDaikinCollectionHead) pa;
                    if (StringUtils.equals(el.getInvoiceNo(),daiKin.getInvoiceNo())){
                        i.getAndIncrement();
                    }
                });
                if (i.get() >= 2) {
                    el.setTempRemark(el.getTempRemark()+xdoi18n.XdoI18nUtil.t("发票号相同|"));
                    el.setTempFlag(WRONG_FLAG);
                    tmpDaikinCollectionHead.setSid(UUID.randomUUID().toString());
                }
            }

            //校验开票申请日和实际入金日期是否为空
            CollectionHead collectionHead = collectionHeadMapper.selectByPrimaryKey(tmpDaikinCollectionHead.getSid());
            if (collectionHead!=null){
                if (el.getLnvApplyDate()==null){
                    el.setLnvApplyDate(collectionHead.getLnvApplyDate());
                }
                if (el.getActualDepositDate()==null){
                    el.setActualDepositDate(collectionHead.getActualDepositDate());
                }
                if (StringUtils.isEmpty(el.getNote())){
                    el.setNote(collectionHead.getNote());
                }
            }

            //计算入金截止日
            if (StringUtils.isNotBlank(tmpDaikinCollectionHead.getSettlementDate())&&StringUtils.isNotBlank(tmpDaikinCollectionHead.getSettlementYear()) && tmpDaikinCollectionHead.getAtdAcceptanceDate() != null) {
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                Calendar c = Calendar.getInstance();
                //当月最后一天
                Calendar calendar2 = Calendar.getInstance();

                c.setTime(tmpDaikinCollectionHead.getAtdAcceptanceDate());
                //增加月份
                c.add(Calendar.MONTH, Integer.parseInt(tmpDaikinCollectionHead.getSettlementYear()));

                c.set(Calendar.DATE, 1);        //设置为该月第一天
                calendar2.setTime(c.getTime());
                calendar2.add(Calendar.MONTH, 1);
                calendar2.add(Calendar.DATE, -1);    //再减一天即为上个月最后一天

                c.add(Calendar.DAY_OF_MONTH,Integer.parseInt(tmpDaikinCollectionHead.getSettlementDate())-1);
                String date = sf.format(c.getTime());
                Date dated = c.getTime();

                String finallyDay = sf.format(calendar2.getTime());
                Date dayDate = calendar2.getTime();

                //比较日期大小
                int dates = date.compareTo(finallyDay);
                if (dates > 0) {
                    el.setDepositDeadlineDate(dayDate);
                } else {
                    el.setDepositDeadlineDate(dated);
                }
            }

            el.setSid(tmpDaikinCollectionHead.getSid());
            el.setEmsListNo(tmpDaikinCollectionHead.getEmsListNo());
            el.setInsertTime(tmpDaikinCollectionHead.getInsertTime());
            el.setOverseasShipper(tmpDaikinCollectionHead.getOverseasShipper());
            el.setOverseasShipperName(tmpDaikinCollectionHead.getOverseasShipperName());
            el.setAtdAcceptanceDate(tmpDaikinCollectionHead.getAtdAcceptanceDate());


        }else {
            el.setSid(UUID.randomUUID().toString());
            el.setTempRemark(el.getTempRemark()+xdoi18n.XdoI18nUtil.t("发票号、单据内部编号不存在|"));
            el.setTempFlag(WRONG_FLAG);
        }



    }

    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        //按配置顺序（同时也是业务校验时添加到excelImportDtoList的顺序）获取临时表批次Id
        if (tempOwnerIdList == null || tempOwnerIdList.size() <= 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("临时表处理批次Id为空，无法执行持久化数据方法"));
        }
        String strTempOwnerId = tempOwnerIdList.get(0);
        List<TmpDaikinCollectionHead> tempList = selectDataList(strTempOwnerId, CORRECT_FLAG);
        List<CollectionHead> list = new ArrayList<>(tempList.size());
        List<String>listSids= new ArrayList<>(tempList.size());
        Date currDate = new Date();

        Map<String, Object> map = new HashMap<>(1);
        map.put("tradeCode", mapBasicParam.get("tradeCode").toString());
        if (CollectionUtils.isNotEmpty(tempList)) {
            tempList.forEach(el -> {
                CollectionHead model = new CollectionHead();
                tmpDaikinCollectionHeadDtoMapper.updatePo(el, model);
                model.setInsertTime(currDate);

                listSids.add(model.getSid());
                list.add(model);
            });
            //先删除后插入
            if (listSids != null) {
                Integer sum = collectionHeadMapper.deleteBySids(listSids);
            }
            IBulkInsert iBulkInsert = getBulkInsertInstance();
            int intEffectCount = 0;
            try {
                intEffectCount = iBulkInsert.fastImport(list);
            } catch (BulkInsertException ex) {
                intEffectCount = ex.getCommitRowCount();
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
                return null;
            } catch (Exception ex) {
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
                return null;
            }
        }
        return resultObject;
    }


    /**
     * 获取导入的各种类型数据
     *
     * @param tempOwner
     * @param tempFlag
     * @return
     */
    private List<TmpDaikinCollectionHead> selectDataList(String tempOwner, Integer tempFlag) {
        Map<String, Object> param = new HashMap<>(2);
        param.put("TEMP_OWNER", tempOwner);
        List flags = new ArrayList();
        flags.add(tempFlag);
        param.put("TEMP_FLAG", flags);
        return tmpDaikinCollectionHeadMapper.selectByFlag(param);
    }

}
