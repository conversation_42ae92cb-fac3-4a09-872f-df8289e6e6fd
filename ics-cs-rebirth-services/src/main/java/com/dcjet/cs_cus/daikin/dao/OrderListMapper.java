package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs.dto_cus.daikin.OrderListDto;
import com.dcjet.cs.dto_cus.daikin.OrderListParam;
import com.dcjet.cs_cus.daikin.model.OrderList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate dcits
 * OrderList
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
public interface OrderListMapper extends Mapper<OrderList> {
    /**
     * 根据参数查询
     *
     * @param orderList
     * @return
     */
    List<OrderList> getList(OrderList orderList);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据表头headId批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);

    /**
     * 更新已出口数量及订单剩余数
     */
    Map<String, String> updateExportQty(Map<String, Object> param);

    /**
     * 更新申报数量
     */
    void updateOrderQty(String tradeCode);

    /**
     * 报表查询
     */
    List<OrderListDto> getReport(OrderListParam orderListParam);

    int selectDataCount(OrderListParam orderListParam);

    List<OrderList> selectBySids(@Param("sids") List<String> sids, @Param("tradeCode") String tradeCode);

    @Select(value = "select distinct type from t_cgw_daikin_order_head where trade_code = #{tradeCode}")
    public List<String> getAllTypes(String tradeCode);
}
