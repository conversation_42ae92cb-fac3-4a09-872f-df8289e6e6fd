package com.dcjet.cs_cus.daikin.service;


import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.dto_cus.daikin.OrderHeadListImportParam;
import com.dcjet.cs.mat.dao.MatImgexgMapper;
import com.dcjet.cs.mat.dao.MatNonSuitRelMapper;
import com.dcjet.cs.mat.model.MatNonSuitRel;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs_cus.daikin.dao.OrderHeadMapper;
import com.dcjet.cs_cus.daikin.dao.OrderListMapper;
import com.dcjet.cs_cus.daikin.dao.OrderPriceMapper;
import com.dcjet.cs_cus.daikin.model.OrderHead;
import com.dcjet.cs_cus.daikin.model.OrderList;
import com.dcjet.cs_cus.daikin.model.OrderPrice;
import com.xdo.common.util.DateUtils;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderHeadImportService implements ImportHandlerInterface {

    @Resource
    private OrderListMapper orderListMapper;
    @Resource
    private OrderHeadMapper orderHeadMapper;
    @Resource
    private OrderPriceMapper orderPriceMapper;
    @Resource
    private MatImgexgMapper matImgexgMapper;
    @Resource
    private MatNonSuitRelMapper matNonSuitRelMapper;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;

    @Override
    public String getTaskCode() {
        return "ORDER_HEAD_LIST";
    }

    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        //从自定义参数中获取需要的数据
        if (!mapBusinessParam.containsKey("insertUser") || !mapBusinessParam.containsKey("insertUserName")) {
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        XdoImportLogger.log("共获取实体：" + objectList.size());

        List<OrderHeadListImportParam> orderHeadListImportParams = new ArrayList<>();
        objectList.forEach(e -> {
            OrderHeadListImportParam orderHeadListImportParam = (OrderHeadListImportParam) e;
            orderHeadListImportParams.add(orderHeadListImportParam);
        });
        excelImportDtoList.add(checkData(orderHeadListImportParams, tradeCode));

        return excelImportDtoList;
    }

    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));

        try {
            List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
            List<OrderHeadListImportParam> orderHeadListImportParams = objectList.stream().map(e -> {
                return (OrderHeadListImportParam) e;
            }).collect(Collectors.toList());

            Map<String, List<OrderHeadListImportParam>> maps = orderHeadListImportParams.stream().collect(Collectors.groupingBy(e -> e.getManageOrderNo()));
            String tradeCode = mapBasicParam.get("tradeCode").toString();
            String insertUser = mapBusinessParam.get("insertUser").toString();
            String insertUserName = mapBasicParam.get("insertUserName").toString();
            Date insertTime = new Date();

            for (String key : maps.keySet()) {
                //处理表头数据
                OrderHead orderHead = new OrderHead();
                OrderHeadListImportParam orderHeadListImportParam = maps.get(key).get(0);
                Example example = new Example(OrderHead.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("tradeCode", tradeCode);
                criteria.andEqualTo("manageOrderNo", orderHeadListImportParam.getManageOrderNo());
                List<OrderHead> orderHeads = orderHeadMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(orderHeads)) {
                    orderHeadMapper.delete(orderHeads.get(0));
                    orderListMapper.deleteByHeadIds(Arrays.asList(orderHeads.get(0).getSid()));
                }
                orderHead.setSid(UUID.randomUUID().toString());
                orderHead.setInsertUser(insertUser);
                orderHead.setInsertTime(insertTime);
                orderHead.setInsertUserName(insertUserName);
                orderHead.setTradeCode(tradeCode);
                orderHead.setManageOrderNo(key);
                orderHead.setCustomerCode(orderHeadListImportParam.getCustomerCode());
                List<BiClientInformation> biClientInformations = biClientInformationMapper.findByCodeAndType(tradeCode, maps.get(key).get(0).getCustomerCode(), Arrays.asList(CommonVariable.CLI));
                if (CollectionUtils.isNotEmpty(biClientInformations)) {
                    orderHead.setCustomerName(biClientInformations.get(0).getCompanyName());
                }
                if (orderHeadListImportParam.getReceiveDate()!=null) {
                    orderHead.setReceiveDate(orderHeadListImportParam.getReceiveDate());
                }
                orderHead.setType(orderHeadListImportParam.getType());
                orderHead.setDestName(orderHeadListImportParam.getDestName());
                orderHead.setTrafModeName(orderHeadListImportParam.getTrafModeName());
                orderHeadMapper.insert(orderHead);
                //处理表体数据
                List<OrderHeadListImportParam> params = maps.get(key);
                params.forEach(e -> {
                    MatNonSuitRel matNonSuitRel = new MatNonSuitRel() {{
                        setTradeCode(tradeCode);
                        setCopSuitNo(e.getOrderFacGNo());
                    }};
                    List<MatNonSuitRel> matNonSuitRels = matNonSuitRelMapper.select(matNonSuitRel);
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    //将料号转成小料号，便于取值
                    if (CollectionUtils.isNotEmpty(matNonSuitRels)) {
                        matNonSuitRels.forEach(rels -> {
                            Map<String, Object> map = new HashMap<>();
                            map.put("facGNo", rels.getCopGNo());
                            map.put("orderFacGNo", e.getOrderFacGNo());
                            map.put("qty", e.getQty());
                            map.put("orderQty", e.getQty().multiply(rels.getQty()).setScale(5, BigDecimal.ROUND_HALF_UP));
                            mapList.add(map);
                        });
                    } else {
                        Map<String, Object> map = new HashMap<>();
                        map.put("facGNo", e.getOrderFacGNo());
                        map.put("orderFacGNo", e.getOrderFacGNo());
                        map.put("qty", e.getQty());
                        map.put("orderQty", e.getQty());
                        mapList.add(map);
                    }
                    mapList.forEach(data -> {
                        OrderList orderList = new OrderList();
                        orderList.setSid(UUID.randomUUID().toString());
                        orderList.setHeadId(orderHead.getSid());
                        orderList.setTradeCode(tradeCode);
                        orderList.setInsertUser(insertUser);
                        orderList.setInsertTime(insertTime);
                        orderList.setInsertUserName(insertUserName);
                        orderList.setManageOrderNo(e.getManageOrderNo());
                        orderList.setCustomerOrderNo(e.getCustomerOrderNo());
                        orderList.setCustomerFacGNo(e.getCustomerFacGNo());
                        if (data.containsKey("facGNo") && data.get("facGNo") != null){
                            orderList.setFacGNo((String) data.get("facGNo"));
                        }
                        if (data.containsKey("orderFacGNo") && data.get("orderFacGNo") != null){
                            orderList.setOrderFacGNo((String) data.get("orderFacGNo"));
                        }
                        orderList.setCopGNameEn(e.getCopGNameEn());
                        orderList.setInternalOrderNo(e.getInternalOrderNo());
                        if (data.containsKey("orderQty") && data.get("orderQty") != null){
                            orderList.setOrderQty(new BigDecimal(data.get("orderQty").toString()));
                        }
                        if (data.containsKey("qty") && data.get("qty") != null){
                            orderList.setQty(new BigDecimal(data.get("qty").toString()));
                        }
                        if (e.getEtd()!=null){
                            orderList.setEtd(e.getEtd());
                        }
                        if (e.getChangeEtd()!=null){
                            orderList.setChangeEtd(e.getChangeEtd());
                        }
                        if (e.getLastEtd()!=null){
                            orderList.setLastEtd(e.getLastEtd());
                        }
                        orderList.setDecPrice(e.getDecPrice());
                        orderList.setCurr(e.getCurr());
                        if (orderList.getLastEtd() == null) {
                            if (orderList.getChangeEtd() != null) {
                                orderList.setLastEtd(orderList.getChangeEtd());
                            } else {
                                orderList.setLastEtd(orderList.getEtd());
                            }
                        }
                        orderList.setSurplusQty(orderList.getOrderQty());
                        orderList.setNote(e.getNote());
                        if (StringUtils.isEmpty(e.getCurr()) || e.getDecPrice() == null) {
                            OrderPrice orderPrice = new OrderPrice();
                            orderPrice.setTradeCode(tradeCode);
                            orderPrice.setFacGNoStr(data.get("facGNo").toString());
                            orderPrice.setCustomerCode(e.getCustomerCode());
                            orderPrice.setNowDate(DateUtils.dateToString(new Date()));
                            List<OrderPrice> prices = orderPriceMapper.getList(orderPrice);
                            if (StringUtils.isEmpty(e.getCurr())) {
                                orderList.setCurr(prices.get(0).getCurr());
                            }
                            if (e.getDecPrice() == null) {
                                orderList.setDecPrice(prices.get(0).getDecPrice());
                            }
                        }
                        orderListMapper.insert(orderList);
                    });
                });
            }
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage(e.getMessage());
        }

        return resultObject;
    }

    private ExcelImportDto<OrderHeadListImportParam> checkData(List<OrderHeadListImportParam> orderHeadListImportParams, String tradeCode) {
        ExcelImportDto<OrderHeadListImportParam> dto = new ExcelImportDto<>();
        Map<String, List<OrderHeadListImportParam>> maps = orderHeadListImportParams.stream().collect(Collectors.groupingBy(e -> e.getManageOrderNo() + "|" + e.getCustomerOrderNo() + "|" + e.getOrderFacGNo()));
        // 用来存放正确的对象的
        List<OrderHeadListImportParam> correctList = new ArrayList<>();
        // 用来存放错误的对象
        List<OrderHeadListImportParam> wrongList = new ArrayList<>();
        orderHeadListImportParams.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getManageOrderNo()) && StringUtils.isNotEmpty(e.getCustomerOrderNo()) && StringUtils.isNotEmpty(e.getOrderFacGNo())) {
                //当前导入数据重复性校验
                String key = e.getManageOrderNo() + "|" + e.getCustomerOrderNo() + "|" + e.getOrderFacGNo();
                if (maps.get(key).size() > 1) {
                    e.setTempFlag(1);
                    e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("本次导入数据中存在重复数据|"));
                }
            }

            //校验是否是套件料号
            MatNonSuitRel matNonSuitRel = new MatNonSuitRel() {{
                setTradeCode(tradeCode);
                setCopSuitNo(e.getOrderFacGNo());
            }};
            List<MatNonSuitRel> matNonSuitRels = matNonSuitRelMapper.select(matNonSuitRel);
            List<Map<String, String>> checkMaps = new ArrayList<>();
            boolean isSuitRel = false;
            //将料号转成小料号，便于校验
            if (CollectionUtils.isNotEmpty(matNonSuitRels)) {
                isSuitRel = true;
                matNonSuitRels.forEach(rels -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("facGNo", rels.getCopGNo());
                    checkMaps.add(map);
                });
            } else {
                Map<String, String> map = new HashMap<>();
                map.put("facGNo", e.getOrderFacGNo());
                checkMaps.add(map);
            }
            //所有的料号均需要校验单价或币制是否存在
            if (StringUtils.isEmpty(e.getCurr()) || e.getDecPrice() == null) {
                boolean isExistPrice = checkMaps.stream().anyMatch(p -> {
                    OrderPrice orderPrice = new OrderPrice() {{
                        setTradeCode(tradeCode);
                        setCustomerCode(e.getCustomerCode());
                        setFacGNoStr(p.get("facGNo"));
                        setNowDate(DateUtils.dateToString(new Date()));
                    }};
                    List<OrderPrice> orderPrices = orderPriceMapper.getList(orderPrice);
                    return CollectionUtils.isEmpty(orderPrices);
                });
                if (isExistPrice) {
                    e.setTempFlag(1);
                    e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("未在单价维护中找到对应币制或单价|"));
                }
            }

            //套件对应小料号一定存在，无需校验，仅校验不是套件的料号
            if (!isSuitRel && StringUtils.isNotBlank(e.getCustomerCode())) {
                checkMaps.forEach(m -> {
                    //检验料号是否存在
                    Map<String, Object> matParam = new HashMap<>();
                    matParam.put("tradeCode", tradeCode);
                    matParam.put("GMark", CommonEnum.GMarkBondEnum.EXG.getCode());
                    matParam.put("facGNo", m.get("facGNo"));
                    matParam.put("copGNo", m.get("facGNo"));
                    int bondCount = matImgexgMapper.getBond(matParam);
                    int noBondCount = matImgexgMapper.getNoBond(matParam);
                    if (bondCount == 0 && noBondCount == 0) {
                        e.setTempFlag(1);
                        e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("订单图号在套件管理/物料中心不存在|"));
                    }
                });
            }

            //校验客户是否存在
            if (StringUtils.isNotBlank(e.getCustomerCode())) {
                List<BiClientInformation> biClientInformations = biClientInformationMapper.findByCodeAndType(tradeCode, e.getCustomerCode(), Arrays.asList(CommonVariable.CLI));
                if (CollectionUtils.isEmpty(biClientInformations)) {
                    e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("客户代码不存在|"));
                    e.setTempFlag(1);
                }
            }

            //校验订单数必须大于0
            if (e.getQty() != null && e.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                e.setTempFlag(1);
                e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("订单数必须大于0|"));
            }

            if (e.getTempFlag() == 1) {
                wrongList.add(e);
            } else {
                correctList.add(e);
            }
        });

        dto.setCorrectList(correctList);
        dto.setCorrectNumber(correctList.size());
        dto.setWrongList(wrongList);
        dto.setWrongNumber(wrongList.size());
        return dto;
    }
}

