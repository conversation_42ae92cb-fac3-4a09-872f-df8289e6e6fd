package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.dto_cus.daikin.CustomerPaymentDto;
import com.dcjet.cs.dto_cus.daikin.CustomerPaymentParam;
import com.dcjet.cs_cus.daikin.dao.CustomerPaymentMapper;
import com.dcjet.cs_cus.daikin.mapper.CustomerPaymentDtoMapper;
import com.dcjet.cs_cus.daikin.model.CustomerPayment;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Service
public class CustomerPaymentService extends BaseService<CustomerPayment> {
    @Resource
    private CustomerPaymentMapper customerPaymentMapper;
    @Resource
    private CustomerPaymentDtoMapper customerPaymentDtoMapper;

    @Override
    public Mapper<CustomerPayment> getMapper() {
        return customerPaymentMapper;
    }

    /**
     * 获取分页信息
     *
     * @param customerPaymentParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<CustomerPaymentDto>> getListPaged(CustomerPaymentParam customerPaymentParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        CustomerPayment customerPayment = customerPaymentDtoMapper.toPo(customerPaymentParam);
        customerPayment.setTradeCode(userInfo.getCompany());
        Page<CustomerPayment> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> customerPaymentMapper.getList(customerPayment));
        List<CustomerPaymentDto> customerPaymentDtos = page.getResult().stream().map(head -> {
            CustomerPaymentDto dto = customerPaymentDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CustomerPaymentDto>> paged = ResultObject.createInstance(customerPaymentDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param customerPaymentParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CustomerPaymentDto insert(CustomerPaymentParam customerPaymentParam, UserInfoToken userInfo) {
        CustomerPayment customerPayment = customerPaymentDtoMapper.toPo(customerPaymentParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        customerPayment.setSid(sid);
        customerPayment.setInsertUser(userInfo.getUserNo());
        customerPayment.setInsertUserName(userInfo.getUserName());
        customerPayment.setTradeCode(userInfo.getCompany());
        customerPayment.setInsertTime(new Date());
        //校验客户代码是否存在
        if (StringUtils.isBlank(customerPayment.getCustomerCode())){
            throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("客户代码不能为空"));
        }
        //客户代码校验唯一性
        boolean customerCode = chekcustomerCode(customerPayment.getCustomerCode(), userInfo.getCompany(), sid);
        if (customerCode) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("客户代码已存在"));
        }

        // 新增数据
        int insertStatus = customerPaymentMapper.insert(customerPayment);
        return insertStatus > 0 ? customerPaymentDtoMapper.toDto(customerPayment) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param customerPaymentParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CustomerPaymentDto update(CustomerPaymentParam customerPaymentParam, UserInfoToken userInfo) {
        CustomerPayment customerPayment = customerPaymentMapper.selectByPrimaryKey(customerPaymentParam.getSid());
        customerPaymentDtoMapper.updatePo(customerPaymentParam, customerPayment);
        customerPayment.setUpdateUser(userInfo.getUserNo());
        customerPayment.setUpdateUserName(userInfo.getUserName());
        customerPayment.setUpdateTime(new Date());
        //校验客户代码是否存在
        if (StringUtils.isBlank(customerPayment.getCustomerCode())){
            throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("客户代码不能为空"));
        }
        //客户代码校验唯一性
        boolean customerCode = chekcustomerCode(customerPayment.getCustomerCode(), userInfo.getCompany(), customerPayment.getSid());
        if (customerCode) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("客户代码已存在"));
        }
        // 更新数据
        int update = customerPaymentMapper.updateByPrimaryKey(customerPayment);
        return update > 0 ? customerPaymentDtoMapper.toDto(customerPayment) : null;
    }

    private boolean chekcustomerCode(String customerCode, String company, String sid) {
        Integer sum = customerPaymentMapper.chekcustomerCode(customerCode, company, sid);
        return sum > 0;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        customerPaymentMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CustomerPaymentDto> selectAll(CustomerPaymentParam exportParam, UserInfoToken userInfo) {
        CustomerPayment customerPayment = customerPaymentDtoMapper.toPo(exportParam);
        customerPayment.setTradeCode(userInfo.getCompany());
        List<CustomerPaymentDto> customerPaymentDtos = new ArrayList<>();
        List<CustomerPayment> customerPayments = customerPaymentMapper.getList(customerPayment);
        if (CollectionUtils.isNotEmpty(customerPayments)) {
            customerPaymentDtos = customerPayments.stream().map(head -> {
                CustomerPaymentDto dto = customerPaymentDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return customerPaymentDtos;
    }
}
