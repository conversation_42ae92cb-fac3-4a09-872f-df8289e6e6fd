package com.dcjet.cs_cus.daikin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Setter
@Getter
@Table(name = "T_CGW_DAIKIN_ORDER_PRICE")
public class OrderPrice implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 客户代码
     */
    @Column(name = "CUSTOMER_CODE")
    private String customerCode;
    /**
     * 客户中文名称
     */
    @Column(name = "CUSTOMER_NAME")
    private String customerName;
    /**
     * 图号（即企业成品料号）
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 单价
     */
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;
    /**
     * 有效期起始日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "BEGIN_DATE")
    private Date beginDate;
    /**
     * 有效期截至日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "END_DATE")
    private Date endDate;
    /**
     * 报价单号
     */
    @Column(name = "QUOTATION_NO")
    private String quotationNo;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    @Transient
    private String nowDate;
    @Transient
    private Integer tempFlag;
    @Transient
    private String tempRemark;
    @Transient
    private String beginDateStr;
    @Transient
    private String endDateStr;
    //精确查询使用字段
    @Transient
    private String facGNoStr;
}
