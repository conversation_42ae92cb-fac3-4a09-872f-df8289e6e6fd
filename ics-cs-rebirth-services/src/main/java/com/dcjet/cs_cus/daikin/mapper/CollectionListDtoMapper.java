package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.CollectionListDto;
import com.dcjet.cs.dto_cus.daikin.CollectionListParam;
import com.dcjet.cs_cus.daikin.model.CollectionList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CollectionListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CollectionListDto toDto(CollectionList po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CollectionList toPo(CollectionListParam param);

    /**
     * 数据库原始数据更新
     *
     * @param collectionListParam
     * @param collectionList
     */
    void updatePo(CollectionListParam collectionListParam, @MappingTarget CollectionList collectionList);

    default void patchPo(CollectionListParam collectionListParam, CollectionList collectionList) {
        // TODO 自行实现局部更新
    }
}
