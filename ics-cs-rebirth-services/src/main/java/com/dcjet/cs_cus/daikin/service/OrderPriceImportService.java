package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs_cus.daikin.dao.OrderPriceMapper;
import com.dcjet.cs_cus.daikin.model.OrderPrice;
import com.google.common.base.Strings;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderPriceImportService implements ImportHandlerInterface {

    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    @Resource
    private OrderPriceMapper orderPriceMapper;

    @Override
    public String getTaskCode() {
        return "ORDER_PRICE";
    }

    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        //从自定义参数中获取需要的数据
        if (!mapBusinessParam.containsKey("insertUser") || !mapBusinessParam.containsKey("insertUserName")) {
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        XdoImportLogger.log("共获取实体：" + objectList.size());

        List<OrderPrice> orderPrices = new ArrayList<>();
        objectList.forEach(e -> {
            OrderPrice orderPrice = (OrderPrice) e;
            orderPrice.setSid(UUID.randomUUID().toString());
            orderPrices.add(orderPrice);
        });

        Set<String> repeatData = new HashSet<>();
        Map<String, List<OrderPrice>> map = orderPrices.stream().collect(Collectors.groupingBy(e -> e.getCustomerCode() + "|" + e.getFacGNo()));
        for (String key : map.keySet()) {
            List<OrderPrice> orderList = map.get(key);
            if (orderList.size() > 1) {
                for (int i = 0; i < orderList.size()-1; i++) {
                    Long beginDate = orderList.get(i).getBeginDate().getTime();
                    Long endDate = orderList.get(i).getEndDate().getTime();
                    for (int j = i + 1; j < orderList.size(); j++) {
                        Long bDate = orderList.get(j).getBeginDate().getTime();
                        Long eDate = orderList.get(j).getEndDate().getTime();
                        if ((beginDate >= bDate && beginDate <= eDate)
                                || (endDate >= bDate && endDate <= eDate)
                                || (beginDate <= bDate && endDate >= eDate)) {
                            repeatData.add(orderList.get(i).getSid());
                            repeatData.add(orderList.get(j).getSid());
                        }
                    }
                }
            }
        }
        excelImportDtoList.add(checkData(orderPrices, tradeCode, repeatData));

        return excelImportDtoList;
    }

    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));

        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();
        Date date = new Date();
        for (int i = 0; i < objectList.size(); i++) {
            OrderPrice orderPrice = (OrderPrice) objectList.get(i);
            List<BiClientInformation> biClientInformations = biClientInformationMapper.findByCodeAndType(tradeCode, orderPrice.getCustomerCode(), Arrays.asList(CommonVariable.CLI));
            if (CollectionUtils.isNotEmpty(biClientInformations)) {
                orderPrice.setCustomerName(biClientInformations.get(0).getCompanyName());
            }
            Example example = new Example(OrderPrice.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("customerCode",orderPrice.getCustomerCode());
            criteria.andEqualTo("facGNo",orderPrice.getFacGNo());
            criteria.andEqualTo("beginDate",orderPrice.getBeginDate());
            criteria.andEqualTo("endDate",orderPrice.getEndDate());
            List<OrderPrice> res = orderPriceMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(res)){
                OrderPrice op = res.get(0);
                op.setDecPrice(orderPrice.getDecPrice());
                op.setCurr(orderPrice.getCurr());
                op.setQuotationNo(orderPrice.getQuotationNo());
                op.setNote(orderPrice.getNote());
                op.setUpdateUserName(insertUserName);
                op.setUpdateUser(insertUser);
                op.setUpdateTime(new Date());
                orderPriceMapper.updateByPrimaryKey(op);
            }else{
                orderPrice.setInsertUser(insertUser);
                orderPrice.setInsertTime(date);
                orderPrice.setSid(UUID.randomUUID().toString());
                orderPrice.setInsertUserName(insertUserName);
                orderPrice.setTradeCode(tradeCode);
                orderPriceMapper.insert(orderPrice);
            }


        }

        return resultObject;
    }

    private ExcelImportDto<OrderPrice> checkData(List<OrderPrice> orderPrices, String tradeCode, Set<String> repeatData) {
        ExcelImportDto<OrderPrice> dto = new ExcelImportDto<>();
        // 用来存放正确的对象的
        List<OrderPrice> correctList = new ArrayList<>();
        // 用来存放错误的对象
        List<OrderPrice> wrongList = new ArrayList<>();
        orderPrices.forEach(e -> {
            //校验有效开始日期和有效结束日期
            if (e.getBeginDate() != null && e.getEndDate() != null && e.getBeginDate().getTime() > e.getEndDate().getTime()) {
                e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("有效期起始日不能大于有效期截至日|"));
                e.setTempFlag(1);
            }
            //本批次数据重复性校验
            if (repeatData.contains(e.getSid())) {
                e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("客户代码和企业料号下本次导入数据有效日期有重叠|"));
                e.setTempFlag(1);
            }
            //校验币制
            if (StringUtils.isNotBlank(e.getCurr())) {
                String curr = pCodeHolder.getValue(PCodeType.CURR_OUTDATED, e.getCurr());
                if (Strings.isNullOrEmpty(curr)) {
                    e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("币制不存在|"));
                    e.setTempFlag(1);
                }
            }
            //校验客户代码
            if (StringUtils.isNotBlank(e.getCustomerCode())) {
                List<BiClientInformation> biClientInformations = biClientInformationMapper.findByCodeAndType(tradeCode, e.getCustomerCode(), Arrays.asList(CommonVariable.CLI));
                if (CollectionUtils.isEmpty(biClientInformations)) {
                    e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("客户代码不存在|"));
                    e.setTempFlag(1);
                }
            }
            //数据库中是否已存在
            if(StringUtils.isNotBlank(e.getCustomerCode()) && StringUtils.isNotBlank(e.getFacGNo()) && e.getBeginDate() != null && e.getEndDate() != null){
                e.setTradeCode(tradeCode);
                int isPerfectMatch = orderPriceMapper.checkPerfectMatch(e);
                if(isPerfectMatch == 0){
                    List<String> repeatSid = orderPriceMapper.checkRepeat(e);
                    if (CollectionUtils.isNotEmpty(repeatSid)) {
                        e.setTempRemark(e.getTempRemark() + xdoi18n.XdoI18nUtil.t("与数据库中时间区间有重叠|"));
                        e.setTempFlag(1);
                    }
                }
            }

            if (e.getTempFlag() == 1) {
                wrongList.add(e);
            } else {
                correctList.add(e);
            }
        });

        dto.setCorrectList(correctList);
        dto.setCorrectNumber(correctList.size());
        dto.setWrongList(wrongList);
        dto.setWrongNumber(wrongList.size());
        return dto;
    }
}
