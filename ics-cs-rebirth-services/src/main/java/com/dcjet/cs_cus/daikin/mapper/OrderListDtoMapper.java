package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.OrderListDto;
import com.dcjet.cs.dto_cus.daikin.OrderListParam;
import com.dcjet.cs_cus.daikin.model.OrderList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderListDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    OrderListDto toDto(OrderList po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    OrderList toPo(OrderListParam param);

    void updatePo(OrderListParam orderListParam, @MappingTarget OrderList orderList);
}
