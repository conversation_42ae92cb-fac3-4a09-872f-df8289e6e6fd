package com.dcjet.cs_cus.daikin.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Setter
@Getter
@Table(name = "T_CGW_DAIKIN_COLLECTION_LIST")
public class CollectionList extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表头SID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 发票号
     */
    @Column(name = "INVOICE_NO")
    private String invoiceNo;
    /**
     * 单据内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 客户订单号
     */
    @Column(name = "CUSTOMER_ORDER_NO")
    private String customerOrderNo;
    /**
     * 成品料号
     */
    @Column(name = "FAC_EXG_NO")
    private String facExgNo;
    /**
     * 数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    /**
     * 单价
     */
    @Column(name = "PRICE")
    private BigDecimal price;
    /**
     * 总价
     */
    @Column(name = "TOTAL_PRICE")
    private BigDecimal totalPrice;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;

}
