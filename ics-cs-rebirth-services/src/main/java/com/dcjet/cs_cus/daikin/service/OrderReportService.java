package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto_cus.daikin.OrderListDto;
import com.dcjet.cs.dto_cus.daikin.OrderListParam;
import com.dcjet.cs_cus.daikin.dao.OrderListMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 订单管理报表异步导出
 * @author: WJ
 * @createDate: 2021/9/1 13:50
 */
@Component
public class OrderReportService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    private final String taskName = xdoi18n.XdoI18nUtil.t("订单管理报表异步导出(DAIKIN_ORDER_REPORT)");

    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private OrderListMapper orderListMapper;

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("DAIKIN_ORDER_REPORT");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        OrderListParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Integer count = orderListMapper.selectDataCount(exportParam);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        OrderListParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Page<OrderListDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> orderListMapper.getReport(exportParam));

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(page.getResult()));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    private List<OrderListDto> convertForPrint(List<OrderListDto> list) {
        for (OrderListDto item : list) {
            if (StringUtils.isNotBlank(item.getCustomerCode()) && StringUtils.isNotBlank(item.getCustomerName())) {
                item.setCustomerCode(item.getCustomerCode() + " " + item.getCustomerName());
            }
            if (StringUtils.isNotBlank(item.getCurr())) {
                item.setCurr(item.getCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
            }
        }
        return list;
    }

    private OrderListParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        OrderListParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, OrderListParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
