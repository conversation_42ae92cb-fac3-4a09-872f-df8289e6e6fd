package com.dcjet.cs_cus.daikin.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Setter
@Getter
@Table(name = "T_CGW_DAIKIN_CUSTOMER_PAYMENT")
public class CustomerPayment extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户代码
     */
    @Column(name = "CUSTOMER_CODE")
    private String customerCode;
    /**
     * 客户名称
     */
    @Column(name = "COMPANY_NAME")
    private String companyName;
    /**
     * 结算月份
     */
    @Column(name = "SETTLEMENT_YEAR")
    private String settlementYear;
    /**
     * 结算日期
     */
    @Column(name = "SETTLEMENT_DATE")
    private String settlementDate;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

}
