package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.dto_cus.daikin.CollectionHeadDto;
import com.dcjet.cs.dto_cus.daikin.CollectionHeadParam;
import com.dcjet.cs_cus.daikin.dao.CollectionHeadMapper;
import com.dcjet.cs_cus.daikin.mapper.CollectionHeadDtoMapper;
import com.dcjet.cs_cus.daikin.model.CollectionHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Service
public class CollectionHeadService extends BaseService<CollectionHead> {
    @Resource
    private CollectionHeadMapper collectionHeadMapper;
    @Resource
    private CollectionHeadDtoMapper collectionHeadDtoMapper;

    @Override
    public Mapper<CollectionHead> getMapper() {
        return collectionHeadMapper;
    }

    /**
     * 获取分页信息
     *
     * @param collectionHeadParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<CollectionHeadDto>> getListPaged(CollectionHeadParam collectionHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        CollectionHead collectionHead = collectionHeadDtoMapper.toPo(collectionHeadParam);
        collectionHead.setTradeCode(userInfo.getCompany());
        Page<CollectionHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> collectionHeadMapper.getList(collectionHead));
        List<CollectionHeadDto> collectionManagementHeadDtos = page.getResult().stream().map(head -> {
            CollectionHeadDto dto = collectionHeadDtoMapper.toDto(head);
//            dto = checkDate(dto);
            if (StringUtils.isNotBlank(dto.getSettlementDate())&&StringUtils.isNotBlank(dto.getSettlementYear()) && dto.getAtdAcceptanceDate() != null) {
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                Calendar c = Calendar.getInstance();
                //当月最后一天
                Calendar calendar2 = Calendar.getInstance();

                c.setTime(dto.getAtdAcceptanceDate());
                //增加月份
                c.add(Calendar.MONTH, Integer.parseInt(dto.getSettlementYear()));

                c.set(Calendar.DATE, 1);        //设置为该月第一天
                calendar2.setTime(c.getTime());
                calendar2.add(Calendar.MONTH, 1);
                calendar2.add(Calendar.DATE, -1);    //再减一天即为上个月最后一天

                c.add(Calendar.DAY_OF_MONTH,Integer.parseInt(dto.getSettlementDate())-1);
                String date = sf.format(c.getTime());
                Date dated = c.getTime();

                String finallyDay = sf.format(calendar2.getTime());
                Date dayDate = calendar2.getTime();

                //比较日期大小
                int dates = date.compareTo(finallyDay);
                if (dates > 0) {
                    dto.setDepositDeadlineDate(dayDate);
                } else {
                    dto.setDepositDeadlineDate(dated);
                }
            }

            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CollectionHeadDto>> paged = ResultObject.createInstance(collectionManagementHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param collectionHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CollectionHeadDto insert(CollectionHeadParam collectionHeadParam, UserInfoToken userInfo) {
        CollectionHead collectionHead = collectionHeadDtoMapper.toPo(collectionHeadParam);
        /**
         * 规范固定字段
         */
//        String sid = UUID.randomUUID().toString();
//        collectionHead.setSid(sid);
        collectionHead.setInsertUser(userInfo.getUserNo());
        collectionHead.setInsertUserName(userInfo.getUserName());
        collectionHead.setTradeCode(userInfo.getCompany());
        collectionHead.setInsertTime(collectionHeadParam.getInsertTime());
        // 新增数据
        int insertStatus = collectionHeadMapper.insert(collectionHead);
        return insertStatus > 0 ? collectionHeadDtoMapper.toDto(collectionHead) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param collectionHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CollectionHeadDto update(CollectionHeadParam collectionHeadParam, UserInfoToken userInfo) {

        CollectionHeadDto collectionHeadDto = null;
        //判断此条数据是否存在
        boolean exist = chekExist(collectionHeadParam.getSid(), collectionHeadParam.getEmsListNo(), userInfo.getCompany());
        if (!exist) {
            //不存在执行新增
            collectionHeadDto = insert(collectionHeadParam, userInfo);
        } else {
            //存在执行更新
            CollectionHead collectionHead = collectionHeadMapper.selectByPrimaryKey(collectionHeadParam.getSid());
            collectionHeadDtoMapper.updatePo(collectionHeadParam, collectionHead);

            collectionHead.setUpdateUser(userInfo.getUserNo());
            collectionHead.setUpdateUserName(userInfo.getUserName());
            collectionHead.setUpdateTime(new Date());
            // 更新数据
            int update = collectionHeadMapper.updateByPrimaryKey(collectionHead);
            collectionHeadDto = update > 0 ? collectionHeadDtoMapper.toDto(collectionHead) : null;
        }
        return collectionHeadDto;
    }


    private boolean chekExist(String sid, String emsListNo, String company) {
        Integer sum = collectionHeadMapper.chekExist(sid,emsListNo,company);
        return sum>0;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        collectionHeadMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CollectionHeadDto> selectAll(CollectionHeadParam exportParam, UserInfoToken userInfo) {
        CollectionHead collectionHead = collectionHeadDtoMapper.toPo(exportParam);
        collectionHead.setTradeCode(userInfo.getCompany());
        List<CollectionHeadDto> collectionHeadDtos = new ArrayList<>();
        List<CollectionHead> collectionHeads = collectionHeadMapper.getList(collectionHead);
        if (CollectionUtils.isNotEmpty(collectionHeads)) {
            collectionHeadDtos = collectionHeads.stream().map(head -> {
                CollectionHeadDto dto = collectionHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return collectionHeadDtos;
    }
}
