package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs_cus.daikin.model.CollectionList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * CollectionManagementList
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
public interface CollectionListMapper extends Mapper<CollectionList> {
    /**
     * 查询获取数据
     *
     * @param collectionList
     * @return
     */
    List<CollectionList> getList(CollectionList collectionList);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
