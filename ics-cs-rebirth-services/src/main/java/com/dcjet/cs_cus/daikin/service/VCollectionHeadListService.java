package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.dto_cus.daikin.VCollectionHeadListDto;
import com.dcjet.cs.dto_cus.daikin.VCollectionHeadListParam;
import com.dcjet.cs_cus.daikin.dao.VCollectionHeadListMapper;
import com.dcjet.cs_cus.daikin.mapper.VCollectionHeadListDtoMapper;
import com.dcjet.cs_cus.daikin.model.VCollectionHeadList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Service
public class VCollectionHeadListService extends BaseService<VCollectionHeadList> {
    @Resource
    private VCollectionHeadListMapper vCollectionHeadListMapper;
    @Resource
    private VCollectionHeadListDtoMapper vCollectionHeadListDtoMapper;

    @Override
    public Mapper<VCollectionHeadList> getMapper() {
        return vCollectionHeadListMapper;
    }

    /**
     * 获取分页信息``
     *
     * @param vCollectionHeadListParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<VCollectionHeadListDto>> getListPaged(VCollectionHeadListParam vCollectionHeadListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VCollectionHeadList vCollectionHeadList = vCollectionHeadListDtoMapper.toPo(vCollectionHeadListParam);
        vCollectionHeadList.setTradeCode(userInfo.getCompany());
        Page<VCollectionHeadList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vCollectionHeadListMapper.getList(vCollectionHeadList));
        List<VCollectionHeadListDto> vCollectionHeadListDtos = page.getResult().stream().map(head -> {
            VCollectionHeadListDto dto = vCollectionHeadListDtoMapper.toDto(head);
            if (StringUtils.isNotBlank(dto.getSettlementDate())&&StringUtils.isNotBlank(dto.getSettlementYear()) && dto.getAtdAcceptanceDate() != null) {
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
                Calendar c = Calendar.getInstance();
                //当月最后一天
                Calendar calendar2 = Calendar.getInstance();

                c.setTime(dto.getAtdAcceptanceDate());
                //增加月份
                c.add(Calendar.MONTH, Integer.parseInt(dto.getSettlementYear()));

                c.set(Calendar.DATE, 1);        //设置为该月第一天
                calendar2.setTime(c.getTime());
                calendar2.add(Calendar.MONTH, 1);
                calendar2.add(Calendar.DATE, -1);    //再减一天即为上个月最后一天

                c.add(Calendar.DAY_OF_MONTH,Integer.parseInt(dto.getSettlementDate())-1);
                String date = sf.format(c.getTime());
                Date dated = c.getTime();

                String finallyDay = sf.format(calendar2.getTime());
                Date dayDate = calendar2.getTime();

                //比较日期大小
                int dates = date.compareTo(finallyDay);
                if (dates > 0) {
                    dto.setDepositDeadlineDate(dayDate);
                } else {
                    dto.setDepositDeadlineDate(dated);
                }
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VCollectionHeadListDto>> paged = ResultObject.createInstance(vCollectionHeadListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VCollectionHeadListDto> selectAll(VCollectionHeadListParam exportParam, UserInfoToken userInfo) {
        VCollectionHeadList vCollectionHeadList = vCollectionHeadListDtoMapper.toPo(exportParam);
        vCollectionHeadList.setTradeCode(userInfo.getCompany());
        List<VCollectionHeadListDto> vCollectionHeadListDtos = new ArrayList<>();
        List<VCollectionHeadList> vCollectionHeadLists = vCollectionHeadListMapper.getList(vCollectionHeadList);
        if (CollectionUtils.isNotEmpty(vCollectionHeadLists)) {
            vCollectionHeadListDtos = vCollectionHeadLists.stream().map(head -> {
                VCollectionHeadListDto dto = vCollectionHeadListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return vCollectionHeadListDtos;
    }
}
