package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.dto_cus.daikin.OrderPriceDto;
import com.dcjet.cs.dto_cus.daikin.OrderPriceParam;
import com.dcjet.cs_cus.daikin.dao.OrderPriceMapper;
import com.dcjet.cs_cus.daikin.mapper.OrderPriceDtoMapper;
import com.dcjet.cs_cus.daikin.model.OrderPrice;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.common.util.DateUtils;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Service
public class OrderPriceService extends BaseService<OrderPrice> {
    @Resource
    private OrderPriceMapper orderPriceMapper;
    @Resource
    private OrderPriceDtoMapper orderPriceDtoMapper;

    @Override
    public Mapper<OrderPrice> getMapper() {
        return orderPriceMapper;
    }

    /**
     * 获取分页信息
     *
     * @param orderPriceParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<OrderPriceDto>> getListPaged(OrderPriceParam orderPriceParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        OrderPrice orderPrice = orderPriceDtoMapper.toPo(orderPriceParam);
        orderPrice.setTradeCode(userInfo.getCompany());
        Page<OrderPrice> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> orderPriceMapper.getList(orderPrice));
        List<OrderPriceDto> orderPriceDtos = page.getResult().stream().map(head -> {
            OrderPriceDto dto = orderPriceDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<OrderPriceDto>> paged = ResultObject.createInstance(orderPriceDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param orderPriceParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderPriceDto insert(OrderPriceParam orderPriceParam, UserInfoToken userInfo) {
        OrderPrice orderPrice = orderPriceDtoMapper.toPo(orderPriceParam);
        if (orderPriceParam.getBeginDate() != null && orderPriceParam.getEndDate() != null) {
            if (orderPriceParam.getBeginDate().getTime() > orderPriceParam.getEndDate().getTime()) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("有效期起始日不可大于有效期截至日!"));
            }
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        orderPrice.setSid(sid);
        orderPrice.setInsertUser(userInfo.getUserNo());
        orderPrice.setInsertTime(new Date());
        orderPrice.setTradeCode(userInfo.getCompany());
        orderPrice.setInsertUserName(userInfo.getUserName());
        List<String> repeatSid = orderPriceMapper.checkRepeat(orderPrice);
        if (CollectionUtils.isNotEmpty(repeatSid)) {
            throw new RuntimeException(xdoi18n.XdoI18nUtil.t("数据已存在或有效日期存在重叠部分，新增失败！"));
        }
        // 新增数据
        int insertStatus = orderPriceMapper.insert(orderPrice);
        return insertStatus > 0 ? orderPriceDtoMapper.toDto(orderPrice) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param orderPriceParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderPriceDto update(OrderPriceParam orderPriceParam, UserInfoToken userInfo) {
        OrderPrice orderPrice = orderPriceMapper.selectByPrimaryKey(orderPriceParam.getSid());
        if (orderPriceParam.getBeginDate() != null && orderPriceParam.getEndDate() != null) {
            if (orderPriceParam.getBeginDate().getTime() > orderPriceParam.getEndDate().getTime()) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("有效期起始日不可大于有效期截至日!"));
            }
        }
        orderPriceDtoMapper.updatePo(orderPriceParam, orderPrice);
        orderPrice.setUpdateUser(userInfo.getUserNo());
        orderPrice.setUpdateTime(new Date());
        orderPrice.setUpdateUserName(userInfo.getUserName());
        List<String> repeatSid = orderPriceMapper.checkRepeat(orderPrice);
        if (CollectionUtils.isNotEmpty(repeatSid) && repeatSid.stream().anyMatch(e -> !e.equals(orderPrice.getSid()))) {
            throw new RuntimeException(xdoi18n.XdoI18nUtil.t("数据有效日期存在重叠部分，更新失败！"));
        }
        // 更新数据
        int update = orderPriceMapper.updateByPrimaryKey(orderPrice);
        return update > 0 ? orderPriceDtoMapper.toDto(orderPrice) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        orderPriceMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<OrderPriceDto> selectAll(OrderPriceParam exportParam, UserInfoToken userInfo) {
        OrderPrice orderPrice = orderPriceDtoMapper.toPo(exportParam);
        orderPrice.setTradeCode(userInfo.getCompany());
        List<OrderPriceDto> orderPriceDtos = new ArrayList<>();
        List<OrderPrice> orderPrices = orderPriceMapper.getList(orderPrice);
        if (CollectionUtils.isNotEmpty(orderPrices)) {
            orderPriceDtos = orderPrices.stream().map(head -> {
                OrderPriceDto dto = orderPriceDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return orderPriceDtos;
    }
}
