package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.CollectionHeadDto;
import com.dcjet.cs.dto_cus.daikin.CollectionHeadParam;
import com.dcjet.cs_cus.daikin.model.CollectionHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CollectionHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CollectionHeadDto toDto(CollectionHead po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CollectionHead toPo(CollectionHeadParam param);

    /**
     * 数据库原始数据更新
     *
     * @param collectionHeadParam
     * @param collectionHead
     */
    void updatePo(CollectionHeadParam collectionHeadParam, @MappingTarget CollectionHead collectionHead);

    default void patchPo(CollectionHeadParam collectionHeadParam, CollectionHead collectionHead) {
        // TODO 自行实现局部更新
    }
}
