package com.dcjet.cs_cus.technimark.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportDto;
import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportParam;
import com.dcjet.cs_cus.technimark.dao.TechnimarkDzReportMapper;
import com.dcjet.cs_cus.technimark.mapper.TechnimarkDzReportDtoMapper;
import com.dcjet.cs_cus.technimark.model.TechnimarkDzReport;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 泰马克出口单证统计表异步导出
 * @author: WJ
 * @createDate: 2020/9/14 14:13
 */
@Component
public class ExportTechnimarkDzReportService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private TechnimarkDzReportMapper technimarkDzReportMapper;
    @Resource
    private TechnimarkDzReportDtoMapper technimarkDzReportDtoMapper;

    private final String taskName = xdoi18n.XdoI18nUtil.t("泰马克出口单证统计表异步导出(TMK_DOCUMENTS_REPORT)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("TMK_DOCUMENTS_REPORT");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        TechnimarkDzReportParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        TechnimarkDzReport technimarkDzReport = technimarkDzReportDtoMapper.toPo(exportParam);
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(technimarkDzReport));
        Integer count = technimarkDzReportMapper.selectDataCount(technimarkDzReport);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        TechnimarkDzReportParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        TechnimarkDzReport technimarkDzReport = technimarkDzReportDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(technimarkDzReport));
        Page<TechnimarkDzReport> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> technimarkDzReportMapper.getList(technimarkDzReport));

        List<TechnimarkDzReportDto> technimarkDzReportDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            technimarkDzReportDtos = page.getResult().stream().map(head -> {
                TechnimarkDzReportDto dto = technimarkDzReportDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }

        List<Object> list = new ArrayList<>();
        list.addAll(convertPrint(technimarkDzReportDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<TechnimarkDzReportDto> convertPrint(List<TechnimarkDzReportDto> list) {
        for (TechnimarkDzReportDto item : list) {
            if (StringUtils.isNotEmpty(item.getTrafMode())) {
                item.setTrafMode(item.getTrafMode() + " " + pCodeHolder.getValue(PCodeType.TRANSF, item.getTrafMode()));
            }
            if (StringUtils.isNotEmpty(item.getForwardCode()) && StringUtils.isNotEmpty(item.getForwardName())) {
                item.setForwardCode(item.getForwardCode() + " " + item.getForwardName());
            }
            if (StringUtils.isNotEmpty(item.getOverseasShipper()) && StringUtils.isNotEmpty(item.getOverseasShipperName())) {
                item.setOverseasShipper(item.getOverseasShipper() + " " + item.getOverseasShipperName());
            }
            if (StringUtils.isNotEmpty(item.getCurr())) {
                item.setCurr(item.getCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
            }
            if (StringUtils.isNotEmpty(item.getTradeMode())) {
                item.setTradeMode(item.getTradeMode() + " " + pCodeHolder.getValue(PCodeType.TRADE, item.getTradeMode()));
            }
            if (StringUtils.isNotEmpty(item.getTrafName()) && StringUtils.isNotEmpty(item.getVoyageNo())) {
                item.setVoyageNo(item.getTrafName() + " " + item.getVoyageNo());
            }
        }
        return list;
    }

    private TechnimarkDzReportParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        TechnimarkDzReportParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, TechnimarkDzReportParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
