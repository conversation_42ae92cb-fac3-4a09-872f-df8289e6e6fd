package com.dcjet.cs_cus.technimark.service;

import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportDto;
import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportParam;
import com.dcjet.cs_cus.technimark.dao.TechnimarkDzReportMapper;
import com.dcjet.cs_cus.technimark.mapper.TechnimarkDzReportDtoMapper;
import com.dcjet.cs_cus.technimark.model.TechnimarkDzReport;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-11-18
 */
@Service
public class TechnimarkDzReportService extends BaseService<TechnimarkDzReport> {
    @Resource
    private TechnimarkDzReportMapper technimarkDzReportMapper;
    @Resource
    private TechnimarkDzReportDtoMapper technimarkDzReportDtoMapper;

    @Override
    public Mapper<TechnimarkDzReport> getMapper() {
        return technimarkDzReportMapper;
    }

    /**
     * 获取分页信息
     *
     * @param technimarkDzReportParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<TechnimarkDzReportDto>> getListPaged(TechnimarkDzReportParam technimarkDzReportParam, PageParam pageParam) {
        // 启用分页查询
        TechnimarkDzReport technimarkDzReport = technimarkDzReportDtoMapper.toPo(technimarkDzReportParam);
        Page<TechnimarkDzReport> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> technimarkDzReportMapper.getList(technimarkDzReport));
        List<TechnimarkDzReportDto> technimarkDzReportDtos = page.getResult().stream().map(head -> {
            TechnimarkDzReportDto dto = technimarkDzReportDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<TechnimarkDzReportDto>> paged = ResultObject.createInstance(technimarkDzReportDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public void reportData(String tradeCode) {
        technimarkDzReportMapper.deleteReportData(tradeCode);
        technimarkDzReportMapper.insertReportData(tradeCode);
    }
}
