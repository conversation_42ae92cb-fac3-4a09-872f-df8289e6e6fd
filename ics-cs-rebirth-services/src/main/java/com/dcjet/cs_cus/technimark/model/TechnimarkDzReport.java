package com.dcjet.cs_cus.technimark.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-18
 */
@Setter
@Getter
@Table(name = "t_cgw_technimark_dz_report")
public class TechnimarkDzReport implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 企业代码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 制单人姓名
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 发票号码
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 备案号
     */
    @Column(name = "ems_no")
    private String emsNo;
    /**
     * 运输方式
     */
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 航次号
     */
    @Column(name = "voyage_no")
    private String voyageNo;
    /**
     * 货代编码
     */
    @Column(name = "forward_code")
    private String forwardCode;
    /**
     * 货代名称
     */
    @Column(name = "forward_name")
    private String forwardName;
    /**
     * 运输工具名称
     */
    @Column(name = "traf_name")
    private String trafName;
    /**
     * 主提运单
     */
    @Column(name = "hawb")
    private String hawb;
    /**
     * 境外收发货人代码
     */
    @Column(name = "overseas_shipper")
    private String overseasShipper;
    /**
     * 境外发货人名称
     */
    @Column(name = "overseas_shipper_name")
    private String overseasShipperName;
    /**
     * 监管方式
     */
    @Column(name = "trade_mode")
    private String tradeMode;
    /**
     * 实际贸易条款
     */
    @Column(name = "trade_terms")
    private String tradeTerms;
    /**
     * 报关单号
     */
    @Column(name = "entry_no")
    private String entryNo;
    /**
     * 商品编码
     */
    @Column(name = "code_t_s")
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "g_name")
    @JsonProperty("gName")
    private String gName;
    /**
     * 申报数量
     */
    @Column(name = "qty")
    private BigDecimal qty;
    /**
     * 毛重
     */
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * 净重
     */
    @Column(name = "net_wt")
    private BigDecimal netWt;
    /**
     * 体积
     */
    @Column(name = "volume")
    private BigDecimal volume;
    /**
     * 申报单价
     */
    @Column(name = "dec_price")
    private BigDecimal decPrice;
    /**
     * 申报总价
     */
    @Column(name = "dec_total")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @Column(name = "curr")
    private String curr;
    /**
     * 预计离港日期（ETD）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "etd")
    private Date etd;
    /**
     * 预计到港日期（ETA）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "eta")
    private Date eta;
    /**
     * 出货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ship_date")
    private Date shipDate;
    /**
     * 箱数
     */
    @Column(name = "carton_num")
    private Integer cartonNum;
    /**
     * 托盘数(件数)
     */
    @Column(name = "pallet_num")
    private Integer palletNum;

    /**
     * 英文品名
     */
    @Column(name = "cop_g_name_en")
    private String copGNameEn;

    @Transient
    private String shipDateFrom;
    @Transient
    private String shipDateTo;
    @Transient
    private String shipDateFlag;
}
