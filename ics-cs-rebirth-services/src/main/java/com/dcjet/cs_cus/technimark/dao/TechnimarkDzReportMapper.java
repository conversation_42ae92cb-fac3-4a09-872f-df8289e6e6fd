package com.dcjet.cs_cus.technimark.dao;

import com.dcjet.cs_cus.technimark.model.TechnimarkDzReport;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * CgwTechnimarkDzReport
 *
 * <AUTHOR>
 * @date: 2021-11-18
 */
public interface TechnimarkDzReportMapper extends Mapper<TechnimarkDzReport> {
    /**
     * 查询获取数据
     *
     * @param technimarkDzReport
     * @return
     */
    List<TechnimarkDzReport> getList(TechnimarkDzReport technimarkDzReport);

    /**
     * 清空泰马克出口单证统计表数据
     */
    int deleteReportData(String tradeCode);

    /**
     * 更新泰马克出口单证统计表数据
     */
    int insertReportData(String tradeCode);

    int selectDataCount(TechnimarkDzReport technimarkDzReport);
}
