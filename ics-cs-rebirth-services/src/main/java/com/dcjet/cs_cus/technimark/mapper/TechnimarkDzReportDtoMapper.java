package com.dcjet.cs_cus.technimark.mapper;

import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportDto;
import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportParam;
import com.dcjet.cs_cus.technimark.model.TechnimarkDzReport;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TechnimarkDzReportDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    TechnimarkDzReportDto toDto(TechnimarkDzReport po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    TechnimarkDzReport toPo(TechnimarkDzReportParam param);

    default void patchPo(TechnimarkDzReportParam technimarkDzReportParam, TechnimarkDzReport technimarkDzReport) {
        // TODO 自行实现局部更新
    }
}
