package com.dcjet.cs_cus.pvdtc.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dto_cus.pvdtc.MatNonBondedUpdateImportPvdtcParam;
import com.dcjet.cs.mat.dao.MatImgexgOrgMapper;
import com.dcjet.cs.mat.dao.MatNonBondedMapper;
import com.dcjet.cs.mat.model.MatImgexgChange;
import com.dcjet.cs.mat.model.MatNonBonded;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.MatVariable;
import com.dcjet.cs_cus.pvdtc.dao.ImpTmpMatNonBondedPvdtcMapper;
import com.dcjet.cs_cus.pvdtc.model.ImpTmpMatNonBondedPvdtc;
import com.google.common.base.Strings;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.persistence.Transient;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务系统校验数据、持久化数据的业务类（采用临时表校验，只要用到临时表就算临时表校验）
 * 临时表校验需要在校验方法checkData中返回TempOwnerId，并在持久化数据方法persistData中接收
 * 导入服务在发现业务采用了临时表校验（即checkData方法返回的结果包含TempOwnerId）时，
 * 在调用persistData方法时便不会把正确数据文件重新解析传递给业务方法，减少不必要的解析时间
 */
@Service
public class MatNonBondedImportPvdtcService extends BaseService<ImpTmpMatNonBondedPvdtc> implements ImportHandlerInterface {
    @Resource
    private BulkInsertConfig bulkInsertConfig;

    public IBulkInsert getBulkInsertInstance() throws Exception {
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    @Resource
    private ImpTmpMatNonBondedPvdtcMapper mapper;

    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private CommonService commonService;
    @Resource
    private MatNonBondedMapper matNonBondedMapper;

    @Resource
    private MatImgexgOrgMapper matImgexgOrgMapper;

    @Override
    public Mapper<ImpTmpMatNonBondedPvdtc> getMapper() {
        return mapper;
    }

    /**
     * 0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal
     */
    public final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();
    public final int WARN_FLAG = EnumCollection.EntityListType.WARN_LIST.ordinal();

    /**
     * 返回当前业务类对应的任务类型，导入服务执行器将根据此方法的返回值获取相应的业务类实例
     *
     * @return 当前业务类对应的任务类型
     */
    @Override
    public String getTaskCode() {
        String strTaskCode = "NONBONDED-IMPORT-UPDATE";
        return strTaskCode;
    }

    /**
     * 业务校验方法（临时表校验）
     *
     * @param mapBasicParam    任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList    实体列表map，可根据xmlContent的sheet的id获取某类实体列表
     * @return 业务校验结果，head根据xmlContent的sheet的field获取，无需设置；当采用临时表校验方式时tempOwnerId必须设置，否则插入正确数据时无法进行操作
     */
    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        /*String strEntityId="Nonbonded";
        if(!mapObjectList.containsKey(strEntityId)){
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在" + strEntityId);
            return null;
        }
        List<Object> objectList=mapObjectList.get(strEntityId);*/
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        //从自定义参数中获取需要的数据，此处是导入表体，需获取对应表头的id和当前操作者
        if (!mapBusinessParam.containsKey("insertUser") || !mapBusinessParam.containsKey("moduleType")) {
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();
        String moduleType = mapBusinessParam.get("moduleType").toString().toUpperCase();
        String modifyMark = moduleType.contains(CommonEnum.ModifyMarkType.UPDATE.name()) ? CommonEnum.ModifyMarkType.UPDATE.getModifyMark() : CommonEnum.ModifyMarkType.INSERT.getModifyMark();
        //遍历当前实体列表进行校验
        String guid = UUID.randomUUID().toString();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);
        List<ImpTmpMatNonBondedPvdtc> impTmpMatNonBondedPvdtcs = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        objectList.forEach(pa -> {
            ImpTmpMatNonBondedPvdtc el = (ImpTmpMatNonBondedPvdtc) pa;
            if (Strings.isNullOrEmpty(el.getTempRemark())) {
                el.setTempRemark("");
            }
            MatNonBondedUpdateImportPvdtcParam pvdtcParam = new MatNonBondedUpdateImportPvdtcParam();
            checkstatus(el);
            el.setSid(UUID.randomUUID().toString());
            el.setCopGNo(el.getCopGNo().trim());
            el.setTempOwner(guid);
            el.setTradeCode(tradeCode);
            el.setInsertUser(insertUser);
            el.setInsertUserName(insertUserName);
            el.setInsertTime(new Date());
            el.setUpdateUser(insertUser);
            el.setUpdateTime(new Date());
            el.setApprStatus(CommonVariable.APPR_STATUS_0);
            el.setStatus(MatVariable.STATUS_0);
            impTmpMatNonBondedPvdtcs.add(el);
        });
        XdoImportLogger.log("共获取插入实体：" + impTmpMatNonBondedPvdtcs.size() + ";执行快速入库操作");
        //初始化快速入库实例（在config文件夹下新建OracleBulkConfig用于读取配置文件中的数据库连接配置）
        //使用快速入库时，拼接的sql语句会根据当前实体进行，如果某个字段存在于实体定义中，但传入的值为空，那么这个字段在数据表存在的默认值将无效
        // 所以请显示赋值，或者，如果要使用数据表的默认值请将此字段从实体中去除
        IBulkInsert iBulkInsert = getBulkInsertInstance();
        int intEffectCount = 0;
        try {
            intEffectCount = iBulkInsert.fastImport(impTmpMatNonBondedPvdtcs);
        } catch (BulkInsertException ex) {
            intEffectCount = ex.getCommitRowCount();
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        } catch (Exception ex) {
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        }
        //调用存储过程校验，对字段的基本校验(数据类型、非空、长度、精度等)尽量放在校验实体的注解上
        //如果有特殊情况必须在存储过程里做，不要用 !='' 来判断字段是否非空，因为oracle里''会被当作null处理，而判断null不能用不等于号，因此不会得到预想的结果
        XdoImportLogger.log("执行存储过程校验");
        checkCurrTmp(guid, modifyMark);

        ExcelImportDto<ImpTmpMatNonBondedPvdtc> excelImportDto = new ExcelImportDto<>();
        excelImportDto.setTempOwnerId(guid);
        //获取正确、错误、警告数据列表(警告数据根据各自业务需求设置,可不加)
        Map<String, Object> param = new HashMap<>(2);
        param.put("TEMP_OWNER", guid);
        List flags = new ArrayList();
        //正确数据包含警告数据，因此要把警告数据添加到正确数据列表中，否则警告数据无法导入
        flags.add(CORRECT_FLAG);
        param.put("TEMP_FLAG", flags);
        List<ImpTmpMatNonBondedPvdtc> correctList = selectDataList(param);
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);
        flags.clear();
        flags.add(WRONG_FLAG);
        param.put("TEMP_FLAG", flags);
        List<ImpTmpMatNonBondedPvdtc> wrongList = selectDataList(param);
        excelImportDto.setWrongNumber(wrongList.size());
        if (wrongList.size() > 0 && correctList.size() > 0) {
            wrongList.addAll(correctList);
            wrongList = wrongList.stream().sorted(Comparator.comparing(ImpTmpMatNonBondedPvdtc::getTempIndex)).collect(Collectors.toList());
        }
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log("correct：" + excelImportDto.getCorrectList().size()
                + ";wrong：" + excelImportDto.getWrongList().size());
        excelImportDtoList.add(excelImportDto);
        return excelImportDtoList;
    }

    /**
     * 业务持久化数据方法
     *
     * @param mapBasicParam    任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList    正确数据序列化的实体列表集合，根据导入配置文件(xmlContent)的每个sheet配置的id获取相应实体列表（采用内存校验时传递）
     * @param tempOwnerIdList  临时表处理批次Id，单sheet时取第一条（采用临时表校验时传递）
     * @return 业务持久化数据结果，导入服务根据返回值设置任务状态
     */
    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();
        //从自定义参数中获取需要的数据
        if (!mapBusinessParam.containsKey("moduleType")) {
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        String moduleType = mapBusinessParam.get("moduleType").toString().toUpperCase();
        String modifyMark = moduleType.contains(CommonEnum.ModifyMarkType.UPDATE.name()) ? CommonEnum.ModifyMarkType.UPDATE.getModifyMark() : CommonEnum.ModifyMarkType.INSERT.getModifyMark();
        //按配置顺序（同时也是业务校验时添加到excelImportDtoList的顺序）获取临时表批次Id
        if (tempOwnerIdList == null || tempOwnerIdList.size() <= 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("临时表处理批次Id为空，无法执行持久化数据方法"));
        }
        //按顺序获取临时表批次Id，单sheet时只需获取第一个即可
        String strTempOwnerId = tempOwnerIdList.get(0);
        Map<String, Object> pa = new HashMap<String, Object>(4);
        pa.put("P_TEMP_OWNER", strTempOwnerId);
        pa.put("P_FLAG", MatVariable.STATUS_1);
        pa.put("P_RET_CODE", "");
        pa.put("P_RET_STR", "");
        //mapper.checkTmpByParam(pa);
        Map<String, String> map = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
//        if (modifyMark.equals(CommonEnum.ModifyMarkType.INSERT.getModifyMark())) {
//            map = mapper.checkTmpByParam(pa);
//        } else {
//            //获取指定修改记录
//            List<MatNonBonded> oldList = matNonBondedMapper.getImportMatNonBondedList(strTempOwnerId);
//            map = mapper.checkTmpByParamUpdate(pa);
//            if (pa.get("P_RET_CODE") == null || map == null || map.get("p_ret_code") == null || pa.get("P_RET_CODE") == "" || map.get("p_ret_code") == "") {
//                //批量插入修改记录
//                if (CollectionUtils.isNotEmpty(oldList)) {
//                    List<MatNonBonded> newList = getMatNonBondedListBySids(oldList.stream().map(MatNonBonded::getSid).collect(Collectors.toList()));
//                    batchInsertChanges(oldList, newList, tradeCode, insertUser, insertUserName);
//                }
//            }
//        }

        map = mapper.checkTmpByParam(pa);
        //获取指定修改记录
        List<MatNonBonded> oldList = matNonBondedMapper.getImportMatNonBondedList(strTempOwnerId);
        map1 = mapper.checkTmpByParamUpdate(pa);
        if (pa.get("P_RET_CODE") == null || map == null || map.get("p_ret_code") == null || pa.get("P_RET_CODE") == "" || map.get("p_ret_code") == "") {
            //批量插入修改记录
            if (CollectionUtils.isNotEmpty(oldList)) {
                List<MatNonBonded> newList = getMatNonBondedListBySids(oldList.stream().map(MatNonBonded::getSid).collect(Collectors.toList()));
                batchInsertChanges(oldList, newList, tradeCode, insertUser, insertUserName);
            }
        }
        map.putAll(map1);

        if (pa.get("P_RET_CODE") != null && !pa.get("P_RET_CODE").toString().isEmpty()) {
            throw new ErrorException(400, pa.get("P_RET_CODE") + ":" + pa.get("P_RET_STR"));
        } else if (map != null && map.get("p_ret_code") != null && !map.get("p_ret_code").isEmpty()) {
            throw new ErrorException(400, map.get("p_ret_code") + ":" + map.get("p_ret_str"));
        }
        return resultObject;
    }

    public ImpTmpMatNonBondedPvdtc checkstatus(ImpTmpMatNonBondedPvdtc el) {

        //去除企业料号/备案料号特殊字符
//        el.setCopGNo(commonService.parseCharacter(el.getCopGNo()));

        //非空校验在注解里添加，由框架实现基础校验
        //商品编码校验
        if (el.getCodeTS() != null && el.getCodeTS().length() == 8) {
            el.setCodeTS(el.getCodeTS() + "00");
        }
        if (!Strings.isNullOrEmpty(el.getCodeTS())) {
            Map<String, String> mapTs = pCodeHolder.getTable(PCodeType.COMPLEX, el.getCodeTS()).orElse(null);
            if (mapTs == null) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("商品编码不存在|"));
                el.setTempFlag(WRONG_FLAG);
            } else {
                el.setUnit1(mapTs.get("UNIT_1"));
                if (!Strings.isNullOrEmpty(mapTs.get("UNIT_2"))) {
                    el.setUnit2(mapTs.get("UNIT_2"));
                }
                if (!Strings.isNullOrEmpty(mapTs.get("CONTROL_MA"))) {
                    el.setCredentials(mapTs.get("CONTROL_MA"));
                }
            }
        }
        //计量单位校验
        if (!Strings.isNullOrEmpty(el.getUnit())) {
            String unit = pCodeHolder.getValue(PCodeType.UNIT, el.getUnit());
            if (Strings.isNullOrEmpty(unit)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("申报计量单位不存在|"));
                el.setTempFlag(WRONG_FLAG);
            }
        }
        /*产销国（地区）校验*/
        if (!Strings.isNullOrEmpty(el.getCountryCode())) {
            String country = pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, el.getCountryCode());
            if (Strings.isNullOrEmpty(country)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("产销国不存在|"));
                el.setTempFlag(WRONG_FLAG);
            }
        }
        /*币制校验*/
        if (!Strings.isNullOrEmpty(el.getCurr())) {
            String curr = pCodeHolder.getValue(PCodeType.CURR_OUTDATED, el.getCurr());
            if (Strings.isNullOrEmpty(curr)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("币制不存在|"));
                el.setTempFlag(WRONG_FLAG);
            }
        }

        //根据HS编码带出检验检疫
        if (StringUtils.isNotBlank(el.getCodeTS())) {
            Map<String, String> mapTs = pCodeHolder.getTable(PCodeType.COMPLEX_CIQ, el.getCodeTS()).orElse(null);
            if (mapTs != null) {
                if (StringUtils.isNotBlank(mapTs.get("INSPMONITORCOND"))) {
                    el.setInspmonitorcond(mapTs.get("INSPMONITORCOND"));
                }
            }

        }

        return el;
    }

    /**
     * 描述：校验临时表数据
     *
     * @param ownerid
     * <AUTHOR>
     * @date 2019-03-16
     */
    public void checkCurrTmp(String ownerid, String modifyMark) {

        mapper.updateImporStatus(ownerid);

        Map<String, Object> pa = new HashMap<String, Object>(4);
        pa.put("P_TEMP_OWNER", ownerid);
        pa.put("P_FLAG", MatVariable.STATUS_0);
        pa.put("P_RET_CODE", "");
        pa.put("P_RET_STR", "");

        mapper.checkTmpByParam(pa);
        mapper.checkTmpByParamUpdate(pa);
    }

    /**
     * 描述：获取导入的各种类型数据
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2019-03-16
     */
    public List<ImpTmpMatNonBondedPvdtc> selectDataList(Map<String, Object> param) {
        return mapper.selectByFlag(param);
    }

    /**
     * 批量插入修改记录
     *
     * @param oldList
     * @param newList
     * @param tradeCode
     * @param insertUser
     */
    public void batchInsertChanges(List<MatNonBonded> oldList, List<MatNonBonded> newList, String tradeCode, String insertUser, String insertUserName) {
        FastDateFormat df = FastDateFormat.getInstance("yyyy-MM-dd");
        Map<String, MatNonBonded> newMap = newList.stream().collect(Collectors.toMap(MatNonBonded::getSid, Function.identity()));
        List<MatImgexgChange> changes = new ArrayList<>();
        Date currDate = new Date();

        for (MatNonBonded oldR : oldList) {
            MatNonBonded newR = newMap.get(oldR.getSid());
            Arrays.stream(MatNonBonded.class.getDeclaredFields()).forEach(fl -> {
                if (fl.getDeclaredAnnotation(Transient.class) != null
                        || fl.getDeclaredAnnotation(ApiModelProperty.class) == null || StringUtils.isBlank(fl.getDeclaredAnnotation(ApiModelProperty.class).value())) {
                    return;
                }
                fl.setAccessible(true);
                try {
                    if (fl.get(oldR) != null || fl.get(newR) != null) {
                        if (fl.get(oldR) != null && fl.get(newR) != null && StringUtils.compare(fl.get(oldR).toString(), fl.get(newR).toString()) == 0) {
                            return;
                        }
                        String sid = UUID.randomUUID().toString();
                        MatImgexgChange matImgexgChange = new MatImgexgChange();
                        matImgexgChange.setSid(sid);
                        matImgexgChange.setHeadId(newR.getSid());
                        matImgexgChange.setTradeCode(tradeCode);
                        matImgexgChange.setGMark(newR.getGMark());
                        matImgexgChange.setChangeCode(fl.getAnnotation(ApiModelProperty.class).example());
                        matImgexgChange.setChangeName(fl.getAnnotation(ApiModelProperty.class).value());
                        if (fl.get(oldR) != null) {
                            String oldValue = fl.get(oldR).toString();
                            if (fl.getAnnotation(ApiModelProperty.class).example().contains("CURR")) {
                                oldValue = commonService.convertPCode(fl.get(oldR).toString(), PCodeType.CURR_OUTDATED);
                            } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("UNIT")) {
                                oldValue = commonService.convertPCode(fl.get(oldR).toString(), PCodeType.UNIT);
                            } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("COUNTRY")) {
                                oldValue = commonService.convertPCode(fl.get(oldR).toString(), PCodeType.COUNTRY_OUTDATED);
                            } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("DATE")) {
                                oldValue = df.format(fl.get(oldR));
                            }
                            matImgexgChange.setChangeBefore(oldValue);
                        }
                        if (fl.get(newR) != null) {
                            String newValue = fl.get(newR).toString();
                            if (fl.getAnnotation(ApiModelProperty.class).example().contains("CURR")) {
                                newValue = commonService.convertPCode(fl.get(newR).toString(), PCodeType.CURR_OUTDATED);
                            } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("UNIT")) {
                                newValue = commonService.convertPCode(fl.get(newR).toString(), PCodeType.UNIT);
                            } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("COUNTRY")) {
                                newValue = commonService.convertPCode(fl.get(newR).toString(), PCodeType.COUNTRY_OUTDATED);
                            } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("DATE")) {
                                newValue = df.format(fl.get(newR));
                            }
                            matImgexgChange.setChangeAfter(newValue);
                        }
                        matImgexgChange.setValidMark(MatVariable.VALID_MARK_0);
                        matImgexgChange.setInsertTime(currDate);
                        matImgexgChange.setInsertUser(insertUser);
                        matImgexgChange.setInsertUserName(insertUserName);
//                        matImgexgChange.setCopGNo(newR.getCopGNo());
                        matImgexgChange.setFacGNo(newR.getCopGNo());
                        matImgexgChange.setGMark(newR.getGMark());
                        changes.add(matImgexgChange);
                    }
                } catch (Exception ex) {
                }
            });
        }
        if (CollectionUtils.isNotEmpty(changes)) {
            int intEffectCount = 0;
            try {
                IBulkInsert iBulkInsert = getBulkInsertInstance();
                intEffectCount = iBulkInsert.fastImport(changes);
            } catch (BulkInsertException ex) {
                intEffectCount = ex.getCommitRowCount();
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
            } catch (Exception ex) {
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
            }
        }
    }

    /**
     * 获取修改前的记录
     *
     * @param sids
     */
    public List<MatNonBonded> getMatNonBondedListBySids(List<String> sids) {
        Example example = new Example(MatNonBonded.class);

        if (sids.size() > 1000) {
            List<MatNonBonded> result = new ArrayList<>(sids.size());
            for (int i = 0; i <= sids.size() / 1000; i++) {
                example.clear();
                Example.Criteria criteria = example.createCriteria();
                int fromIdx = i * 1000;
                int toIdx = (i + 1) * 1000;
                if (toIdx > sids.size()) {
                    toIdx = sids.size();
                }
                criteria.andIn("sid", sids.subList(fromIdx, toIdx));
                result.addAll(matNonBondedMapper.selectByExample(example));
            }
            return result;
        } else {
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("sid", sids);
            return matNonBondedMapper.selectByExample(example);
        }

    }
}
