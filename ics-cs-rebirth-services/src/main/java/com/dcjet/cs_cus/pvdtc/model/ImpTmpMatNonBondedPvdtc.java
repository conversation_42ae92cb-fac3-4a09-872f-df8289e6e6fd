package com.dcjet.cs_cus.pvdtc.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 * 物料中心-非保税物料数据
 *
 * @author: 鏈辨涓�
 * @date: 2019-02-27
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_IMP_TMP_MAT_NON_BONDED")
public class ImpTmpMatNonBondedPvdtc extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;

    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 创建时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 用户
     */
    @Column(name = "TEMP_OWNER")
    private String tempOwner;
    /**
     * 标识
     */
    @Column(name = "TEMP_MARK")
    private BigDecimal tempMark;
    /**
     * 提示
     */
    @Column(name = "TEMP_REMARK")
    private String tempRemark;
    /**
     * 备案号
     */
    @XdoSize(max = 512, message = "备案号长度不能超过512位字节长度(一个汉字2位字节长度)!")
    @Column(name = "EMS_NO")
    private String emsNo;
    /**
     * 物料类型 3 料件  4 成品
     */
    @XdoSize(max = 4, message = "物料类型 I 料件 E 成品 2 设备 3 其他长度不能超过4位字节长度(一个汉字2位字节长度)!")
    @Column(name = "G_MARK")
    private String GMark;
    /**
     * 备案序号
     */
    @Digits(integer = 11, fraction = 0, message = "备案序号必须为数字,整数位最大11位,小数最大0位!")
    @Column(name = "SERIAL_NO")
    private BigDecimal serialNo;
    /**
     * 商品料号
     */
    @XdoSize(max = 32, message = "商品料号(企业料号)长度不能超过32位字节长度(一个汉字2位字节长度)!")
    @Column(name = "COP_G_NO")
    private String copGNo;
    /**
     * 商品编码
     */
    @XdoSize(max = 32, message = "商品编码长度不能超过32位字节长度(一个汉字2位字节长度)!")
    @Column(name = "CODE_T_S")
    private String codeTS;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "G_NAME")
    private String GName;
    /**
     * 商品规格型号
     */
    @XdoSize(max = 255, message = "商品规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "G_MODEL")
    private String GModel;
    /**
     * 申报计量单位
     */
    @XdoSize(max = 3, message = "申报计量单位长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @Column(name = "UNIT")
    private String unit;
    /**
     * 法定计量单位
     */
    @XdoSize(max = 3, message = "法定计量单位长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @Column(name = "UNIT_1")
    private String unit1;
    /**
     * 法定第二计量单位
     */
    @XdoSize(max = 3, message = "法定第二计量单位长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @Column(name = "UNIT_2")
    private String unit2;
    /**
     * 申报数量
     */
    @Digits(integer = 11, fraction = 5, message = "申报数量必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "QTY")
    private BigDecimal qty;
    /**
     * 申报要素
     */
    @XdoSize(max = 255, message = "申报要素长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "DECLARE_ELEMENTS")
    private String declareElements;
    /**
     * 申报单价
     */
    @Column(name = "DEC_PRICE")
    private String decPrice;
    /**
     * 申报总价
     */
    @Digits(integer = 12, fraction = 5, message = "申报总价必须为数字,整数位最大12位,小数最大5位!")
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @XdoSize(max = 3, message = "币制长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @Column(name = "CURR")
    private String curr;
    /**
     * 减免方式
     */
    @XdoSize(max = 3, message = "减免方式长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @Column(name = "DUTY_MODE")
    private String dutyMode;
    /**
     * 产销国
     */
    @XdoSize(max = 3, message = "产销国长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @Column(name = "COUNTRY")
    private String countryCode;
    /**
     * 归类标记 1-主料件 2-辅料件；
     */
    @XdoSize(max = 4, message = "归类标记 1-主料件 2-辅料件；长度不能超过4位字节长度(一个汉字2位字节长度)!")
    @Column(name = "CLASS_MARK")
    private String classMark;
    /**
     * 法一单位比例
     */
    @Digits(integer = 11, fraction = 5, message = "法一单位比例必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "FACTOR_1")
    private BigDecimal factor1;
    /**
     * 法二单位比例
     */
    @Digits(integer = 11, fraction = 5, message = "法二单位比例必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "FACTOR_2")
    private BigDecimal factor2;
    /**
     * 重量比例因子
     */
    @Digits(integer = 11, fraction = 5, message = "重量比例因子必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "FACTOR_WT")
    private BigDecimal factorWt;
    /**
     * 归类说明
     */
    @XdoSize(max = 255, message = "归类说明长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "CLASS_REMARK")
    private String classRemark;
    /**
     * 修改标记代码 0-未修改 1-修改 2-删除 3-增加 备案时新增；变更时修改、增加；删除暂时不用。海关审批通过时，自动将最新备案数据更新到未修改。
     */
    @Column(name = "MODIFY_MARK")
    private String modifyMark;
    /**
     * 企业执行标记代码 1-运行 2-停用，默认为1，企业申报 默认1v
     */
    @Column(name = "ETPS_EXE_MARK")
    private String comImplementSign;

    /**
     * 英文商品名称
     */
    @XdoSize(max = 255, message = "企业商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "COP_G_NAME")
    private String copGName;
    /**
     * 英文商品规格型号
     */
    @XdoSize(max = 255, message = "企业规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "COP_G_MODEL")
    private String copGModel;
    /**
     * 工厂
     */
    @XdoSize(max = 20, message = "工厂长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @Column(name = "FACTORY")
    private String factory;
    /**
     * 成本中心
     */
    @XdoSize(max = 50, message = "成本中心长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @Column(name = "COST_CENTER")
    private String costCenter;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 单重
     */
    @Digits(integer = 11, fraction = 5, message = "单重必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "NET_WT")
    private BigDecimal netWt;
    /**
     * 重量单位
     */
    @XdoSize(max = 3, message = "重量单位长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @Column(name = "UNIT_WT")
    private String unitWt;
    /**
     * 数据状态 0 正常，1 过滤
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 备注
     */
    @XdoSize(max = 512, message = "备注长度不能超过512位字节长度(一个汉字2位字节长度)!")
    @Column(name = "NOTE")
    private String note;

    /**
     * 发送人
     */
    @Column(name = "SEND_USER")
    private String sendUser;
    /**
     * 发送时间
     */
    @Column(name = "DECLARE_DATE")
    private Date declareDate;
    /**
     * 内审员
     */
    @Column(name = "APPR_USER")
    private String auditUser;
    /**
     * 审核时间
     */
    @Column(name = "APPR_TIME")
    private Date auditTime;
    /**
     * 审核状态
     */
    @Column(name = "APPR_STATUS")
    private String apprStatus;

    /**
     * 手/账册内部编号
     */
    @XdoSize(max = 512, message = "手/账册内部编号长度不能超过512位字节长度(一个汉字2位字节长度)!")
    @Column(name = "COP_EMS_NO")
    private String copEmsNo;

    /**
     * 备案有效期
     */
    @Column(name = "RECORD_DATE_START")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date recordDateStart;

    /**
     * 备案有效期
     */
    @Column(name = "RECORD_DATE_END")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date recordDateEnd;

    /**
     * 标识
     */
    @Column(name = "TEMP_FLAG")
    private int tempFlag;
    /**
     * 序号
     */
    @Column(name = "TEMP_INDEX")
    private int tempIndex;

    /**
     * 英文名称
     */
    @Column(name = "COP_G_NAME_EN")
    private String copGNameEn;

    /**
     * 英文规格型号
     */
    @Column(name = "COP_G_MODEL_EN")
    private String copGModelEn;

    /**
     * ERP计量单位
     */
    @Column(name = "UNIT_ERP")
    private String unitErp;

    /**
     * ERP比例因子
     */
    @Column(name = "FACTOR_ERP")
    private BigDecimal factorErp;

    /**
     * 净重数量
     */
    @Column(name = "QTY_WT")
    private BigDecimal qtyWt;
    /**
     * 原始物料
     */
    @Column(name = "ORIGINAL_G_NO")
    private String originalGNo;
    /**
     * 监管证件
     */
    @Column(name = "CREDENTIALS")
    private String credentials;
    /**
     * CIQ编码
     */
    @Column(name = "CIQ_NO")
    private String ciqNo;
    /**
     * 客供单价
     */
    @Column(name = "CLIENT_PRICE")
    private BigDecimal clientPrice;

    /**
     * 检验检疫
     */
    @Column(name = "INSPMONITORCOND")
    private String inspmonitorcond;

    @XdoSize(max = 255, message = "商品用途长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "use_to")
    private String useTo;
    @XdoSize(max = 255, message = "商品构成长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @Column(name = "structure")
    private String structure;
    /**
     * 导入状态
     */
    @Column(name = "IMPORT_STATUS")
    private String importStatus;
}
