package com.dcjet.cs_cus.huanhong.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.ftpConfig.dao.GwstdFtpConfigMapper;
import com.dcjet.cs.ftpConfig.model.GwstdFtpConfig;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.SFTPUtils;
import com.dcjet.cs_cus.huanhong.dao.DutyFormMapper;
import com.dcjet.cs_cus.huanhong.model.TaxPaymentData;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: WJ
 * @createDate: 2022/2/11 11:17
 */
@Service
@Slf4j
public class TaxPaymentService {

    @Resource
    private DutyFormMapper dutyFormMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private ExportService exportService;
    @Resource
    private GwstdFtpConfigMapper gwstdFtpConfigMapper;

    public void returnTaxData() {
        String dateString = DateUtils.dateToString(new Date(), "yyyyMMdd");
        log.info("****************获取待发送数据***************");
        // 获取最近2个月没支付状态的税单信息
        String tradeCode = "3223963234";
        List<TaxPaymentData> list = dutyFormMapper.selectTaxData(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.info("****************未获取到待发送数据***************");
        }
        log.info("****************获取完待发送数据，pcode转换***************");
        // pcode转换
        int i = 1;
        for (TaxPaymentData data : list) {
            data.setIndex(i);
            data.setTradeMode(commonService.convertPCode(data.getTradeMode(), PCodeType.TRADE));
            data.setMasterCustoms(commonService.convertPCode(data.getMasterCustoms(), PCodeType.CUSTOMS_REL, ConstantsStatus.STATUS_4));
            if (StringUtils.isNotBlank(data.getTaxType())) {
                data.setTaxType(CommonEnum.TAX_TYPE.getValue(data.getTaxType()));
            }
            if (StringUtils.isNotBlank(data.getTransStatus())) {
                data.setTransStatus(CommonEnum.TRANS_STATUS.getValue(data.getTransStatus()));
            }
        }
        log.info("****************生成excel临时文件***************");
        // 导出excel文件
        String templateName = "tax_payment.xlsx";
        String fileName = dateString + ".csv";
        String exportPath = exportService.exportForHuanHong(list, fileName, "huanhong" + File.separator + templateName);
        // 上传文件
        uploadExcel(tradeCode, exportPath);
    }

//    private void uploadExcel(String tradeCode, String exportPath) {
//        FTPClient ftpClient = null;
//        try {
//            log.info("****************连接ftp***************");
//            // 连接ftp
//            GwstdFtpConfig config = getFtpConfigList(tradeCode);
//
//            ftpClient = FtpUtil.connectFtpServer(config.getIpAddr(), config.getPort(), config.getFtpAccount(),
//                    config.getFtpPwd(), StringUtils.isBlank(config.getCoding()) ? "UTF-8" : config.getCoding());
//
//            // 文件上传路径
//            String path = config.getServerFilePath();
//            // 切换目录
//            FtpUtil.changeWorkingDirectory(ftpClient, path);
//            if (StringUtils.isNotBlank(exportPath)) {
//                // 上传文件
//                File file = new File(exportPath);
//                log.info("***************开始上传文件，目标地址{}***************", path);
//                FtpUtil.uploadFiles(ftpClient, file);
//            } else {
//                log.info("****************生成excel临时文件发生异常***************");
//            }
//        } catch (Exception e) {
//            log.error("连接ftp上传excel发生异常", e);
//        } finally {
//            //关闭Ftp连接
//            FtpUtil.closeFTPConnect(ftpClient);
//        }
//    }
    private void uploadExcel(String tradeCode, String exportPath) {
        if (StringUtils.isBlank(exportPath)) {
            log.info("****************生成excel临时文件发生异常***************");
            return;
        }
        // 获取sftp配置
        GwstdFtpConfig config = getFtpConfigList(tradeCode);

        SFTPUtils sftpUtils = new SFTPUtils(config.getIpAddr(), config.getPort(), config.getFtpAccount(), config.getFtpPwd());
        try {
            // 连接SFTP服务器
            sftpUtils.connect();
            // 上传文件
            File file = new File(exportPath);
            sftpUtils.uploadFile(config.getServerFilePath(), file.getName(), new FileInputStream(exportPath));
            log.info("文件上传成功");
        } catch (Exception e) {
            log.error("传输文件失败:{}", e);
        } finally {
            // 关闭sftp连接
            sftpUtils.disconnect();
            log.info("关闭SFTP连接");
        }
    }

    /**
     * 获取FTP账号配置信息
     * @return
     */
    public GwstdFtpConfig getFtpConfigList(String tradeCode) {
        Example example = new Example(GwstdFtpConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isEnable", "1");
        criteria.andEqualTo("tradeCode", tradeCode);
        List<GwstdFtpConfig> configList = gwstdFtpConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(configList)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("扫描Ftp目录失败,未查询到到FTP配置信息!"));
        }
        return configList.get(0);
    }
}
