package com.dcjet.cs_cus.huanhong.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description:
 * @author: WJ
 * @createDate: 2022/2/11 10:54
 */
@Getter
@Setter
public class TaxPaymentData {
    @ApiModelProperty("序号")
    private Integer index;
    @ApiModelProperty("报关单号")
    private String entryId;
    @ApiModelProperty("监管方式")
    private String tradeMode;
    @ApiModelProperty("税费种类")
    private String taxType;
    @ApiModelProperty("税单生成时间")
    private String generationTime;
    @ApiModelProperty("申报关区")
    private String masterCustoms;
    @ApiModelProperty("缴款期限")
    private String limitDate;
    @ApiModelProperty("支付金额")
    private BigDecimal paymentAmount;
    @ApiModelProperty("汇总征税标志")
    private String sumTaxMark;
    @ApiModelProperty("是否支付 N:未支付，S:已支付，P、A、:支付中")
    private String transStatus;
}
