package com.dcjet.cs_cus.cusSt.mapper;

import com.dcjet.cs.dto_cus.cusSt.VTaxCipSelectDto;
import com.dcjet.cs.dto_cus.cusSt.VTaxCipSelectParam;
import com.dcjet.cs_cus.cusSt.model.VTaxCipSelect;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VTaxCipSelectDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    VTaxCipSelectDto toDto(VTaxCipSelect po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    VTaxCipSelect toPo(VTaxCipSelectParam param);
    /**
     * 数据库原始数据更新
     * @param vTaxCipSelectParam
     * @param vTaxCipSelect
     */
    void updatePo(VTaxCipSelectParam vTaxCipSelectParam, @MappingTarget VTaxCipSelect vTaxCipSelect);
    default void patchPo(VTaxCipSelectParam vTaxCipSelectParam, VTaxCipSelect vTaxCipSelect) {
        // TODO 自行实现局部更新
    }
}
