package com.dcjet.cs_cus.cusSt.service;

import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.mat.MatIeInfoDto;
import com.dcjet.cs.dto_cus.cusSt.CusStParam;
import com.dcjet.cs.dto_cus.cusSt.SapTaxCipDto;
import com.dcjet.cs.dto_cus.cusSt.SapTaxCipParam;
import com.dcjet.cs.mat.dao.MatImgexgOrgMapper;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs_cus.cusSt.dao.SapTaxCipMapper;
import com.dcjet.cs_cus.cusSt.dao.TaxConInvPackMapper;
import com.dcjet.cs_cus.cusSt.mapper.SapTaxCipDtoMapper;
import com.dcjet.cs_cus.cusSt.model.SapTaxCip;
import com.dcjet.cs_cus.cusSt.model.TaxConInvPack;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-7-22
 */
@Service
public class SapTaxCipService extends BaseService<SapTaxCip> {
    @Resource
    private SapTaxCipMapper sapTaxCipMapper;
    @Resource
    private SapTaxCipDtoMapper sapTaxCipDtoMapper;
    @Resource
    private TaxConInvPackMapper taxConInvPackMapper;
    @Resource
    private MatImgexgOrgMapper matImgexgOrgMapper;
    @Resource
    private CommonService commonService;
    @Override
    public Mapper<SapTaxCip> getMapper() {
        return sapTaxCipMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param sapTaxCipParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<SapTaxCipDto>> getListPaged(SapTaxCipParam sapTaxCipParam, PageParam pageParam) {
        // 启用分页查询
        SapTaxCip sapTaxCip = sapTaxCipDtoMapper.toPo(sapTaxCipParam);
        Page<SapTaxCip> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> sapTaxCipMapper.getList(sapTaxCip));
        List<SapTaxCipDto> sapTaxCipDtos = page.getResult().stream().map(head -> {
            SapTaxCipDto dto = sapTaxCipDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<SapTaxCipDto>> paged = ResultObject.createInstance(sapTaxCipDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param sapTaxCipParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SapTaxCipDto insert(SapTaxCipParam sapTaxCipParam, UserInfoToken userInfo) {
        SapTaxCip sapTaxCip = sapTaxCipDtoMapper.toPo(sapTaxCipParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        sapTaxCip.setSid(sid);
        sapTaxCip.setInsertUser(userInfo.getUserNo());
        sapTaxCip.setInsertTime(new Date());
        // 新增数据
        int insertStatus = sapTaxCipMapper.insert(sapTaxCip);
        return  insertStatus > 0 ? sapTaxCipDtoMapper.toDto(sapTaxCip) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param sapTaxCipParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SapTaxCipDto update(SapTaxCipParam sapTaxCipParam, UserInfoToken userInfo) {
        SapTaxCip sapTaxCip = sapTaxCipMapper.selectByPrimaryKey(sapTaxCipParam.getSid());
        sapTaxCipDtoMapper.updatePo(sapTaxCipParam, sapTaxCip);
        sapTaxCip.setUpdateUser(userInfo.getUserNo());
        sapTaxCip.setUpdateTime(new Date());
        // 更新数据
        int update = sapTaxCipMapper.updateByPrimaryKey(sapTaxCip);
        return update > 0 ? sapTaxCipDtoMapper.toDto(sapTaxCip) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		sapTaxCipMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<SapTaxCipDto> selectAll(SapTaxCipParam exportParam, UserInfoToken userInfo) {
        SapTaxCip sapTaxCip = sapTaxCipDtoMapper.toPo(exportParam);
        sapTaxCip.setTradeCode(userInfo.getCompany());
        List<SapTaxCipDto> sapTaxCipDtos = new ArrayList<>();
        List<SapTaxCip> sapTaxCips = sapTaxCipMapper.getList(sapTaxCip);
        if (CollectionUtils.isNotEmpty(sapTaxCips)) {
            sapTaxCipDtos = sapTaxCips.stream().map(head -> {
                SapTaxCipDto dto = sapTaxCipDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return sapTaxCipDtos;
    }

    /**
     * 对账单数据引入查询
     *
     * <AUTHOR> @param cusStParam
     * @param userInfo
     * @return
     */
    public ResultObject<List<SapTaxCipDto>> leadCusStData(CusStParam cusStParam,PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<SapTaxCipDto>> result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("引入完成"));
        try {
            String emsNo=cusStParam.getEmsNo();
            List<String> invs=cusStParam.getInvs();
            invs.removeAll(Collections.singleton(null));
            String strMsg = checkCusStData(emsNo,  invs, userInfo);
            if (Strings.isNullOrEmpty(strMsg) == false) {
                result.setSuccess(false);
                result.setMessage(strMsg);
            } else {
                /*List<String>  invs1=invs.stream().map(inv->
                {
                    if (inv.startsWith("00")==false)
                    {
                        inv="00"+inv;
                    }
                    return inv;
                }).collect(Collectors.toList());*/
                Page<SapTaxCip> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                        .doSelectPage(() -> sapTaxCipMapper.getListByInvs(invs));
                List<SapTaxCipDto> sapTaxCipDtos = page.getResult().stream().map(head -> {
                    SapTaxCipDto dto = sapTaxCipDtoMapper.toDto(head);
                    return dto;
                }).collect(Collectors.toList());
                result.setMessage(invs.toString());
                result.setData(sapTaxCipDtos);
                result.setTotal((int) page.getTotal());
                result.setPageIndex(page.getPageNum());
            }
        }
        catch (Exception Err)
        {
            result.setSuccess(false);
            result.setMessage(Err.toString());
        }
        return  result;
    }
    /**
     * 校验引入的对账单数据
     *
     * <AUTHOR> @param invs
     * @param userInfo
     * @return
     */
    public String checkCusStData(String emsNo,  List<String> invs, UserInfoToken userInfo) {
        SapTaxCip sapTaxCip=new SapTaxCip();
        TaxConInvPack taxConInvPack=new TaxConInvPack();
        String strMsg="";
        String strBondsMark="";
        StringBuilder checkMessage = new StringBuilder();
        /*if(Strings.isNullOrEmpty(emsNo))
        {
            checkMessage.append("请选择备案号！|");
        }*/
        if(Strings.isNullOrEmpty(emsNo))
        {
            strBondsMark= CommonEnum.BondMarkEnum.NOBOND.getCode();
        }
        else
        {
            strBondsMark=CommonEnum.BondMarkEnum.BOND.getCode();
        }
        if(CollectionUtils.isNotEmpty(invs)==false)
        {
            checkMessage.append(xdoi18n.XdoI18nUtil.t("单据号列表为空！"));
        }
        strMsg=checkMessage.toString();
        if(Strings.isNullOrEmpty(strMsg)==false)
        {
            return strMsg;
        }
        /*invs=invs.stream().map(inv->
        {
            if (inv.startsWith("00")==false)
            {
                inv="00"+inv;
            }
            return inv;
        }).collect(Collectors.toList());*/

        for(String inv:invs) {
            sapTaxCip.setBillNo(inv);
            List<SapTaxCip> sapTaxCips=sapTaxCipMapper.getList(sapTaxCip);
            if(CollectionUtils.isNotEmpty(sapTaxCips)==false)
            {
                checkMessage.append(inv+"|");
            }
        };
        if(Strings.isNullOrEmpty(checkMessage.toString())==false) {
            strMsg=xdoi18n.XdoI18nUtil.t("以下单据号不存在：")+checkMessage.toString();
            return  strMsg;
        }
        for(String inv:invs) {
            taxConInvPack.setBillNo(inv);
            List<TaxConInvPack> sapTaxCips=taxConInvPackMapper.getList(taxConInvPack);
            if(CollectionUtils.isNotEmpty(sapTaxCips))
            {
                checkMessage.append(inv+"|");
            }
        };
        if(Strings.isNullOrEmpty(checkMessage.toString())==false) {
            strMsg=xdoi18n.XdoI18nUtil.t("以下单据号已经引入过：")+checkMessage.toString();
            return  strMsg;
        }
        List<SapTaxCip> sapTaxCips=sapTaxCipMapper.getListByInvs(invs);
        for(SapTaxCip sapTaxCipItem :sapTaxCips)
        {
            //检查料号是否可用
            Map<String, Object> param = new HashMap<>();
            param.put("tradeCode", userInfo.getCompany());
            param.put("emsNo", emsNo);
            param.put("bondedFlag",strBondsMark);
            param.put("GMark", CommonEnum.GMarkEnum.EXG.getCode());
            param.put("facGNo", sapTaxCipItem.getCopGNo());

            //备案序号
            List<MatIeInfoDto>  lstMatImgExg =matImgexgOrgMapper.getIeInfoSimple(param);
            if(CollectionUtils.isNotEmpty(lstMatImgExg) == false) {
                if(checkMessage.toString().contains(sapTaxCipItem.getCopGNo())==false)
                {
                   checkMessage.append(sapTaxCipItem.getCopGNo()+"｜");
                }
            }
        }
        if(Strings.isNullOrEmpty(checkMessage.toString())==false) {
            strMsg=xdoi18n.XdoI18nUtil.t("以下料号未备案或未审核通过：")+checkMessage.toString();
            return  strMsg;
        }

        return  strMsg;
    }

    /**
     * 保存引入的对账单数据
     *
     * <AUTHOR> @param cusStParam
     * @param userInfo
     * @return
     */
    public ResultObject saveCusStData(CusStParam cusStParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("保存成功"));
        String emsNo=cusStParam.getEmsNo();
        List<String> invs=cusStParam.getInvs();
        String isXmy=ConstantsStatus.STATUS_0;
        if(Strings.isNullOrEmpty(cusStParam.getIsXmy())==false) {
        isXmy=cusStParam.getIsXmy();
        }
        String pid = UUID.randomUUID().toString();
        try {
            String strMsg = "";
            StringBuilder checkMessage = new StringBuilder();
            /*if (Strings.isNullOrEmpty(emsNo)) {
                checkMessage.append("请选择备案号！|");
            }*/
            if (CollectionUtils.isNotEmpty(invs)==false) {
                checkMessage.append(xdoi18n.XdoI18nUtil.t("单据号列表为空！"));
            }
            strMsg = checkMessage.toString();
            if (Strings.isNullOrEmpty(strMsg) == false) {
                result.setSuccess(false);
                result.setMessage(strMsg);
                return result;
            }

            Map<String, Object> param = new HashMap<>(3);
            param.put("pid", pid);
            param.put("invs", invs);
            param.put("tradeCode", userInfo.getCompany());
            int insertStatus = taxConInvPackMapper.insertInvs(param);

            Map<String, Object> param2 = new HashMap<>(7);
            param2.put("P_ID", pid);
            param2.put("P_USER_ID", userInfo.getUserNo());
            param2.put("P_EMS_NO", emsNo);
            param2.put("P_XMY", isXmy);
            param2.put("P_TRADE_CODE", userInfo.getCompany());
            param2.put("P_RET_CODE", "");
            param2.put("P_RET_STR", "");
            Map<String, Object> cusStMap = taxConInvPackMapper.insertCusStData(param2);
            if (param2.get("P_RET_CODE") != null && !param2.get("P_RET_CODE").toString().isEmpty()) {
                strMsg = commonService.sqlErrorMsg(param2.get("P_RET_CODE") + ":" + param2.get("P_RET_STR"));
            } else if (cusStMap != null && cusStMap.get("p_ret_code") != null && !cusStMap.get("p_ret_code").toString().isEmpty()) {
                strMsg = commonService.sqlErrorMsg(cusStMap.get("p_ret_code") + ":" + cusStMap.get("p_ret_str"));
            }
            if (Strings.isNullOrEmpty(strMsg) == false) {
                result.setSuccess(false);
                result.setMessage(strMsg);
                return result;
            }

        }
        catch (Exception Err)
        {
            result.setSuccess(false);
            result.setMessage(Err.toString());
            return result;
        }
        finally {
            taxConInvPackMapper.deleteByPid(pid);
        }
        return  result;
    }
}
