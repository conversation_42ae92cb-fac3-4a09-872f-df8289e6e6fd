package com.dcjet.cs_cus.cusSt.dao;

import com.dcjet.cs_cus.cusSt.model.VTaxCipHead;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* VTaxCipHead
* <AUTHOR>
* @date: 2020-7-27
*/
public interface VTaxCipHeadMapper extends Mapper<VTaxCipHead> {
    /**
     * 查询获取数据
     * @param vTaxCipHead
     * @return
     */
    List<VTaxCipHead> getList(VTaxCipHead vTaxCipHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
