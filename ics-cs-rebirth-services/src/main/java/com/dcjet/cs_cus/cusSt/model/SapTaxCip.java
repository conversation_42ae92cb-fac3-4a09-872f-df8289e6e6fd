package com.dcjet.cs_cus.cusSt.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-7-22
 */
@Setter
@Getter
@Table(name = "T_SAP_TAX_CIP")
public class SapTaxCip implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "M_DATE")
	@JsonProperty("mDate")
	private  Date mDate;
	/**
     * 日期-开始
     */
	@Transient
	private String mDateFrom;
	/**
     * 日期-结束
     */
	@Transient
    private String mDateTo;
	/**
     * 发运单号
     */
	@Column(name = "BILL_NO")
	private  String billNo;
	/**
     * 客户代码
     */
	@Column(name = "CUST_ID")
	private  String custId;
	/**
     * 客户采购料号
     */
	@Column(name = "PO")
	private  String po;
	/**
     * 客户物料
     */
	@Column(name = "C_COP_G_NO")
	@JsonProperty("cCopGNo")
	private  String cCopGNo;
	/**
     * 客户项号
     */
	@Column(name = "C_G_NO")
	@JsonProperty("cGNo")
	private  Integer cGNo;
	/**
     * 物料描述
     */
	@Column(name = "G_MARK")
	@JsonProperty("gMark")
	private  String gMark;
	/**
     * 数量（片）
     */
	@Column(name = "QTY1")
	private  BigDecimal qty1;
	/**
     * 单位
     */
	@Column(name = "UNIT")
	private  String unit;
	/**
     * 净重
     */
	@Column(name = "NET")
	private  BigDecimal net;
	/**
     * 毛重
     */
	@Column(name = "GROSS")
	private  BigDecimal gross;
	/**
     * 金额
     */
	@Column(name = "DEC_TOTAL")
	private  BigDecimal decTotal;
	/**
     * 币别
     */
	@Column(name = "DEC_CURR")
	private  String decCurr;
	/**
     * 企业物料代码
     */
	@Column(name = "SAP_COP_G_NO")
	private  String sapCopGNo;
	/**
     * 海关物料代码
     */
	@Column(name = "COP_G_NO")
	private  String copGNo;
	/**
     * 数量（折合平方米）
     */
	@Column(name = "QTY2")
	private  BigDecimal qty2;
	/**
     * BOM版本号
     */
	@Column(name = "VERSION_NO")
	private  String versionNo;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * MQ同步数据批次号
     */
	@Column(name = "SYNC_BATCH_NO")
	private  String syncBatchNo;
	/**
     * 传输批次号
     */
	@Column(name = "TEMP_OWNER")
	private  String tempOwner;
	/**
     * 数据标识 0 正常 1 错误
     */
	@Column(name = "TEMP_FLAG")
	private  Integer tempFlag;
	/**
     * 错误说明
     */
	@Column(name = "TEMP_REMARK")
	private  String tempRemark;
	/**
     * 唯一键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
}
