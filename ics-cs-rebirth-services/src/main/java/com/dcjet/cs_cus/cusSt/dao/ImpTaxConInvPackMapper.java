package com.dcjet.cs_cus.cusSt.dao;

import com.dcjet.cs_cus.cusSt.model.ImpTaxConInvPack;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* ImpTaxConInvPack
* <AUTHOR>
* @date: 2020-7-24
*/
public interface ImpTaxConInvPackMapper extends Mapper<ImpTaxConInvPack> {
    /**
     * 查询获取数据
     * @param impTaxConInvPack
     * @return
     */
    List<ImpTaxConInvPack> getList(ImpTaxConInvPack impTaxConInvPack);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    void checkTmpByParam(Map<String, Object> pa);

    List<ImpTaxConInvPack> selectByFlag(Map<String, Object> param);
}
