package com.dcjet.cs_cus.cusSt.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-7-27
 */
@Setter
@Getter
@Table(name = "")
public class VTaxCipSelect implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	@Column(name = "EN_CH_NAME")
	private  String enChName;
	/**
     * 
     */
	@Column(name = "BS_DESCRIPTION")
	private  String bsDescription;
	/**
     * 
     */
	@Column(name = "C_COP_G_NO")
	@JsonProperty("cCopGNo")
	private  String cCopGNo;
	/**
     * 
     */
	@Column(name = "C_G_NO")
	@JsonProperty("cGNo")
	private  Integer cGNo;
	/**
     * 
     */
	@Column(name = "QTY1")
	private  BigDecimal qty1;
	/**
     * 
     */
	@Column(name = "UNIT")
	private  String unit;
	/**
     * 
     */
	@Column(name = "NET")
	private  BigDecimal net;
	/**
     * 
     */
	@Column(name = "GROSS")
	private  BigDecimal gross;
	/**
     * 
     */
	@Column(name = "DEC_TOTAL")
	private  BigDecimal decTotal;
	/**
     * 
     */
	@Column(name = "DEC_CURR")
	private  String decCurr;
	/**
     * 
     */
	@Column(name = "QTY2")
	private  BigDecimal qty2;
	/**
     * 
     */
	@Column(name = "CUST_NAME")
	private  String custName;
	/**
     * 
     */
	@Column(name = "SELF_ID")
	private  String selfId;
	/**
     * 
     */
	@Column(name = "CREATE_USER")
	private  String createUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_DATE")
	private  Date createDate;
	/**
     * -开始
     */
	@Transient
	private String createDateFrom;
	/**
     * -结束
     */
	@Transient
    private String createDateTo;
	/**
     * 
     */
	@Column(name = "G_NAME_CH")
	@JsonProperty("gNameCh")
	private  String gNameCh;
	/**
     * 
     */
	@Column(name = "DEC_P1")
	private  BigDecimal decP1;
	/**
     * 
     */
	@Column(name = "DEC_P2")
	private  BigDecimal decP2;
	/**
     * 
     */
	@Column(name = "C_NO")
	@JsonProperty("cNo")
	private  Integer cNo;
	/**
     * 
     */
	@Column(name = "DISPLAY_NO")
	private  Integer displayNo;
	/**
     * 
     */
	@Column(name = "G_NAME_EN")
	@JsonProperty("gNameEn")
	private  String gNameEn;
	/**
     * 
     */
	@Column(name = "PO")
	private  String po;
	/**
     * 
     */
	@Column(name = "G_MARK")
	@JsonProperty("gMark")
	private  String gMark;
	/**
     * 
     */
	@Column(name = "COUNTRY_NAME")
	private  String countryName;
	/**
     * 
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
	 *
	 */
	@Column(name = "EMS_NO")
	private  String emsNo;
}
