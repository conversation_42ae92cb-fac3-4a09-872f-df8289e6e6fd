package com.dcjet.cs_cus.cusSt.mapper;

import com.dcjet.cs.dto_cus.cusSt.TaxConInvPackDto;
import com.dcjet.cs.dto_cus.cusSt.TaxConInvPackParam;
import com.dcjet.cs_cus.cusSt.model.TaxConInvPack;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaxConInvPackDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    TaxConInvPackDto toDto(TaxConInvPack po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    TaxConInvPack toPo(TaxConInvPackParam param);
    /**
     * 数据库原始数据更新
     * @param taxConInvPackParam
     * @param taxConInvPack
     */
    void updatePo(TaxConInvPackParam taxConInvPackParam, @MappingTarget TaxConInvPack taxConInvPack);
    default void patchPo(TaxConInvPackParam taxConInvPackParam, TaxConInvPack taxConInvPack) {
        // TODO 自行实现局部更新
    }
}
