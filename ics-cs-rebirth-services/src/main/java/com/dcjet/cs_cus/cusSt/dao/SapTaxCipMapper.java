package com.dcjet.cs_cus.cusSt.dao;

import com.dcjet.cs_cus.cusSt.model.SapTaxCip;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* SapTaxCip
* <AUTHOR>
* @date: 2020-7-22
*/
public interface SapTaxCipMapper extends Mapper<SapTaxCip> {
    /**
     * 查询获取数据
     * @param sapTaxCip
     * @return
     */
    List<SapTaxCip> getList(SapTaxCip sapTaxCip);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 查询获取数据
     * @param invs
     * @return
     */
    List<SapTaxCip> getListByInvs(List<String> invs);
}
