package com.dcjet.cs_cus.cusSt.dao;

import com.dcjet.cs_cus.cusSt.model.TaxConInvPack;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;
/**
* generated by Generate 神码
* TaxConInvPack
* <AUTHOR>
* @date: 2020-7-22
*/
public interface TaxConInvPackMapper extends Mapper<TaxConInvPack> {
    /**
     * 查询获取数据
     * @param taxConInvPack
     * @return
     */
    List<TaxConInvPack> getList(TaxConInvPack taxConInvPack);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 删除
     * @param pid
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteByPid(String pid);
    /**
     * 批量插入单据号
     * @param map
     * @return 返回执行成功行数，0为执行失败
     */
    int insertInvs(Map<String,Object> map);
    /***
     * 调用存储过程 保存引用数据
     * @param param
     * @return
     */
    Map<String,Object> insertCusStData(Map<String, Object> param);

    /***
     * 调用存储过程 计算对账单数据源
     * @param param
     * @return
     */
    Map<String,Object> computeTaxPackData(Map<String, Object> param);

    /**
     * 查询对账单客户数据
     *
     * <AUTHOR>
    List<Map<String, Object>> selectCusCombox();
    /**
     * 获取客户名称
     *
     * <AUTHOR>
    String  getCusName(String cusCode);

    /***
     * 调用存储过程 保存引用数据
     * @param param
     * @return
     */
    Map<String,Object> generateCusStData(Map<String, Object> param);

    /***
     * 调用存储过程 保存引用数据
     * @param param
     * @return
     */
    Map<String,Object> generateOtherCusStData(Map<String, Object> param);
}
