package com.dcjet.cs_cus.cusSt.mapper;

import com.dcjet.cs.dto_cus.cusSt.ImpTaxConInvPackDto;
import com.dcjet.cs.dto_cus.cusSt.ImpTaxConInvPackParam;
import com.dcjet.cs_cus.cusSt.model.ImpTaxConInvPack;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ImpTaxConInvPackDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    ImpTaxConInvPackDto toDto(ImpTaxConInvPack po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    ImpTaxConInvPack toPo(ImpTaxConInvPackParam param);
    /**
     * 数据库原始数据更新
     * @param impTaxConInvPackParam
     * @param impTaxConInvPack
     */
    void updatePo(ImpTaxConInvPackParam impTaxConInvPackParam, @MappingTarget ImpTaxConInvPack impTaxConInvPack);
    default void patchPo(ImpTaxConInvPackParam impTaxConInvPackParam, ImpTaxConInvPack impTaxConInvPack) {
        // TODO 自行实现局部更新
    }
}
