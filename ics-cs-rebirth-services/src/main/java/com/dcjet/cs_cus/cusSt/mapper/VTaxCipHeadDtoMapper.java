package com.dcjet.cs_cus.cusSt.mapper;

import com.dcjet.cs.dto_cus.cusSt.VTaxCipHeadDto;
import com.dcjet.cs.dto_cus.cusSt.VTaxCipHeadParam;
import com.dcjet.cs_cus.cusSt.model.VTaxCipHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VTaxCipHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    VTaxCipHeadDto toDto(VTaxCipHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    VTaxCipHead toPo(VTaxCipHeadParam param);
    /**
     * 数据库原始数据更新
     * @param vTaxCipHeadParam
     * @param vTaxCipHead
     */
    void updatePo(VTaxCipHeadParam vTaxCipHeadParam, @MappingTarget VTaxCipHead vTaxCipHead);
    default void patchPo(VTaxCipHeadParam vTaxCipHeadParam, VTaxCipHead vTaxCipHead) {
        // TODO 自行实现局部更新
    }
}
