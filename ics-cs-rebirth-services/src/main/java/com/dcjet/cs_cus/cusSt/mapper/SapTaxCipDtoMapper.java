package com.dcjet.cs_cus.cusSt.mapper;

import com.dcjet.cs.dto_cus.cusSt.SapTaxCipDto;
import com.dcjet.cs.dto_cus.cusSt.SapTaxCipParam;
import com.dcjet.cs_cus.cusSt.model.SapTaxCip;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SapTaxCipDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    SapTaxCipDto toDto(SapTaxCip po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    SapTaxCip toPo(SapTaxCipParam param);
    /**
     * 数据库原始数据更新
     * @param sapTaxCipParam
     * @param sapTaxCip
     */
    void updatePo(SapTaxCipParam sapTaxCipParam, @MappingTarget SapTaxCip sapTaxCip);
    default void patchPo(SapTaxCipParam sapTaxCipParam, SapTaxCip sapTaxCip) {
        // TODO 自行实现局部更新
    }
}
