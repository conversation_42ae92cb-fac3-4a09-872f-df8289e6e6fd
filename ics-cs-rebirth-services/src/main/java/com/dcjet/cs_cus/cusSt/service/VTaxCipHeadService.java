package com.dcjet.cs_cus.cusSt.service;

import com.dcjet.cs.dto_cus.cusSt.VTaxCipHeadDto;
import com.dcjet.cs.dto_cus.cusSt.VTaxCipHeadParam;
import com.dcjet.cs_cus.cusSt.dao.VTaxCipHeadMapper;
import com.dcjet.cs_cus.cusSt.mapper.VTaxCipHeadDtoMapper;
import com.dcjet.cs_cus.cusSt.model.VTaxCipHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-7-27
 */
@Service
public class VTaxCipHeadService extends BaseService<VTaxCipHead> {
    @Resource
    private VTaxCipHeadMapper vTaxCipHeadMapper;
    @Resource
    private VTaxCipHeadDtoMapper vTaxCipHeadDtoMapper;
    @Override
    public Mapper<VTaxCipHead> getMapper() {
        return vTaxCipHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param vTaxCipHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<VTaxCipHeadDto>> getListPaged(VTaxCipHeadParam vTaxCipHeadParam, PageParam pageParam) {
        // 启用分页查询
        VTaxCipHead vTaxCipHead = vTaxCipHeadDtoMapper.toPo(vTaxCipHeadParam);
        Page<VTaxCipHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vTaxCipHeadMapper.getList(vTaxCipHead));
        List<VTaxCipHeadDto> vTaxCipHeadDtos = page.getResult().stream().map(head -> {
            VTaxCipHeadDto dto = vTaxCipHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<VTaxCipHeadDto>> paged = ResultObject.createInstance(vTaxCipHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VTaxCipHeadDto> selectAll(VTaxCipHeadParam exportParam, UserInfoToken userInfo) {
        VTaxCipHead vTaxCipHead = vTaxCipHeadDtoMapper.toPo(exportParam);
        vTaxCipHead.setTradeCode(userInfo.getCompany());
        List<VTaxCipHeadDto> vTaxCipHeadDtos = new ArrayList<>();
        List<VTaxCipHead> vTaxCipHeads = vTaxCipHeadMapper.getList(vTaxCipHead);
        if (CollectionUtils.isNotEmpty(vTaxCipHeads)) {
            vTaxCipHeadDtos = vTaxCipHeads.stream().map(head -> {
                VTaxCipHeadDto dto = vTaxCipHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return vTaxCipHeadDtos;
    }
}
