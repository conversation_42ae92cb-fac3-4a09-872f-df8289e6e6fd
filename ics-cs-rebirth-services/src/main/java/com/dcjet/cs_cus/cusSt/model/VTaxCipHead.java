package com.dcjet.cs_cus.cusSt.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-7-27
 */
@Setter
@Getter
@Table(name = "")
public class VTaxCipHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	@Column(name = "CLIENT_TYPE")
	private  String clientType;
	/**
     * 
     */
	@Column(name = "CLIENT_CODE")
	private  String clientCode;
	/**
     * 
     */
	@Column(name = "SHORTNAME")
	private  String shortname;
	/**
     * 
     */
	@Column(name = "TEL")
	private  String tel;
	/**
     * 
     */
	@Column(name = "FAX")
	private  String fax;
	/**
     * 
     */
	@Column(name = "NAME_CH")
	private  String nameCh;
	/**
     * 
     */
	@Column(name = "NAME_EN")
	private  String nameEn;
	/**
     * 
     */
	@Column(name = "ADDRESS_CH")
	private  String addressCh;
	/**
     * 
     */
	@Column(name = "ADDRESS_EN")
	private  String addressEn;
	/**
     * 
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "EMS_NO")
	private  String emsNo;
}
