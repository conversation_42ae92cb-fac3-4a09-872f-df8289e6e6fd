package com.dcjet.cs_cus.cusSt.service;

import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto_cus.cusSt.TaxConInvPackDto;
import com.dcjet.cs.dto_cus.cusSt.TaxConInvPackParam;
import com.dcjet.cs_cus.cusSt.dao.TaxConInvPackMapper;
import com.dcjet.cs_cus.cusSt.mapper.TaxConInvPackDtoMapper;
import com.dcjet.cs_cus.cusSt.model.TaxConInvPack;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-7-22
 */
@Service
public class TaxConInvPackService extends BaseService<TaxConInvPack> {
    @Resource
    private TaxConInvPackMapper taxConInvPackMapper;
    @Resource
    private TaxConInvPackDtoMapper taxConInvPackDtoMapper;
    @Resource
    private CommonService commonService;
    @Override
    public Mapper<TaxConInvPack> getMapper() {
        return taxConInvPackMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param taxConInvPackParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<TaxConInvPackDto>> getListPaged(TaxConInvPackParam taxConInvPackParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        taxConInvPackParam.setInsertUserParam(userInfo.getUserNo());
        taxConInvPackParam.setTradeCode(userInfo.getCompany());
        TaxConInvPack taxConInvPack = taxConInvPackDtoMapper.toPo(taxConInvPackParam);
        Page<TaxConInvPack> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> taxConInvPackMapper.getList(taxConInvPack));
        List<TaxConInvPackDto> taxConInvPackDtos = page.getResult().stream().map(head -> {
            TaxConInvPackDto dto = taxConInvPackDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<TaxConInvPackDto>> paged = ResultObject.createInstance(taxConInvPackDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param taxConInvPackParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TaxConInvPackDto insert(TaxConInvPackParam taxConInvPackParam, UserInfoToken userInfo) {
        TaxConInvPack taxConInvPack = taxConInvPackDtoMapper.toPo(taxConInvPackParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        taxConInvPack.setSid(sid);
        taxConInvPack.setInsertUser(userInfo.getUserNo());
        taxConInvPack.setInsertTime(new Date());
        // 新增数据
        int insertStatus = taxConInvPackMapper.insert(taxConInvPack);
        return  insertStatus > 0 ? taxConInvPackDtoMapper.toDto(taxConInvPack) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param taxConInvPackParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TaxConInvPackDto update(TaxConInvPackParam taxConInvPackParam, UserInfoToken userInfo) {
        TaxConInvPack taxConInvPack = taxConInvPackMapper.selectByPrimaryKey(taxConInvPackParam.getSid());
        taxConInvPackDtoMapper.updatePo(taxConInvPackParam, taxConInvPack);
        taxConInvPack.setUpdateUser(userInfo.getUserNo());
        taxConInvPack.setUpdateTime(new Date());
        // 更新数据
        int update = taxConInvPackMapper.updateByPrimaryKey(taxConInvPack);
        return update > 0 ? taxConInvPackDtoMapper.toDto(taxConInvPack) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		taxConInvPackMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<TaxConInvPackDto> selectAll(TaxConInvPackParam exportParam, UserInfoToken userInfo) {
        TaxConInvPack taxConInvPack = taxConInvPackDtoMapper.toPo(exportParam);
        taxConInvPack.setTradeCode(userInfo.getCompany());
        taxConInvPack.setInsertUser(userInfo.getUserNo());
        List<TaxConInvPackDto> taxConInvPackDtos = new ArrayList<>();
        List<TaxConInvPack> taxConInvPacks = taxConInvPackMapper.getList(taxConInvPack);
        if (CollectionUtils.isNotEmpty(taxConInvPacks)) {
            taxConInvPackDtos = taxConInvPacks.stream().map(head -> {
                TaxConInvPackDto dto = taxConInvPackDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return taxConInvPackDtos;
    }
    /**
     * 功能描述: 查询客户对账单客户数据
     *
     * <AUTHOR>
     * @version :   1.0
     * @date：2019-07-22
     * @param: session
     * @return: ResultObject
     */
    public ResultObject selectCusCombox() {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        List<Map<String, Object>> listResult =taxConInvPackMapper.selectCusCombox();
        List<KeyValuePair> keyValuePairList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(listResult)) {
            for (Map<String, Object> map : listResult) {
                keyValuePairList.add(new KeyValuePair(map.get("VALUE"), map.get("LABEL")));
            }
        }
        result.setData(keyValuePairList);
        return result;
    }
    /**
     * 功能描述: 计算客户对账单数据源
     *
     * <AUTHOR>
     * @version :   1.0
     * @date：2019-07-27
     * @param: session
     * @return: String
     */
    public String  computeTaxPack(String cType,UserInfoToken userInfo)
    {
        String strMsg="";
        Map<String, Object> param = new HashMap<>(6);
        param.put("P_USER_ID", userInfo.getUserNo());
        param.put("P_TYPE", cType);
        param.put("P_TRADE_CODE", userInfo.getCompany());
        param.put("P_FLAG", "");
        param.put("P_RET_CODE", "");
        param.put("P_RET_STR", "");
        Map<String, Object> cusStMap = taxConInvPackMapper.computeTaxPackData(param);
        if (param.get("P_RET_CODE") != null && !param.get("P_RET_CODE").toString().isEmpty()) {
            strMsg = commonService.sqlErrorMsg(param.get("P_RET_CODE") + ":" + param.get("P_RET_STR"));
        } else if (cusStMap != null && cusStMap.get("p_ret_code") != null && !cusStMap.get("p_ret_code").toString().isEmpty()) {
            strMsg = commonService.sqlErrorMsg(cusStMap.get("p_ret_code") + ":" + cusStMap.get("p_ret_str"));
        }
        return strMsg;
    }

    public ResultObject generateCusData(String invNo, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("生成成功"));
        Map<String, Object> pa = new HashMap<String, Object>(4);
        pa.put("P_USER_ID", userInfo.getUserNo());
        pa.put("P_TRADE_CODE",userInfo.getCompany());
        pa.put("P_INV_NO",invNo);
        pa.put("P_RET_CODE", "");
        pa.put("P_RET_STR", "");
        taxConInvPackMapper.generateCusStData(pa);
        if (pa.get("P_RET_CODE") != null && !pa.get("P_RET_CODE").toString().isEmpty()) {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("生成失败：")+pa.get("P_RET_CODE") + ":" + pa.get("P_RET_STR"));
        }
        return result;
    }

    public ResultObject generateOtherCusData(String invNo, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("生成成功"));
        Map<String, Object> pa = new HashMap<String, Object>(4);
        pa.put("P_USER_ID", userInfo.getUserNo());
        pa.put("P_TRADE_CODE",userInfo.getCompany());
        pa.put("P_INV_NO",invNo);
        pa.put("P_RET_CODE", "");
        pa.put("P_RET_STR", "");
        taxConInvPackMapper.generateOtherCusStData(pa);
        if (pa.get("P_RET_CODE") != null && !pa.get("P_RET_CODE").toString().isEmpty()) {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("生成失败：")+pa.get("P_RET_CODE") + ":" + pa.get("P_RET_STR"));
        }
        return result;
    }
}
