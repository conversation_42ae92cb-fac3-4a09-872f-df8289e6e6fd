package com.dcjet.cs_cus.cache;
import java.util.concurrent.*;
import java.util.Map;

public class CacheUtil {
    private final static Map<String, CacheEntity> CACHE_MAP = new ConcurrentHashMap<>();
    private static ScheduledExecutorService executorService= Executors.newSingleThreadScheduledExecutor();

    static {
        executorService.scheduleAtFixedRate(() -> {
            long currentTime= System.currentTimeMillis();
            CACHE_MAP.values().removeIf(entity -> entity.getExpireTime() < currentTime);
        }, 0, 500, TimeUnit.MILLISECONDS);
    }

    public static void put(String key, Object value, long expireTimeInSeconds) {
        long expireTime= System.currentTimeMillis() + expireTimeInSeconds * 1000;
        CACHE_MAP.put(key, new CacheEntity(key, value, expireTime));
    }

    public static Object get(String key) {
        CacheEntity entity= CACHE_MAP.get(key);
        if (entity == null || entity.getExpireTime() < System.currentTimeMillis()) {
            CACHE_MAP.remove(key);
            return null;
        }
        return entity.getCacheValue();
    }

    public static void delete(String key) {
        CACHE_MAP.remove(key);
    }

    public static void clear() {
        CACHE_MAP.clear();
    }
}
