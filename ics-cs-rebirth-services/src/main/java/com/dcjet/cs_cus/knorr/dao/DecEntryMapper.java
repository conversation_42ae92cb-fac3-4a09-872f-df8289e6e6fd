package com.dcjet.cs_cus.knorr.dao;

import com.dcjet.cs_cus.knorr.model.DecEntry;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate dcits
 * 报关时效追踪
 *
 * @author: 鏈辨涓�
 * @date: 2019-03-13
 */
public interface DecEntryMapper extends Mapper<DecEntry> {
    /**
     * 查询获取数据
     *
     * @param decEntry
     * @return
     */
    List<DecEntry> getList(DecEntry decEntry);

    Integer selectDataCountAll(DecEntry decEntry);
}
