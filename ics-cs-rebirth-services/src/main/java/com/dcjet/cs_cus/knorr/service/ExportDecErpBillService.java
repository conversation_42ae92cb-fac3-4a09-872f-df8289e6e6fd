package com.dcjet.cs_cus.knorr.service;

import com.dcjet.cs.dto_cus.knorr.DecErpBillDto;
import com.dcjet.cs.dto_cus.knorr.DecErpBillParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs_cus.knorr.dao.DecErpBillMapper;
import com.dcjet.cs_cus.knorr.mapper.DecErpBillDtoMapper;
import com.dcjet.cs_cus.knorr.model.DecErpBill;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 到货清单异步导出
 * @author: WJ
 * @createDate: 2020/9/14 13:28
 */
@Component
public class ExportDecErpBillService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private DecErpBillMapper decErpBillMapper;

    @Resource
    private DecErpBillDtoMapper decErpBillDtoMapper;

    private final String taskName = xdoi18n.XdoI18nUtil.t("到货清单异步导出(E_DEC_ERP_BILL)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("E_DEC_ERP_BILL");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        DecErpBillParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        DecErpBill decErpBill = decErpBillDtoMapper.toPo(exportParam);

        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(decErpBill));
        Integer count = decErpBillMapper.selectDataCountAll(decErpBill);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        DecErpBillParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        DecErpBill decErpBill = decErpBillDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(decErpBill));
        Page<DecErpBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decErpBillMapper.getList(decErpBill));

        List<DecErpBillDto> decErpBillDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            decErpBillDtos = page.getResult().stream().map(head -> {
                DecErpBillDto dto = decErpBillDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(decErpBillDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    public List<DecErpBillDto> convertForPrint(List<DecErpBillDto> list) {
        for (DecErpBillDto item : list) {
            if (StringUtils.isNotBlank(item.getIEMark())) {
                CommonEnum.I_E_MARK_ENUM.getValue(item.getIEMark());
            }
        }
        return list;
    }

    private DecErpBillParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        DecErpBillParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, DecErpBillParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
