package com.dcjet.cs_cus.knorr.service;

import com.dcjet.cs.dto_cus.knorr.DecLogisticsDto;
import com.dcjet.cs.dto_cus.knorr.DecLogisticsParam;
import com.dcjet.cs_cus.knorr.dao.DecLogisticsMapper;
import com.dcjet.cs_cus.knorr.mapper.DecLogisticsDtoMapper;
import com.dcjet.cs_cus.knorr.model.DecLogistics;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Service
public class DecLogisticsService extends BaseService<DecLogistics> {
    @Resource
    private DecLogisticsMapper decLogisticsMapper;
    @Resource
    private DecLogisticsDtoMapper decLogisticsDtoMapper;
    @Override
    public Mapper<DecLogistics> getMapper() {
        return decLogisticsMapper;
    }


    /**
     * 功能描述 物流查询信息分页查
     * @version 1.0
     * @param decLogisticsParam 物流查询信息查询传入参数
     * @param pageParam 分页参数
     * @param userInfo 当前用户信息
     * @return com.xdo.domain.ResultObject<java.util.List<com.dcjet.cs.dto.erp.DecLogisticsDto>>
     */
    public ResultObject<List<DecLogisticsDto>> getListPaged(DecLogisticsParam decLogisticsParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        DecLogistics decErpBill = decLogisticsDtoMapper.toPo(decLogisticsParam);
        decErpBill.setTradeCode(userInfo.getCompany());
        Page<DecLogistics> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decLogisticsMapper.getList(decErpBill));
        List<DecLogisticsDto> decErpBillDtos = page.getResult().stream().map(head -> {
            DecLogisticsDto dto = decLogisticsDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecLogisticsDto>> paged = ResultObject.createInstance(decErpBillDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecLogisticsDto> logisticsSelectAll(DecLogisticsParam exportParam, UserInfoToken userInfo) {
        DecLogistics decLogistics = decLogisticsDtoMapper.toPo(exportParam);
        decLogistics.setTradeCode(userInfo.getCompany());
        List<DecLogisticsDto> decLogisticsDtos = new ArrayList<>();
        List<DecLogistics> decEntries = decLogisticsMapper.getList(decLogistics);
        if (CollectionUtils.isNotEmpty(decEntries)) {
            decLogisticsDtos = decEntries.stream().map(head -> {
                DecLogisticsDto dto = decLogisticsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decLogisticsDtos;
    }


}
