package com.dcjet.cs_cus.knorr.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate dcits
 * 进口报关单表头
 *
 * @author: 鏈辨涓�
 * @date: 2019-03-13
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_ERP_E_LIST_N")
public class DecEntry extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 进出口标志
     */
    @Transient
    private String IEMark;
    /**
     * 报关单号
     */
    @Transient
    private String entryNo;
    /**
     * 预报单创建日期
     */
    @Transient
    private Date preInsertTime;
    /**
     * 预计到货日期-开始
     */
    @Transient
    private String preInsertTimeFrom;
    /**
     * 预计到货日期-结束
     */
    @Transient
    private String preInsertTimeTo;

    /***
     * 预报单分单日期
     */
    @Transient
    private Date distributeDate;
    /**
     * 接受委托日期
     */
    @Column(name = "PRE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date preDate;
    /**
     * 第一次提交审核日期
     */
    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date minInsertTime;
    /**
     * 终审通过日期
     */
    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date maxInsertTime;
    /**
     * 报关补录入
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Transient
    private Date thirdInsertTime;
    /**
     * 审核通过
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Transient
    private Date extInsertTime;
    /**
     * 报关申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Transient
    private Date DDate;
    /**
     * 预计到货日期-开始
     */
    @Transient
    private String DDateFrom;
    /**
     * 预计到货日期-结束
     */
    @Transient
    private String DDateTo;
    /**
     * 放行日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Transient
    private Date passDate;

}
