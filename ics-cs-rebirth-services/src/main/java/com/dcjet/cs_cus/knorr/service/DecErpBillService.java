package com.dcjet.cs_cus.knorr.service;

import com.dcjet.cs.dto_cus.knorr.DecErpBillDto;
import com.dcjet.cs.dto_cus.knorr.DecErpBillParam;
import com.dcjet.cs_cus.knorr.dao.DecErpBillMapper;
import com.dcjet.cs_cus.knorr.mapper.DecErpBillDtoMapper;
import com.dcjet.cs_cus.knorr.model.DecErpBill;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Service
public class DecErpBillService extends BaseService<DecErpBill> {
    @Resource
    private DecErpBillMapper decErpBillMapper;
    @Resource
    private DecErpBillDtoMapper decErpBillDtoMapper;

    @Override
    public Mapper<DecErpBill> getMapper() {
        return decErpBillMapper;
    }

    /**
     * 功能描述 到货清单信息查询
     * @version 1.0
     * @param decErpBillParam 到货清单信息传入参数
     * @param pageParam 分页参数
     * @param userInfo 当前用户信息
     * @return com.xdo.domain.ResultObject<java.util.List<com.dcjet.cs.dto.erp.DecErpBillDto>>
     */
    public ResultObject<List<DecErpBillDto>> getListPaged(DecErpBillParam decErpBillParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        DecErpBill decErpBill = decErpBillDtoMapper.toPo(decErpBillParam);
        decErpBill.setTradeCode(userInfo.getCompany());
        Page<DecErpBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decErpBillMapper.getList(decErpBill));
        List<DecErpBillDto> decErpBillDtos = page.getResult().stream().map(head -> {
            DecErpBillDto dto = decErpBillDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecErpBillDto>> paged = ResultObject.createInstance(decErpBillDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecErpBillDto> billSelectAll(DecErpBillParam exportParam, UserInfoToken userInfo) {
        DecErpBill decErpBill = decErpBillDtoMapper.toPo(exportParam);
        decErpBill.setTradeCode(userInfo.getCompany());
        List<DecErpBillDto> decErpBillDtos = new ArrayList<>();
        List<DecErpBill> decErpBills = decErpBillMapper.getList(decErpBill);
        if (CollectionUtils.isNotEmpty(decErpBills)) {
            decErpBillDtos = decErpBills.stream().map(head -> {
                DecErpBillDto dto = decErpBillDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decErpBillDtos;
    }


}
