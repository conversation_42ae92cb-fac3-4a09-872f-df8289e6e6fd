package com.dcjet.cs_cus.knorr.mapper;

import com.dcjet.cs.dto_cus.knorr.DecLogisticsDto;
import com.dcjet.cs.dto_cus.knorr.DecLogisticsParam;
import com.dcjet.cs_cus.knorr.model.DecLogistics;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecLogisticsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecLogisticsDto toDto(DecLogistics po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecLogistics toPo(DecLogisticsParam param);
}
