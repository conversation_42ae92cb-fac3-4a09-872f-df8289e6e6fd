package com.dcjet.cs_cus.knorr.service;

import com.dcjet.cs.dto_cus.knorr.DecLogisticsDto;
import com.dcjet.cs.dto_cus.knorr.DecLogisticsParam;
import com.dcjet.cs_cus.knorr.dao.DecLogisticsMapper;
import com.dcjet.cs_cus.knorr.mapper.DecLogisticsDtoMapper;
import com.dcjet.cs_cus.knorr.model.DecLogistics;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 出口清单-表头表体异步导出
 * @author: WJ
 * @createDate: 2020/9/14 13:28
 */
@Component
public class ExportDecLogisticsService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private DecLogisticsMapper decLogisticsMapper;

    @Resource
    private DecLogisticsDtoMapper decLogisticsDtoMapper;

    private final String taskName = xdoi18n.XdoI18nUtil.t("物流查询异步导出(DEC_LOGISTICS)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("DEC_LOGISTICS");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        DecLogisticsParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        DecLogistics decLogistics = decLogisticsDtoMapper.toPo(exportParam);

        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(decLogistics));
        Integer count = decLogisticsMapper.selectDataCountAll(decLogistics);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        DecLogisticsParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        DecLogistics decLogistics = decLogisticsDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(decLogistics));
        Page<DecLogistics> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decLogisticsMapper.getList(decLogistics));

        List<DecLogisticsDto> decLogisticsDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            decLogisticsDtos = page.getResult().stream().map(head -> {
                DecLogisticsDto dto = decLogisticsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(decLogisticsDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    public List<DecLogisticsDto> convertForPrint(List<DecLogisticsDto> list) {
        for (DecLogisticsDto item : list) {
            if (StringUtils.isNotBlank(item.getEntryStatus())) {
                item.setEntryStatus(item.getEntryStatus() + "" + item.getEntryStatusName());
            }
        }
        return list;
    }

    private DecLogisticsParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        DecLogisticsParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, DecLogisticsParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
