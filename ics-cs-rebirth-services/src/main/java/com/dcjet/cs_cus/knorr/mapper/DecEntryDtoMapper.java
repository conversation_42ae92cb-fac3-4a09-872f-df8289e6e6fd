package com.dcjet.cs_cus.knorr.mapper;

import com.dcjet.cs.dto_cus.knorr.DecEntryDto;
import com.dcjet.cs.dto_cus.knorr.DecEntryParam;
import com.dcjet.cs_cus.knorr.model.DecEntry;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate dcits
 * 进口报关单表头
 *
 * @author: 鏈辨涓�
 * @date: 2019-03-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecEntryDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecEntryDto toDto(DecEntry po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecEntry toPo(DecEntryParam param);

}
