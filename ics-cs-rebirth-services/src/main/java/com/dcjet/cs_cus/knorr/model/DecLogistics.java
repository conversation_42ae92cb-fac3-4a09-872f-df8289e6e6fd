package com.dcjet.cs_cus.knorr.model;

import com.dcjet.cs.base.model.BasicModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_ERP_I_HEAD_N")
public class DecLogistics extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * SA
     */
    @Column(name = "SA")
    private String sa;
    /**
     * 单据内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private  String emsListNo;
    /**
     * 清单内部编号
     */
    @Transient
    private  String billEmsListNo;
    /**
     * 提运单号
     */
    @Column(name = "HAWB")
    private  String hawb;
    /**
     * 境外发货人名称
     */
    @Column(name = "OVERSEAS_SHIPPER_NAME")
    private  String overseasShipperName;
    /**
     * DN号
     */
    @Transient
    private  String note1;
    /**
     * 订单号
     */
    @Transient
    private  String orderNo;
    /**
     * 报关状态
     */
    @Transient
    private  String entryStatus;
    /**
     * 报关状态名称
     */
    @Transient
    private  String entryStatusName;
    /**
     * 预计到货日期
     */
    @Transient
    private Date planArrivalDate;
    /**
     * 预计到货日期-开始
     */
    @Transient
    private String planArrivalDateFrom;
    /**
     * 预计到货日期-结束
     */
    @Transient
    private String planArrivalDateTo;
    /**
     * 预计到厂日期
     */
    @Transient
    private Date deliveryDate;
    /**
     * 关务人员
     */
    @Column(name = "INSERT_USER_NAME")
    private  String insertUserParam;
    /**
     * ETA日期
     */
    @ApiModelProperty("ETA日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Column(name = "ARRIVAL_PORT_DATE")
    private  Date arrivalPortDate;

    /**
     * ETA日期-开始
     */
    @Transient
    private String arrivalPortDateFrom;
    /**
     * ETA日期-结束
     */
    @Transient
    private String arrivalPortDateTo;

}
