package com.dcjet.cs_cus.knorr.dao;

import com.dcjet.cs_cus.knorr.model.DecLogistics;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
public interface DecLogisticsMapper extends Mapper<DecLogistics>{

    /**
     * 查询获取数据
     *
     * @param decLogistics
     * @return
     */
    List<DecLogistics> getList(DecLogistics decLogistics);

    Integer selectDataCountAll(DecLogistics decLogistics);
}
