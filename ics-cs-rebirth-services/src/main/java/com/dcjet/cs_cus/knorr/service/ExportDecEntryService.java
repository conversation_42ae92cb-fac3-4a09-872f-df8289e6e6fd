package com.dcjet.cs_cus.knorr.service;

import com.dcjet.cs.dto_cus.knorr.DecEntryDto;
import com.dcjet.cs.dto_cus.knorr.DecEntryParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs_cus.knorr.dao.DecEntryMapper;
import com.dcjet.cs_cus.knorr.mapper.DecEntryDtoMapper;
import com.dcjet.cs_cus.knorr.model.DecEntry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 报关时效追踪异步导出
 * @author: WJ
 * @createDate: 2020/9/14 13:28
 */
@Component
public class ExportDecEntryService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private DecEntryMapper decEntryMapper;

    @Resource
    private DecEntryDtoMapper decEntryDtoMapper;

    private final String taskName = xdoi18n.XdoI18nUtil.t("报关时效追踪异步导出(E_DEC_ENTRY)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("E_DEC_ENTRY");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        DecEntryParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        DecEntry decEntry = decEntryDtoMapper.toPo(exportParam);

        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(decEntry));
        Integer count = decEntryMapper.selectDataCountAll(decEntry);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        DecEntryParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        DecEntry decEntry = decEntryDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(decEntry));
        Page<DecEntry> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decEntryMapper.getList(decEntry));

        List<DecEntryDto> decEntryDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            decEntryDtos = page.getResult().stream().map(head -> {
                DecEntryDto dto = decEntryDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(decEntryDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    public List<DecEntryDto> convertForPrint(List<DecEntryDto> list) {
        for (DecEntryDto item : list) {
            if (StringUtils.isNotBlank(item.getIEMark())) {
                CommonEnum.I_E_MARK_ENUM.getValue(item.getIEMark());
            }
        }
        return list;
    }

    private DecEntryParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        DecEntryParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, DecEntryParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
