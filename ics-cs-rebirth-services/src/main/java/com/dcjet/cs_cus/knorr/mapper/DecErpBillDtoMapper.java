package com.dcjet.cs_cus.knorr.mapper;

import com.dcjet.cs.dto_cus.knorr.DecErpBillDto;
import com.dcjet.cs.dto_cus.knorr.DecErpBillParam;
import com.dcjet.cs_cus.knorr.model.DecErpBill;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecErpBillDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecErpBillDto toDto(DecErpBill po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecErpBill toPo(DecErpBillParam param);

}
