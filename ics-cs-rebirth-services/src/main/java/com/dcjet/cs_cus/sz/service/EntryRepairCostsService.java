package com.dcjet.cs_cus.sz.service;

import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListDto;
import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListParam;
import com.dcjet.cs.dto_cus.sz.EntryRepairCostsDto;
import com.dcjet.cs.dto_cus.sz.EntryRepairCostsParam;
import com.dcjet.cs_cus.panasonic.model.DecErpEFreightList;
import com.dcjet.cs_cus.sz.dao.EntryRepairCostsMapper;
import com.dcjet.cs_cus.sz.mapper.EntryRepairCostsDtoMapper;
import com.dcjet.cs_cus.sz.model.EntryRepairCosts;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * @author: 沈振宇
 * @date: 2019-03-11
 */
@Service
public class EntryRepairCostsService extends BaseService<EntryRepairCosts> {

    @Resource
    private EntryRepairCostsMapper entryRepairCostsMapper;

    @Override
    public Mapper<EntryRepairCosts> getMapper() {
        return entryRepairCostsMapper;
    }

    @Resource
    private EntryRepairCostsDtoMapper entryRepairCostsDtoMapper;


    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param entryRepairCostsParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<EntryRepairCostsDto>> getListPaged(EntryRepairCostsParam entryRepairCostsParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        EntryRepairCosts entryRepairCosts = entryRepairCostsDtoMapper.toPo(entryRepairCostsParam);
        entryRepairCosts.setTradeCode(userInfo.getCompany());
        Page<EntryRepairCosts> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> entryRepairCostsMapper.getList(entryRepairCosts));
        List<EntryRepairCostsDto> entryRepairCostsDtos = page.getResult().stream().map(head -> {
            EntryRepairCostsDto dto = entryRepairCostsDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<EntryRepairCostsDto>> paged = ResultObject.createInstance(entryRepairCostsDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<EntryRepairCostsDto> selectAll(EntryRepairCostsParam exportParam, UserInfoToken userInfo) {
        EntryRepairCosts entryRepairCosts = entryRepairCostsDtoMapper.toPo(exportParam);
        entryRepairCosts.setTradeCode(userInfo.getCompany());
        List<EntryRepairCostsDto> entryRepairCostsDtos = new ArrayList<>();
        List<EntryRepairCosts> entryRepairCostsList = entryRepairCostsMapper.getList(entryRepairCosts);
        if (CollectionUtils.isNotEmpty(entryRepairCostsList)) {
            entryRepairCostsDtos = entryRepairCostsList.stream().map(head -> {
                EntryRepairCostsDto dto = entryRepairCostsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return entryRepairCostsDtos;
    }

}
