package com.dcjet.cs_cus.sz.service;

import com.dcjet.cs.dto_cus.sz.EntryRepairCostsDto;
import com.dcjet.cs.dto_cus.sz.EntryRepairCostsParam;
import com.dcjet.cs.dto_cus.sz.OutboundRepairCostsDto;
import com.dcjet.cs.dto_cus.sz.OutboundRepairCostsParam;
import com.dcjet.cs_cus.sz.dao.EntryRepairCostsMapper;
import com.dcjet.cs_cus.sz.dao.OutboundRepairCostsMapper;
import com.dcjet.cs_cus.sz.mapper.EntryRepairCostsDtoMapper;
import com.dcjet.cs_cus.sz.mapper.OutboundRepairCostsDtoMapper;
import com.dcjet.cs_cus.sz.model.EntryRepairCosts;
import com.dcjet.cs_cus.sz.model.OutboundRepairCosts;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * @author: 沈振宇
 * @date: 2019-03-11
 */
@Service
public class OutboundRepairCostsService extends BaseService<OutboundRepairCosts> {

    @Resource
    private OutboundRepairCostsMapper outboundRepairCostsMapper;

    @Override
    public Mapper<OutboundRepairCosts> getMapper() {
        return outboundRepairCostsMapper;
    }

    @Resource
    private OutboundRepairCostsDtoMapper outboundRepairCostsDtoMapper;


    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param outboundRepairCostsParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<OutboundRepairCostsDto>> getListPaged(OutboundRepairCostsParam outboundRepairCostsParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        OutboundRepairCosts outboundRepairCosts = outboundRepairCostsDtoMapper.toPo(outboundRepairCostsParam);
        outboundRepairCosts.setTradeCode(userInfo.getCompany());
        Page<OutboundRepairCosts> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> outboundRepairCostsMapper.getList(outboundRepairCosts));
        List<OutboundRepairCostsDto> outboundRepairCostsDtos = page.getResult().stream().map(head -> {
            OutboundRepairCostsDto dto = outboundRepairCostsDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<OutboundRepairCostsDto>> paged = ResultObject.createInstance(outboundRepairCostsDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }


    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<OutboundRepairCostsDto> selectAll(OutboundRepairCostsParam exportParam, UserInfoToken userInfo) {
        OutboundRepairCosts outboundRepairCosts = outboundRepairCostsDtoMapper.toPo(exportParam);
        outboundRepairCosts.setTradeCode(userInfo.getCompany());
        List<OutboundRepairCostsDto> outboundRepairCostsDtos = new ArrayList<>();
        List<OutboundRepairCosts> outboundRepairCostsList = outboundRepairCostsMapper.getList(outboundRepairCosts);
        if (CollectionUtils.isNotEmpty(outboundRepairCostsList)) {
            outboundRepairCostsDtos = outboundRepairCostsList.stream().map(head -> {
                OutboundRepairCostsDto dto = outboundRepairCostsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return outboundRepairCostsDtos;
    }


}
