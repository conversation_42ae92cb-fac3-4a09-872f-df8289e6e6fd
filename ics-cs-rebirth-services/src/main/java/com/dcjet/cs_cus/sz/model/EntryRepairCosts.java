package com.dcjet.cs_cus.sz.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Setter
@Getter
@RemoveTailingZero
public class EntryRepairCosts extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @Column(name = "SERIAL_NO")
    private Long serialNo;
    /**
     * 单据内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 客户
     */
    @Column(name = "OVERSEAS_SHIPPER_NAME")
    private String overseasShipperName;
    /**
     * 进/出
     */
    @Column(name = "I_E_MARK")
    private String IEMark;
    /**
     * 申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "DECLARE_DATE")
    private Date declareDate;
    @Transient
    private String declareDateFrom;
    @Transient
    private String declareDateTo;
    /**
     * 合同号
     */
    @Column(name = "CONTR_NO")
    private String contrNo;
    /**
     * S/N
     */
    @Column(name = "REMARK1")
    private String remark1;
    /**
     * HS编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;
    /**
     * 品名
     */
    @Column(name = "G_NAME")
    private String GName;
    /**
     * 型号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 包装件数
     */
    @Column(name = "PACK_NUM")
    private BigDecimal packNum;
    /**
     * 包装类型
     */
    @Column(name = "WRAP_TYPE")
    private String wrapType;
    /**
     * 净重kg
     */
    @Column(name = "NET_WT")
    private BigDecimal netWt;
    /**
     * 毛重kg
     */
    @Column(name = "GROSS_WT")
    private BigDecimal grossWt;
    /**
     * 包装尺寸
     */
    @Column(name = "PACKING")
    private String packing;
    /**
     * 产品数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    /**
     * 单价USD
     */
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;
    /**
     * 总价USD
     */
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;
    /**
     * 贸易方式
     */
    @Column(name = "TRADE_MODE")
    private String tradeMode;
    /**
     * 物流
     */
    @Column(name = "FORWARD_CODE")
    private String forwardCode;
    /**
     * 提运单号
     */
    @Column(name = "HAWB")
    private String hawb;
    /**
     * 运费RMB
     */
    @Column(name = "FEE_RATE")
    private BigDecimal feeRate;
    /**
     * 原报关单号
     */
    @Column(name = "PRIMARY_ENTRY_NO")
    private String primaryEntryNo;
    /**
     * 报关单号
     */
    @Column(name = "ENTRY_NO")
    private String entryNo;
    /**
     * 报关行简码
     */
    @Column(name = "DECLARE_CODE_CUSTOMS")
    private String declareCodeCustoms;
    /**
     * 报关行名称
     */
    @Column(name = "DECLARE_NAME")
    private String declareName;
    /**
     * 进口日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "I_E_DATE")
    private Date iedate;
    @Transient
    private String iedateFrom;
    @Transient
    private String iedateTo;
    /**
     * 消保
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 是否查验
     */
    @Column(name = "INSPECTION")
    private String inspection;
    /**
     * 报关费
     */
    @Column(name = "LOGISTICS_FEE")
    private BigDecimal logisticsFee;
    /**
     * 仓储费
     */
    @Column(name = "CUSTOMS_FEE")
    private BigDecimal customsFee;
    /**
     * 备注2
     */
    @Column(name = "NOTE_PASS")
    private String notePass;
    /**
     * 报关维修费
     */
    @Column(name = "REPAIR_DEC_TOTAL")
    private BigDecimal repairDecTotal;
    /**
     * 废料总价
     */
    @Column(name = "WASTE_DEC_TOTAL")
    private BigDecimal wasteDecTotal;
    /**
     * 总维修费
     */
    @Column(name = "TOTAL_REPAIR_FEE")
    private BigDecimal totalRepairFee;
    /**
     * 废品零件
     */
    @Column(name = "SCRAP_PARTS")
    private String scrapParts;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
}
