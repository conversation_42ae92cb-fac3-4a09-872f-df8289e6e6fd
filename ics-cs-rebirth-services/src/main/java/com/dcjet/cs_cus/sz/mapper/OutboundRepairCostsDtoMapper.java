package com.dcjet.cs_cus.sz.mapper;

import com.dcjet.cs.dto_cus.sz.EntryRepairCostsDto;
import com.dcjet.cs.dto_cus.sz.EntryRepairCostsParam;
import com.dcjet.cs.dto_cus.sz.OutboundRepairCostsDto;
import com.dcjet.cs.dto_cus.sz.OutboundRepairCostsParam;
import com.dcjet.cs_cus.sz.model.EntryRepairCosts;
import com.dcjet.cs_cus.sz.model.OutboundRepairCosts;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OutboundRepairCostsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    OutboundRepairCostsDto toDto(OutboundRepairCosts po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    OutboundRepairCosts toPo(OutboundRepairCostsParam param);

    /**
     * 数据库原始数据更新
     *
     * @param outboundRepairCostsParam
     * @param outboundRepairCosts
     */
    void updatePo(OutboundRepairCostsParam outboundRepairCostsParam, @MappingTarget OutboundRepairCosts outboundRepairCosts);

    default void patchPo(OutboundRepairCostsParam outboundRepairCostsParam, OutboundRepairCosts outboundRepairCosts) {
        // TODO 自行实现局部更新
    }
}
