package com.dcjet.cs_cus.sz.dao;

import com.dcjet.cs_cus.sz.model.EntryRepairCosts;
import com.dcjet.cs_cus.sz.model.OutboundRepairCosts;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwstdEmptyManagement
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
public interface OutboundRepairCostsMapper extends Mapper<OutboundRepairCosts> {
    /**
     * 查询获取数据
     *
     * @param outboundRepairCosts
     * @return
     */
    List<OutboundRepairCosts> getList(OutboundRepairCosts outboundRepairCosts);

}
