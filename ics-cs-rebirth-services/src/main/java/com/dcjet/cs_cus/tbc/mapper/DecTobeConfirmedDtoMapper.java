package com.dcjet.cs_cus.tbc.mapper;
import com.dcjet.cs.dto_cus.tbc.DecTobeConfirmedDto;
import com.dcjet.cs.dto_cus.tbc.DecTobeConfirmedParam;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs_cus.tbc.model.DecTobeConfirmed;
import org.mapstruct.*;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecTobeConfirmedDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecTobeConfirmedDto toDto(DecTobeConfirmed po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecTobeConfirmed toPo(DecTobeConfirmedParam param);
    /**
     * 数据库原始数据更新
     * @param decTobeConfirmedParam
     * @param decTobeConfirmed
     */
    void updatePo(DecTobeConfirmedParam decTobeConfirmedParam, @MappingTarget DecTobeConfirmed decTobeConfirmed);
    default void patchPo(DecTobeConfirmedParam decTobeConfirmedParam, DecTobeConfirmed decTobeConfirmed) {
        // TODO 自行实现局部更新
    }

    @Mappings({
            @Mapping(source = "insertTime", target = "erpInsertTime"),
            @Mapping(source = "insertUser", target = "erpInsertUser"),
            @Mapping(source = "insertUserName", target = "erpInsertUserName"),
    })
    void updatePo(DecErpIHeadN decErpIHeadN, @MappingTarget DecTobeConfirmed decTobeConfirmed);
    @Mappings({
            @Mapping(source = "insertTime", target = "erpInsertTime"),
            @Mapping(source = "insertUser", target = "erpInsertUser"),
            @Mapping(source = "insertUserName", target = "erpInsertUserName"),
    })
    void updatePo(DecErpEHeadN decErpEHeadN, @MappingTarget DecTobeConfirmed decTobeConfirmed);
}
