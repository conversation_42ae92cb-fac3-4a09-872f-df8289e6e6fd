package com.dcjet.cs_cus.tbc.model;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("待确认列表退回参数")
public class ToBeConfirmRefuseParams {

    @ApiModelProperty("主键列表")
    private List<String> sids;

    @ApiModelProperty("拒绝原因")
    @XdoSize(max = 500, message = "拒绝原因长度不能超过500位字节长度(一个汉字2位字节长度)!")
    private String refuseReason;
}
