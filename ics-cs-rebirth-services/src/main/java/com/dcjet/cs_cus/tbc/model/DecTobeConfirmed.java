package com.dcjet.cs_cus.tbc.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-8-20
 */
@Setter
@Getter
@Table(name = "T_DEC_TOBE_CONFIRMED")
public class DecTobeConfirmed implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String CONFIRM_STATUS_NO_FLOW = "0";
    public static final String CONFIRM_STATUS_PURCHASE_TO_BE_CONFIRMED = "1";
	public static final String CONFIRM_STATUS_PURCHASE_REFUSED = "-1";
	public static final String CONFIRM_STATUS_FINANCE_TO_BE_CONFIRMED = "2";
	public static final String CONFIRM_STATUS_FINANCE_REFUSED = "-2";
	public static final String CONFIRM_STATUS_CONFIRMED = "3";
	public static final String CONFIRM_STATUS_FILTERED = "4";


	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 退回原因
     */
	@Column(name = "REFUSE_REASON")
	private  String refuseReason;
	/**
     * 确认状态0 未流转 1采购待确认 -1采购已退回 2财务待确认 -2财务已退回 3已确认 4 已过滤
     */
	@Column(name = "CONFIRM_STATUS")
	private  String confirmStatus;
	/**
     * 预录入单表头ID
     */
	@Column(name = "DEC_ERP_ID")
	private  String decErpId;
	/**
     * 单据内部编号
     */
	@Column(name = "EMS_LIST_NO")
	private  String emsListNo;
	/**
     * 制单日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "ERP_INSERT_TIME")
	private  Date erpInsertTime;
	/**
     * 制单日期-开始
     */
	@Transient
	private String erpInsertTimeFrom;
	/**
     * 制单日期-结束
     */
	@Transient
    private String erpInsertTimeTo;
	/**
     * 监管方式
     */
	@Column(name = "TRADE_MODE")
	private  String tradeMode;
	/**
     * 备案号
     */
	@Column(name = "EMS_NO")
	private  String emsNo;
	/**
     * 制单人
     */
	@Column(name = "ERP_INSERT_USER")
	private  String erpInsertUser;
	/**
     * 发票号
     */
	@Column(name = "INVOICE_NO")
	private  String invoiceNo;
	/**
     * 报关单申报单位
     */
	@Column(name = "AGENT_CODE")
	private  String agentCode;
	/**
     * 报关单申报单位名称
     */
	@Column(name = "AGENT_NAME")
	private  String agentName;
	/**
     * 报关单申报单位社会统一信用代码
     */
	@Column(name = "AGENT_CREDIT_CODE")
	private  String agentCreditCode;
	/**
     * 境外收发货人
     */
	@Column(name = "OVERSEAS_SHIPPER")
	private  String overseasShipper;
	/**
     * 境外收发货人名称
     */
	@Column(name = "OVERSEAS_SHIPPER_NAME")
	private  String overseasShipperName;
	/**
     * 运输方式
     */
	@Column(name = "TRAF_MODE")
	private  String trafMode;
	/**
     * 运输工具名称
     */
	@Column(name = "TRAF_NAME")
	private  String trafName;
	/**
     * 合同协议号
     */
	@Column(name = "CONTR_NO")
	private  String contrNo;
	/**
     * 内审员
     */
	@Column(name = "INTERNAL_AUDIT_USER")
	private  String internalAuditUser;
	/**
     * 内部备注
     */
	@Column(name = "REMARK")
	private  String remark;
	/**
     * 入/离境口岸
     */
	@Column(name = "ENTRY_PORT")
	private  String entryPort;
	/**
     * 报关单号
     */
	@Column(name = "ENTRY_NO")
	private  String entryNo;
	/**
     * 主提运单
     */
	@Column(name = "MAWB")
	private  String mawb;
	/**
     * 分提运单
     */
	@Column(name = "HAWB")
	private  String hawb;
	/**
     * 保完税标识 0 保税 1 非保税
     */
	@Column(name = "BOND_MARK")
	private  String bondMark;
	/**
     * 进出口标记
     */
	@Column(name = "I_E_MARK")
	@JsonProperty("iEMark")
	private  String iEMark;
	/**
     * 总净重
     */
	@Column(name = "NET_WT")
	private  BigDecimal netWt;
	/**
     * 总毛重
     */
	@Column(name = "GROSS_WT")
	private  BigDecimal grossWt;
	/**
     * 总体积
     */
	@Column(name = "VOLUME")
	private  BigDecimal volume;
	/**
     * 件数
     */
	@Column(name = "PACK_NUM")
	private  Integer packNum;
	/**
     * 运费币制
     */
	@Column(name = "FEE_CURR")
	private  String feeCurr;
	/**
     * 运费类型
     */
	@Column(name = "FEE_MARK")
	private  String feeMark;
	/**
     * 运费
     */
	@Column(name = "FEE_RATE")
	private  BigDecimal feeRate;
	/**
     * 保费币制
     */
	@Column(name = "INSUR_CURR")
	private  String insurCurr;
	/**
     * 保费类型
     */
	@Column(name = "INSUR_MARK")
	private  String insurMark;
	/**
     * 保费
     */
	@Column(name = "INSUR_RATE")
	private  BigDecimal insurRate;
	/**
     * 杂费币制
     */
	@Column(name = "OTHER_CURR")
	private  String otherCurr;
	/**
     * 杂费类型
     */
	@Column(name = "OTHER_MARK")
	private  String otherMark;
	/**
	 * 杂费
	 */
	@Column(name = "OTHER_RATE")
	private  BigDecimal otherRate;
	/**
     * 总金额
     */
	@Column(name = "SUM_DEC_TOTAL")
	private  BigDecimal sumDecTotal;
	/**
     * 总数量
     */
	@Column(name = "SUM_QTY")
	private  BigDecimal sumQty;
	/**
     * 航次号
     */
	@Column(name = "VOYAGE_NO")
	private  String voyageNo;
	/**
     * 计费重量
     */
	@Column(name = "C_WEIGHT")
	@JsonProperty("cWeight")
	private  BigDecimal cweight;
	/**
     * 采购确认用户
     */
	@Column(name = "PURCHASE_COMFIRMER")
	private  String purchaseComfirmer;
	/**
     * 财务确认用户
     */
	@Column(name = "FINANCE_COMFIRMER")
	private  String financeComfirmer;
	/**
     * 采购确认日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "PURCHASE_COMFIRM_TIME")
	private  Date purchaseComfirmTime;
	/**
     * 财务确认日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "FINANCE_COMFIRM_TIME")
	private  Date financeComfirmTime;
	/**
     * 内审日期/插入日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INTERNAL_AUDIT_TIME")
	private  Date internalAuditTime;

	@Column(name = "TRADE_CODE")
	private String tradeCode;

	@Column(name = "VAT_INVOICE_NO")
	private String vatInvoiceNo;

	@Column(name = "PURCHASE_COMFIRMER_NAME")
	private String purchaseComfirmerName;

	@Column(name = "FINANCE_COMFIRMER_NAME")
	private String financeComfirmerName;

	@Column(name = "INTERNAL_AUDIT_USER_NAME")
	private String internalAuditUserName;

	@Column(name = "ERP_INSERT_USER_NAME")
	private String erpInsertUserName;
	/**
	 * 进出口标记
	 */
	@Column(name = "I_E_DATE")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date iedate;

	@Column(name = "INVITE_DATE")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date inviteDate;
	/**
	 * 币制
	 */
	@Column(name = "CURR")
	private String curr;

	/**
	 * 内审状态
	 */
	@Transient
	private String apprStatus;
	@Transient
	private String pageMark;

	/**
	 * 确认状态（前端展示）0未流转 -1采购退回 -2财务退回 5已流转"
	 */
	@Transient
	private String confirmStatusDesc;
	/**
	 * 采购确认状态 0未确认 1已确认"
	 */
	@Transient
	private String purchaseConfirmStatus;

	/**
	 *财务确认状态 0未确认 1已确认
	 */
	@Transient
	private String financeConfirmStatus;

	/**
	 * 采购确认用户 0 空 1 非空"
	 */
	@Transient
	private String purchaseConfirm;

	/**
	 * 发票总金额
	 */
	@Column(name = "TOTAL_INVOICE_AMOUNT")
	private BigDecimal totalInvoiceAmount;


	public void newRecord(UserInfoToken token) {
		this.setSid(UUID.randomUUID().toString());
		this.setEntryNo(entryNo);
		this.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_NO_FLOW);
		this.setInternalAuditUser(token.getUserNo());
		this.setInternalAuditUserName(token.getUserName());
		this.setInternalAuditTime(new Date());
	}

	public void cleanConfirmInfo() {
		this.setPurchaseComfirmer(null);
		this.setPurchaseComfirmerName(null);
		this.setPurchaseComfirmTime(null);

		this.setFinanceComfirmer(null);
		this.setFinanceComfirmerName(null);
		this.setFinanceComfirmTime(null);

		this.setRefuseReason(null);
//		this.setVatInvoiceNo(null);
	}
}
