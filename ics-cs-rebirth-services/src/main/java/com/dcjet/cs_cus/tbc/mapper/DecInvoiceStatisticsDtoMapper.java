package com.dcjet.cs_cus.tbc.mapper;

import com.dcjet.cs.dto_cus.tbc.DecInvoiceStatisticsDto;
import com.dcjet.cs.dto_cus.tbc.DecInvoiceStatisticsParam;
import com.dcjet.cs_cus.tbc.model.DecInvoiceStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-12-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecInvoiceStatisticsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecInvoiceStatisticsDto toDto(DecInvoiceStatistics po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecInvoiceStatistics toPo(DecInvoiceStatisticsParam param);


    /***
     * 转换DTO到数据库对象
     * @param decInvoiceStatistics
     * @return
     */
    DecInvoiceStatisticsParam toParam(DecInvoiceStatistics decInvoiceStatistics);

    /**
     * 数据库原始数据更新
     *
     * @param decInvoiceStatisticsParam
     * @param decInvoiceStatistics
     */
    void updatePo(DecInvoiceStatisticsParam decInvoiceStatisticsParam, @MappingTarget DecInvoiceStatistics decInvoiceStatistics);

    default void patchPo(DecInvoiceStatisticsParam decInvoiceStatisticsParam, DecInvoiceStatistics decInvoiceStatistics) {
        // TODO 自行实现局部更新
    }
}
