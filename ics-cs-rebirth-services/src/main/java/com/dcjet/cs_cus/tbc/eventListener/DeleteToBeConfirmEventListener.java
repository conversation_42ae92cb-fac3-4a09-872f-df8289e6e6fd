package com.dcjet.cs_cus.tbc.eventListener;

import com.dcjet.cs.base.event.CRUDEnum;
import com.dcjet.cs.erp.event.DecErpEHeadEvent;
import com.dcjet.cs.erp.event.DecErpIHeadEvent;
import com.dcjet.cs_cus.tbc.service.DecTobeConfirmedService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

@Component
public class DeleteToBeConfirmEventListener {
    private static final Logger log = LoggerFactory.getLogger(DeleteToBeConfirmEventListener.class);

    @Resource
    private DecTobeConfirmedService decTobeConfirmedService;


    @TransactionalEventListener(value = DecErpIHeadEvent.class)
    public void onDecErpIHeadEvent(DecErpIHeadEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("监听到DecErpIHeadEvent事件 {}", event);
        }
        if (event.getCrud() == CRUDEnum.DELETE) {
            event.validation();

            decTobeConfirmedService.delete(event.getDecErpIHeadN().getSid());
        }
    }


    @TransactionalEventListener(value = DecErpEHeadEvent.class)
    public void onDecErpEHeadEvent(DecErpEHeadEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("监听到DecErpEHeadEvent事件 {}", event);
        }
        if (event.getCrud() == CRUDEnum.DELETE) {
            event.validation();

            decTobeConfirmedService.delete(event.getDecErpEHeadN().getSid());
        }
    }






}
