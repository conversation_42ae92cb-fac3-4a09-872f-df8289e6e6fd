package com.dcjet.cs_cus.tbc.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-12-25
 */
@Setter
@Getter
@Table(name = "T_DEC_INVOICE_STATISTICS")
public class DecInvoiceStatistics implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 发票号
     */
    @Column(name = "INVOICE_NO")
    private String invoiceNo;
    /**
     * 发票金额
     */
    @Column(name = "INVOICE_AMOUNT")
    private BigDecimal invoiceAmount;
    /**
     * 表头sid
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 企业十位编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
}
