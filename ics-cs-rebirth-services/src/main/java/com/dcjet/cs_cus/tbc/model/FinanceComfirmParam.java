package com.dcjet.cs_cus.tbc.model;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("财务确认参数")
public class FinanceComfirmParam {

    @ApiModelProperty("sid")
    private String sid;

    @XdoSize(max = 50, message = "财务增值税发票号长度不能超过50位字节长度!")
//    @NotBlank(message = "财务增值税发票号不可为空")
    @ApiModelProperty("财务增值税发票号")
    private String vatInvoiceNo;
}
