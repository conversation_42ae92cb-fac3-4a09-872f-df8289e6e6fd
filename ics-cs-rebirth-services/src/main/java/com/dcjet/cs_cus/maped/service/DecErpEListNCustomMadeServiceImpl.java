package com.dcjet.cs_cus.maped.service;

import com.dcjet.cs.base.service.impl.BasicServiceImpl;
import com.dcjet.cs.bi.dao.BiCustomerParamsMapper;
import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.erp.DecErpEListNDto;
import com.dcjet.cs.dto.erp.DecErpEListNParam;
import com.dcjet.cs.dto.mat.MatIeInfoDto;
import com.dcjet.cs.dto.mat.MatIeInfoParam;
import com.dcjet.cs.dto_cus.maped.DecErpEListNImportCustomMadeParam;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpEListNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.erp.service.DecCommonService;
import com.dcjet.cs.erp.service.DecErpEListNService;
import com.dcjet.cs.mat.dao.MatImgexgMapper;
import com.dcjet.cs.mat.dao.MatImgexgOrgMapper;
import com.dcjet.cs.mat.model.MatImgexg;
import com.dcjet.cs.mat.service.MatImgexgOrgService;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs_cus.maped.dao.DecErpIListNCustomMadeMapper;
import com.dcjet.cs_cus.maped.mapper.DecErpEListNCustomMadeDtoMapper;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class DecErpEListNCustomMadeServiceImpl
        extends BasicServiceImpl<DecErpIListNCustomMadeMapper, DecErpEListN, DecErpEListNDto, DecErpEListNParam, DecErpEListNImportCustomMadeParam, DecErpEListNCustomMadeDtoMapper>
        implements DecErpEListNCustomMadeService {
    @Resource
    private BulkInsertConfig bulkInsertConfig;
    public IBulkInsert getBulkInsertInstance() throws Exception{
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    @Resource
    private BiCustomerParamsMapper biCustomerParamsMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private MatImgexgOrgMapper matImgexgOrgMapper;
    @Resource
    private DecErpEListNMapper decErpEListNMapper;
    @Resource
    private MatImgexgMapper matImgexgMapper;
    @Resource
    private MatImgexgOrgService matImgexgOrgService;
    @Resource
    private DecErpEListNCustomMadeDtoMapper decErpEListNCustomMadeDtoMapper;
    @Resource
    private DecCommonService decCommonService;

    @Override
    public ImportValidation<DecErpEListNImportCustomMadeParam> importValidation(ImportData<DecErpEListNImportCustomMadeParam> importData, ExcelImportParam param, UserInfoToken token) {
        List<DecErpEListNImportCustomMadeParam> correctList = importData.getCorrectData();
        Map<String, Object> bizParam = importData.getBizParam();

        List<DecErpEListNImportCustomMadeParam> corrects = new ArrayList<>(correctList.size());
        String headId = Optional.ofNullable(bizParam.get("headId")).orElseThrow(() -> new ArgumentException(xdoi18n.XdoI18nUtil.t("headId不能为空"))).toString();
        List<DecErpEListNImportCustomMadeParam> errors = new ArrayList<>(correctList.size());
        DecErpEHeadN headN = decErpEHeadNMapper.selectByPrimaryKey(headId);
        for (DecErpEListNImportCustomMadeParam val : correctList) {
            val.setHeadId(headId);
            checkData(val,headN,token,importData.getHeadFieldMap());

            if (val.getErrMsg()==null||val.getErrMsg()==""){
                corrects.add(val);
            }else {
                errors.add(val);
            }
        }

        return new ImportValidation<>(corrects, errors);
    }

    /**
     * 数据校验
     *
     * @param val
     * @param headN
     * @param token
     * @param headFieldMap
     * @return
     */
    private DecErpEListNImportCustomMadeParam checkData(DecErpEListNImportCustomMadeParam val, DecErpEHeadN headN, UserInfoToken token, Map<String, String> headFieldMap) {

        if (headN!=null) {
            //根据企业料号和备案号查询物料中心数据
            List<MatIeInfoDto> matImgexgs = matImgexgOrgMapper.selectMatList(val.getFacGNo(),token.getCompany());
            if (matImgexgs.size()>0){
                MatIeInfoDto matImgexg = matImgexgs.get(0);
                //计算申报数量
                if (val.getOrderQty()!=null&&matImgexg.getFactorErp()!=null) {
                    val.setQty(val.getOrderQty().multiply(new BigDecimal(matImgexg.getFactorErp())).setScale(5, BigDecimal.ROUND_HALF_UP));
                }
                //计算申报总价
                if (val.getOrderQty()!=null&&val.getOrderPrice()!=null){
                    val.setDecTotal(val.getOrderQty().multiply(val.getOrderPrice()).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                //计算申报单价
                if (val.getDecTotal()!=null&&val.getQty()!=null){
                    val.setDecPrice(val.getDecTotal().divide(val.getQty(),4,BigDecimal.ROUND_HALF_UP));
                }else {
                    val.setDecPrice(new BigDecimal(0));
                }
                //计算法一、法二数量
//                if (val.getQty()!=null&&matImgexg.getFactor1()!=null){
//                    val.setQty1(val.getQty().multiply(matImgexg.getFactor1()));
//                }
                BigDecimal netWt;
                if (matImgexg.getNetWt() == null) {
                    netWt = null;
                } else {
                    netWt = matImgexg.getNetWt();
                }
                if (val.getNetWt() == null && netWt != null) {
                    val.setNetWt(netWt.multiply(val.getQty()).setScale(8, BigDecimal.ROUND_HALF_UP));
                }

                if (val.getQty1() == null) {
                    val.setQty1(decCommonService.calculateLegalUnit(val.getQty(), matImgexg.getUnit(), matImgexg.getUnit1(), matImgexg.getFactor1(), val.getNetWt(), token.getCompany()));
                }
                if (val.getQty2() == null) {
                    val.setQty2(decCommonService.calculateLegalUnit(val.getQty(), matImgexg.getUnit(), matImgexg.getUnit2(), matImgexg.getFactor2(), val.getNetWt(), token.getCompany()));
                }

                val.setBondMark(matImgexg.getBondedFlag());
                val.setStatus(ConstantsStatus.STATUS_0);
                val.setCurr("502");
                val.setDistrictCode("32239");
                val.setOriginCountry("142");
                val.setGMark(matImgexg.getGMark());
                //判断是保税还是非保税
                if (StringUtils.equals(matImgexg.getBondedFlag(), ConstantsStatus.STATUS_0)){
                    val.setTradeMode(headN.getTradeMode());
                    val.setDutyMode("3");
                    val.setEmsNo(headN.getEmsNo());
                }else {
                    val.setTradeMode("0110");
                    val.setDutyMode("1");
                    val.setEmsNo("");
                }
                //国别转换
                String destinationCountry = biCustomerParamsMapper.selectdestinationCountry(val.getDestinationCountry(),token.getCompany());
                if (StringUtils.isNotBlank(destinationCountry)){
                    val.setDestinationCountry(destinationCountry);
                }else {
                    val.setErrMsg(val.getErrMsg()+xdoi18n.XdoI18nUtil.t("最终目的国在企业参数库(国别)不存在/"));
                }
                val.setEmsListNo(headN.getEmsListNo());

                MatIeInfoParam matIeInfoParam = new MatIeInfoParam();
                matIeInfoParam.setBondedFlag(val.getBondMark());
                matIeInfoParam.setCopGNo("");
                matIeInfoParam.setEmsNo(matImgexg.getEmsNo());
                matIeInfoParam.setFacGNo(val.getFacGNo());
                matIeInfoParam.setGMark(matImgexg.getGMark());
                matIeInfoParam.setHeadId(headN.getSid());
                matIeInfoParam.setIEMark("E");
                ResultObject<MatIeInfoDto> mat = matImgexgOrgService.getIeInfo(matIeInfoParam,token,"F");
                if (mat.getData()!=null){
                    val.setCopGNo(mat.getData().getCopGNo());
                    val.setGNo(mat.getData().getSerialNo());
                    val.setCodeTS(mat.getData().getCodeTS());
                    val.setUnit(mat.getData().getUnit());
                    val.setUnit1(mat.getData().getUnit1());
                    val.setUnit2(mat.getData().getUnit2());
                    val.setGModel(mat.getData().getGModel());
                    val.setGName(mat.getData().getGName());
                }

            }else {
                val.setErrMsg(val.getErrMsg()+xdoi18n.XdoI18nUtil.t("物料在物料中心不存在，请确认/"));
            }
        }

        return val;
    }


    @Override
    public int importSave(ImportData<DecErpEListNImportCustomMadeParam> data, UserInfoToken token) {
        if (data.getBizParam() == null) {
            throw new ArgumentException();
        }

        Date date = new Date();
        int sum = 0;
        //获取表头headId
        String headId = Optional.ofNullable(data.getBizParam().get("headId")).orElseThrow(() -> new ArgumentException(xdoi18n.XdoI18nUtil.t("headId不能为空"))).toString();

        List<DecErpEListN> decErpEListNS = new ArrayList<>();
        Integer serialNo = decErpEListNMapper.getMaxSerialNo(headId);
        for (DecErpEListNImportCustomMadeParam param : data.getCorrectData()) {
            serialNo++;
            DecErpEListN listN = decErpEListNCustomMadeDtoMapper.toPo(param);
            listN.setSid(UUID.randomUUID().toString());
            listN.setInsertTime(date);
            listN.setSerialNo(new BigDecimal(serialNo));
            listN.setInsertUser(token.getUserNo());
            listN.setInsertUserName(token.getUserName());
            listN.setTradeCode(token.getCompany());
            listN.setWarningMark(ConstantsStatus.STATUS_0);
            listN.setDataSource("2");
            decErpEListNS.add(listN);
            sum++;
        }

        try {
            IBulkInsert iBulkInsert = getBulkInsertInstance();
            int intEffectCount = 0;
            try {
                intEffectCount = iBulkInsert.fastImport(decErpEListNS);
            } catch (BulkInsertException ex) {
                intEffectCount = ex.getCommitRowCount();
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
            } catch (Exception ex) {
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
            }
            //更新表体汇总数据到表头
            DecErpEListN decErpSumAll = decErpEListNMapper.selectSumAll(headId);
            DecErpEHeadN decErpEHeadN = new DecErpEHeadN();
            decErpEHeadN.setSid(headId);
            decErpEHeadN.setQtyAll(decErpSumAll.getQtyAll());
            decErpEHeadN.setTotalAll(decErpSumAll.getTotalAll());
            decErpEHeadN.setGNameAll(decErpSumAll.getGNameAll());
            decErpEHeadNMapper.updateByPrimaryKeySelective(decErpEHeadN);


        } catch (Exception e) {
            e.printStackTrace();
        }

        return sum;
    }


}
