package com.dcjet.cs_cus.maped.mapper;

import com.dcjet.cs.base.mapper.BasicConverter;
import com.dcjet.cs.dto.erp.DecErpEListNDto;
import com.dcjet.cs.dto.erp.DecErpEListNParam;
import com.dcjet.cs.dto_cus.maped.DecErpEListNImportCustomMadeParam;
import com.dcjet.cs.erp.model.DecErpEListN;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecErpEListNCustomMadeDtoMapper extends BasicConverter<DecErpEListN, DecErpEListNDto, DecErpEListNParam, DecErpEListNImportCustomMadeParam> {

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecErpEListN toPo(DecErpEListNImportCustomMadeParam param);

}