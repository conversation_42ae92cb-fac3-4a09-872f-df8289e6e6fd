package com.dcjet.cs_cus.mudong.dao;
import com.dcjet.cs_cus.mudong.model.MatNotice;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* MatNotice
* <AUTHOR>
* @date: 2021-8-16
*/
public interface MatNoticeMapper extends Mapper<MatNotice> {
    /**
     * 查询获取数据
     * @param matNotice
     * @return
     */
    List<MatNotice> getList(MatNotice matNotice);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据sid查询所有需要删除的数据
     * @param sids
     * @return
     */
    List<MatNotice> getdeletelist(List<String> sids);

    Integer chekCopGNo(MatNotice matNotice);
}
