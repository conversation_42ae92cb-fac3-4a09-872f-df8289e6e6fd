package com.dcjet.cs_cus.mudong.mapper;

import com.dcjet.cs.dto_cus.mudong.MatNoticeDto;
import com.dcjet.cs.dto_cus.mudong.MatNoticeParam;
import com.dcjet.cs_cus.mudong.model.MatNotice;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MatNoticeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    MatNoticeDto toDto(MatNotice po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    MatNotice toPo(MatNoticeParam param);

    /**
     * 数据库原始数据更新
     *
     * @param matNoticeParam
     * @param matNotice
     */
    void updatePo(MatNoticeParam matNoticeParam, @MappingTarget MatNotice matNotice);

    default void patchPo(MatNoticeParam matNoticeParam, MatNotice matNotice) {
        // TODO 自行实现局部更新
    }
}
