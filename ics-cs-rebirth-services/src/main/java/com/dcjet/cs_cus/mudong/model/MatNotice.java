package com.dcjet.cs_cus.mudong.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-16
 */
@Setter
@Getter
@Table(name = "T_MAT_NOTICE")
public class MatNotice extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 状态 0 暂存 1 已提交 2 已处理
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 备案标记
     */
    @Column(name = "MODIFY_MARK")
    private String modifyMark;
    /**
     * 备案料号
     */
    @Column(name = "COP_G_NO")
    private String copGNo;
    /**
     * 提交人
     */
    @Column(name = "SEND_USER")
    private String sendUser;
    /**
     * 提交时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "SEND_DATE")
    private Date sendDate;
    /**
     * 处理人
     */
    @Column(name = "APPR_USER")
    private String apprUser;
    /**
     * 处理日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "APPR_DATE")
    private Date apprDate;

    /**
     * 提交人名称
     */
    @Column(name = "SEND_USER_NAME")
    private String sendUserName;
    /**
     * 处理人名称
     */
    @Column(name = "APPR_USER_NAME")
    private String apprUserName;
}
