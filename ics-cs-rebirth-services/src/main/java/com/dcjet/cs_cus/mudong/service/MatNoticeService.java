package com.dcjet.cs_cus.mudong.service;

import com.dcjet.cs.dto_cus.mudong.MatNoticeDto;
import com.dcjet.cs.dto_cus.mudong.MatNoticeParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs_cus.mudong.dao.MatNoticeMapper;
import com.dcjet.cs_cus.mudong.mapper.MatNoticeDtoMapper;
import com.dcjet.cs_cus.mudong.model.MatNotice;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-8-16
 */
@Service
public class MatNoticeService extends BaseService<MatNotice> {
    @Resource
    private MatNoticeMapper matNoticeMapper;
    @Resource
    private MatNoticeDtoMapper matNoticeDtoMapper;

    @Override
    public Mapper<MatNotice> getMapper() {
        return matNoticeMapper;
    }

    /**
     * 获取分页信息
     *
     * @param matNoticeParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<MatNoticeDto>> getListPaged(MatNoticeParam matNoticeParam, PageParam pageParam, UserInfoToken userInfoToken) {
        // 启用分页查询
        MatNotice matNotice = matNoticeDtoMapper.toPo(matNoticeParam);
        matNotice.setTradeCode(userInfoToken.getCompany());
        Page<MatNotice> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> matNoticeMapper.getList(matNotice));
        List<MatNoticeDto> matNoticeDtos = page.getResult().stream().map(head -> {
            MatNoticeDto dto = matNoticeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<MatNoticeDto>> paged = ResultObject.createInstance(matNoticeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param matNoticeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MatNoticeDto insert(MatNoticeParam matNoticeParam, UserInfoToken userInfo) {
        MatNotice matNotice = matNoticeDtoMapper.toPo(matNoticeParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        matNotice.setSid(sid);
        matNotice.setInsertUser(userInfo.getUserNo());
        matNotice.setInsertUserName(userInfo.getUserName());
        matNotice.setTradeCode(userInfo.getCompany());
        matNotice.setInsertTime(new Date());
        matNotice.setStatus(ConstantsStatus.STATUS_0);

        if (StringUtils.equals(matNotice.getModifyMark(), CommonEnum.modifyMarkEnum.MODIFY_MARK_3.getValue())) {
            boolean copGNo = chekCopGNo(matNotice);
            if (copGNo) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("备案料号已存在"));
            }
        }

        // 新增数据
        int insertStatus = matNoticeMapper.insert(matNotice);
        return insertStatus > 0 ? matNoticeDtoMapper.toDto(matNotice) : null;
    }

    private boolean chekCopGNo(MatNotice matNotice) {
        Integer sum = matNoticeMapper.chekCopGNo(matNotice);
        if (sum == null) {
            sum = 0;
        }
        return sum > 0;
    }

    /**
     * 功能描述:修改
     *
     * @param matNoticeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MatNoticeDto update(MatNoticeParam matNoticeParam, UserInfoToken userInfo) {
        MatNotice matNotice = matNoticeMapper.selectByPrimaryKey(matNoticeParam.getSid());
        if (StringUtils.equals(matNotice.getStatus(),ConstantsStatus.STATUS_1)){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("已提交数据，不允许修改"));
        }
        if (StringUtils.equals(matNotice.getStatus(),ConstantsStatus.STATUS_2)){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("已处理数据，不允许修改"));
        }
        matNoticeDtoMapper.updatePo(matNoticeParam, matNotice);

        if (StringUtils.equals(matNoticeParam.getModifyMark(), CommonEnum.modifyMarkEnum.MODIFY_MARK_3.getValue())) {
            boolean copGNo = chekCopGNo(matNotice);
            if (copGNo) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("备案料号已存在"));
            }
        }
        matNotice.setUpdateUser(userInfo.getUserNo());
        matNotice.setUpdateUserName(userInfo.getUserName());
        matNotice.setTradeCode(userInfo.getCompany());
        matNotice.setUpdateTime(new Date());
        // 更新数据
        int update = matNoticeMapper.updateByPrimaryKey(matNotice);
        return update > 0 ? matNoticeDtoMapper.toDto(matNotice) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
        matNoticeMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<MatNoticeDto> selectAll(MatNoticeParam exportParam, UserInfoToken userInfo) {
        MatNotice matNotice = matNoticeDtoMapper.toPo(exportParam);
        matNotice.setTradeCode(userInfo.getCompany());
        List<MatNoticeDto> matNoticeDtos = new ArrayList<>();
        List<MatNotice> matNotices = matNoticeMapper.getList(matNotice);
        if (CollectionUtils.isNotEmpty(matNotices)) {
            matNoticeDtos = matNotices.stream().map(head -> {
                MatNoticeDto dto = matNoticeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return matNoticeDtos;
    }

    /**
     * 状态更新
     * @param sids
     * @param status
     * @param userInfoToken
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(List<String> sids, String status, UserInfoToken userInfoToken) {
        ResultObject resultObject = ResultObject.createInstance(true, "");
        List<MatNotice> matNotices = matNoticeMapper.getdeletelist(sids);
        if (matNotices.size() > 0) {
            Integer sum = Math.toIntExact(matNotices.stream().filter(e -> !StringUtils.equals(e.getStatus(), ConstantsStatus.STATUS_0)).count());
            Integer status1 = Math.toIntExact(matNotices.stream().filter(e -> !StringUtils.equals(e.getStatus(), ConstantsStatus.STATUS_1)).count());
            Integer status2 = Math.toIntExact(matNotices.stream().filter(e -> StringUtils.equals(e.getStatus(), ConstantsStatus.STATUS_2)).count());

            //修改已提交数据
            if (StringUtils.equals(status, ConstantsStatus.STATUS_1)) {
                if (sum != 0) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("数据中已有已提交和已处理数据，不允许修改"));
                } else {
                    for (MatNotice matNotice : matNotices) {
                        matNotice.setUpdateUser(userInfoToken.getUserNo());
                        matNotice.setUpdateUserName(userInfoToken.getUserName());
                        matNotice.setUpdateTime(new Date());
                        matNotice.setStatus(ConstantsStatus.STATUS_1);
                        matNotice.setSendUser(userInfoToken.getUserNo());
                        matNotice.setSendUserName(userInfoToken.getUserName());
                        matNotice.setSendDate(new Date());
                        matNoticeMapper.updateByPrimaryKey(matNotice);
                    }
                }
                //修改已处理数据
            } else if (StringUtils.equals(status, ConstantsStatus.STATUS_2)) {
                if (status1 != 0 || status2 != 0) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("数据中有暂存和已处理状态数据，不允许修改"));
                } else {
                    for (MatNotice matNotice : matNotices) {
                        matNotice.setUpdateUser(userInfoToken.getUserNo());
                        matNotice.setUpdateUserName(userInfoToken.getUserName());
                        matNotice.setUpdateTime(new Date());
                        matNotice.setStatus(ConstantsStatus.STATUS_2);
                        matNotice.setApprUser(userInfoToken.getUserNo());
                        matNotice.setApprUserName(userInfoToken.getUserName());
                        matNotice.setApprDate(new Date());
                        matNoticeMapper.updateByPrimaryKey(matNotice);
                    }
                }
            }
        }
    }



}

