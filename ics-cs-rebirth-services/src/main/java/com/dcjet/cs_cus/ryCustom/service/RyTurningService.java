package com.dcjet.cs_cus.ryCustom.service;

import com.dcjet.cs.dto.mat.AttachedDto;
import com.dcjet.cs.dto_cus.ryCustom.RyTurningDto;
import com.dcjet.cs.dto_cus.ryCustom.RyTurningParam;
import com.dcjet.cs.dto_cus.ryCustom.RyTurningPrint;
import com.dcjet.cs.mat.service.AttachedService;
import com.dcjet.cs_cus.ryCustom.dao.RyTurningMapper;
import com.dcjet.cs_cus.ryCustom.mapper.RyTurningDtoMapper;
import com.dcjet.cs_cus.ryCustom.model.RyTurning;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-6-23
 */
@Service
public class RyTurningService extends BaseService<RyTurning> {
    @Resource
    private RyTurningMapper ryTurningMapper;
    @Resource
    private RyTurningDtoMapper ryTurningDtoMapper;
    @Resource
    private AttachedService attachedService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Override
    public Mapper<RyTurning> getMapper() {
        return ryTurningMapper;
    }

    /**
     * 获取分页信息
     *
     * @param ryTurningParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<RyTurningDto>> getListPaged(RyTurningParam ryTurningParam, PageParam pageParam) {
        // 启用分页查询
        RyTurning ryTurning = ryTurningDtoMapper.toPo(ryTurningParam);
        Page<RyTurning> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> ryTurningMapper.getList(ryTurning));
        List<RyTurningDto> ryTurningDtos = page.getResult().stream().map(head -> {
            RyTurningDto dto = ryTurningDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<RyTurningDto>> paged = ResultObject.createInstance(ryTurningDtos,
                (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param ryTurningParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RyTurningDto insert(RyTurningParam ryTurningParam, UserInfoToken userInfo) {
        RyTurning ryTurning = ryTurningDtoMapper.toPo(ryTurningParam);
        Integer maxSerialNo = ryTurningMapper.getMaxSerialNo(userInfo.getCompany());
        if (maxSerialNo == null) {
            ryTurning.setSerialNo(1);
        } else {
            ryTurning.setSerialNo(++maxSerialNo);
        }

        ryTurning.setTradeCode(userInfo.getCompany());
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        ryTurning.setSid(sid);
        ryTurning.setInsertUser(userInfo.getUserNo());
        ryTurning.setInsertTime(new Date());
        // 新增数据
        int insertStatus = ryTurningMapper.insert(ryTurning);
        return insertStatus > 0 ? ryTurningDtoMapper.toDto(ryTurning) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param ryTurningParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RyTurningDto update(RyTurningParam ryTurningParam, UserInfoToken userInfo) {
        RyTurning ryTurning = ryTurningMapper.selectByPrimaryKey(ryTurningParam.getSid());
        ryTurningDtoMapper.updatePo(ryTurningParam, ryTurning);
        ryTurning.setTradeCode(userInfo.getCompany());
        ryTurning.setUpdateUser(userInfo.getUserNo());
        ryTurning.setUpdateTime(new Date());
        // 更新数据
        int update = ryTurningMapper.updateByPrimaryKey(ryTurning);
        return update > 0 ? ryTurningDtoMapper.toDto(ryTurning) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param businessSids
     * @return
     */
    @Transient
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> businessSids, UserInfoToken userInfo) throws Exception {
        for (String businessSid : businessSids) {
            ResultObject<List<AttachedDto>> resultObject = attachedService.getAttechedList(businessSid, userInfo);
            List<AttachedDto> attachedDtos = resultObject.getData();
            if (CollectionUtils.isNotEmpty(attachedDtos)) {
                List<String> sids = attachedDtos.stream().map(e -> e.getSid()).collect(Collectors.toList());
                attachedService.delete(sids);
            }
        }
        ryTurningMapper.deleteBySids(businessSids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<RyTurningDto> selectAll(RyTurningParam exportParam, UserInfoToken userInfo) {
        RyTurning ryTurning = ryTurningDtoMapper.toPo(exportParam);
        ryTurning.setTradeCode(userInfo.getCompany());
        List<RyTurningDto> ryTurningDtos = new ArrayList<>();
        List<RyTurning> ryTurnings = ryTurningMapper.getList(ryTurning);
        if (CollectionUtils.isNotEmpty(ryTurnings)) {
            ryTurningDtos = ryTurnings.stream().map(head -> {
                RyTurningDto dto = ryTurningDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return ryTurningDtos;
    }

    public RyTurningPrint toPrintBean(RyTurning ryTurning) {
        RyTurningPrint ryTurningPrint = new RyTurningPrint();

        ryTurningPrint.setExgSerialNoStr(xdoi18n.XdoI18nUtil.t("成品序号 ") + ryTurning.getExgSerialNo());
        ryTurningPrint.setCopExgNameStr(xdoi18n.XdoI18nUtil.t("(品名：") + ryTurning.getCopExgName() + ")");
        ryTurningPrint.setImgSerialNoStr(xdoi18n.XdoI18nUtil.t("之第 ") + ryTurning.getImgSerialNo() + xdoi18n.XdoI18nUtil.t(" 项料件"));
        ryTurningPrint.setCopImgNameStr(xdoi18n.XdoI18nUtil.t("(品名：") + ryTurning.getCopImgName() + ")");
        ryTurningPrint.setCompositionDosage(xdoi18n.XdoI18nUtil.t("单耗：") + ryTurning.getCompositionDosage());
        ryTurningPrint.setWeighNetCon(xdoi18n.XdoI18nUtil.t("净耗（克/个）= ") + ryTurning.getWeighNetCon());
        StringBuilder comCal = new StringBuilder(xdoi18n.XdoI18nUtil.t("单耗（克/个）="));
        StringBuilder lossRateCal = new StringBuilder(xdoi18n.XdoI18nUtil.t("工艺损耗率 = （单耗-净耗）/ 单耗 ="));
        if (ryTurning.getEffLength() != null && ryTurning.getErpProductH() != null
                && ryTurning.getErpKnifeW() != null && !(ryTurning.getErpProductH().compareTo(BigDecimal.ZERO) == 0
                && BigDecimal.ZERO.compareTo(ryTurning.getErpKnifeW()) == 0)) {
            BigDecimal calFactor1 = ryTurning.getEffLength().divide(ryTurning.getErpProductH()
                    .add(ryTurning.getErpKnifeW()), 0, BigDecimal.ROUND_DOWN);
            comCal.append("(").append(ryTurning.getErpSingleW()).append(" / ").append(calFactor1)
                    .append("）= ").append(ryTurning.getCompositionDosage());
            ryTurningPrint.setProductAmount(ryTurning.getEffLength()
                    .divide(ryTurning.getErpProductH().add(ryTurning.getErpKnifeW()), 0, BigDecimal.ROUND_DOWN).toString());
        }
        if (ryTurning.getCompositionDosage() != null && ryTurning.getWeighNetCon() != null
                && BigDecimal.ZERO.compareTo(ryTurning.getCompositionDosage()) != 0) {
            lossRateCal.append("（").append(ryTurning.getCompositionDosage()).append("-").append(ryTurning.getWeighNetCon())
                    .append(") / ").append(ryTurning.getCompositionDosage()).append(" = ")
                    .append(ryTurning.getLossRate()).append("%");
        }
        if (ryTurning.getErpProductH() != null) {
            ryTurningPrint.setExgLength(ryTurning.getErpProductH().add(ryTurning.getErpKnifeW()));
        }

        ryTurningPrint.setEmsNo(ryTurning.getEmsNo());
        ryTurningPrint.setCompositionDosageCal(comCal.toString());
        ryTurningPrint.setLossRate(lossRateCal.toString());
        ryTurningPrint.setCopExgName(ryTurning.getCopExgName());
        ryTurningPrint.setDiameter(ryTurning.getDiameter());
        ryTurningPrint.setErpSingleW(ryTurning.getErpSingleW());
        ryTurningPrint.setEffLength(ryTurning.getEffLength());
        ryTurningPrint.setCopExgNo(ryTurning.getCopExgNo());
        ryTurningPrint.setFacGNo("B-" + ryTurning.getFacGNo());
        ryTurningPrint.setCopImgName(ryTurning.getCopImgName());
        return ryTurningPrint;
    }
}
