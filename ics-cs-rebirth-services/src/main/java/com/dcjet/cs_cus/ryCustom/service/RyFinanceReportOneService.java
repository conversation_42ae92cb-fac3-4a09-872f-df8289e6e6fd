package com.dcjet.cs_cus.ryCustom.service;
import com.dcjet.cs.dto_cus.ryCustom.RyFinanceReportOneDto;
import com.dcjet.cs.dto_cus.ryCustom.RyFinanceReportOneParam;
import com.dcjet.cs.erp.dao.sap.ErpSapExgIeListMapper;
import com.dcjet.cs_cus.ryCustom.mapper.RyFinanceReportOneDtoMapper;
import com.dcjet.cs_cus.ryCustom.model.RyFinanceReportOne;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-3-13
 */
@Service
public class RyFinanceReportOneService extends BaseService<RyFinanceReportOne> {
    @Resource
    private RyFinanceReportOneDtoMapper ryFinanceReportOneDtoMapper;
    @Resource
    private ErpSapExgIeListMapper erpExgIeListMapper;
    @Override
    public Mapper<RyFinanceReportOne> getMapper() {
        return null;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param ryFinanceReportOneParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<RyFinanceReportOneDto>> getListPaged(RyFinanceReportOneParam ryFinanceReportOneParam, PageParam pageParam) {
        // 启用分页查询
        RyFinanceReportOne ryFinanceReportOne = ryFinanceReportOneDtoMapper.toPo(ryFinanceReportOneParam);
        Page<RyFinanceReportOne> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> erpExgIeListMapper.getListByTradeCode(ryFinanceReportOne));
        List<RyFinanceReportOneDto> ryFinanceReportOneDtos = page.getResult().stream().map(head -> {
            RyFinanceReportOneDto dto = ryFinanceReportOneDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<RyFinanceReportOneDto>> paged = ResultObject.createInstance(ryFinanceReportOneDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<RyFinanceReportOneDto> selectAll(RyFinanceReportOneParam exportParam, UserInfoToken userInfo) {
        RyFinanceReportOne ryFinanceReportOne = ryFinanceReportOneDtoMapper.toPo(exportParam);
        ryFinanceReportOne.setTradeCode(userInfo.getCompany());
        List<RyFinanceReportOneDto> ryFinanceReportOneDtos = new ArrayList<>();
        List<RyFinanceReportOne> ryFinanceReportOnes = erpExgIeListMapper.getListByTradeCode(ryFinanceReportOne);
        if (CollectionUtils.isNotEmpty(ryFinanceReportOnes)) {
            ryFinanceReportOneDtos = ryFinanceReportOnes.stream().map(head -> {
                RyFinanceReportOneDto dto = ryFinanceReportOneDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return ryFinanceReportOneDtos;
    }
}
