package com.dcjet.cs_cus.ryCustom.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-6-29
 */
@Setter
@Getter
@Table(name = "T_RY_STAMPING")
public class RyStamping extends RyUnitConBaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 密度
     */
    @Column(name = "DENSITY")
    private BigDecimal density;
    /**
     * 料宽
     */
    @Column(name = "WIDTH")
    private BigDecimal width;
    /**
     * 料距
     */
    @Column(name = "DISTANCE")
    private BigDecimal distance;
    /**
     * 模穴数
     */
    @Column(name = "TOUCH_POINT_NUM")
    private String touchPointNum;
    /**
     * 模具图耗用
     */
    @Column(name = "MOULD_EXPEND")
    private BigDecimal mouldExpend;
    /**
     * erp净耗
     */
    @Column(name = "NET_CONSUMPTION")
    private BigDecimal netConsumption;
    /**
     * 实际称重
     */
    @Column(name = "ACTUAL_WEIGH")
    private BigDecimal actualWeigh;

    /**
     * 料厚
     */
    @Column(name = "THICK_NESS")
    private BigDecimal thickNess;
}
