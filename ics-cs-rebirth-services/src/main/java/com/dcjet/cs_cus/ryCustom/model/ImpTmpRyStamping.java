package com.dcjet.cs_cus.ryCustom.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Table(name = "T_IMP_TMP_RY_STAMPING")
public class ImpTmpRyStamping extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * sid
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 序号
     */
    @Column(name = "SERIAL_NO")
    private Integer serialNo;
    /**
     * 荣益品号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 成品品名
     */
    @Column(name = "FAC_G_NAME")
    private String facGName;
    /**
     * 版本号
     */
    @Column(name = "VERSION")
    private String version;
    /**
     * 原始品号
     */
    @Column(name = "ORIGINAL_NO")
    private String originalNo;
    /**
     * 原料品名
     */
    @Column(name = "ORIGINAL_NAME")
    private String originalName;
    /**
     * 密度
     */
    @Column(name = "DENSITY")
    private BigDecimal density;
    /**
     * 料宽
     */
    @Column(name = "WIDTH")
    private BigDecimal width;
    /**
     * 料距
     */
    @Column(name = "DISTANCE")
    private BigDecimal distance;
    /**
     * 模穴数
     */
    @Column(name = "TOUCH_POINT_NUM")
    private String touchPointNum;
    /**
     * 模具图耗用
     */
    @Column(name = "MOULD_EXPEND")
    private BigDecimal mouldExpend;
    /**
     * erp净耗
     */
    @Column(name = "NET_CONSUMPTION")
    private BigDecimal netConsumption;
    /**
     * erp损耗量
     */
    @Column(name = "LOSS_AMOUNT")
    private BigDecimal lossAmount;
    /**
     * erp损耗率
     */
    @Column(name = "LOSS_RATE")
    private BigDecimal lossRate;
    /**
     * 实际称重
     */
    @Column(name = "ACTUAL_WEIGH")
    private BigDecimal actualWeigh;
    /**
     * 料厚
     */
    @Column(name = "THICK_NESS")
    private BigDecimal thickNess;
    /**
     * 导入批次号
     */
    @Column(name = "TEMP_OWNER")
    private String tempOwner;
    /**
     * 导入标记
     */
    @Column(name = "TEMP_FLAG")
    private Integer tempFlag;
    /**
     * 导入错误信息
     */
    @Column(name = "TEMP_REMARK")
    private String tempRemark;

    public RyStamping toRyStamping() {
        RyStamping ryStamping = new RyStamping();
        ryStamping.setTradeCode(this.tempRemark);
        ryStamping.setFacGNo(this.facGNo);
        ryStamping.setFacGName(this.facGName);
        ryStamping.setVersion(this.version);
        ryStamping.setOriginalNo(this.originalNo);
        ryStamping.setOriginalName(this.originalName);
        ryStamping.setDensity(this.density);
        ryStamping.setWidth(this.width);
        ryStamping.setDistance(this.distance);
        ryStamping.setTouchPointNum(this.touchPointNum);
        ryStamping.setMouldExpend(this.mouldExpend);
        ryStamping.setNetConsumption(this.netConsumption);
        ryStamping.setActualWeigh(this.actualWeigh);
        ryStamping.setThickNess(this.thickNess);
        ryStamping.setLossAmount(this.lossAmount);
        ryStamping.setLossRate(this.lossRate);
        ryStamping.setSerialNo(this.serialNo);
        ryStamping.setInsertUser(this.getInsertUser());
        ryStamping.setInsertUserName(this.getInsertUserName());
        return ryStamping;
    }
}
