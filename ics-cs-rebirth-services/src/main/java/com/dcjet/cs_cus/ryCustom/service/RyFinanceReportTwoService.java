package com.dcjet.cs_cus.ryCustom.service;
import com.dcjet.cs.dec.dao.DecECustomsTrackMapper;
import com.dcjet.cs.dto_cus.ryCustom.RyFinanceReportTwoDto;
import com.dcjet.cs.dto_cus.ryCustom.RyFinanceReportTwoParam;
import com.dcjet.cs_cus.ryCustom.mapper.RyFinanceReportTwoDtoMapper;
import com.dcjet.cs_cus.ryCustom.model.RyFinanceReportTwo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-3-13
 */
@Service
public class RyFinanceReportTwoService extends BaseService<RyFinanceReportTwo> {
    @Resource
    private RyFinanceReportTwoDtoMapper ryFinanceReportTwoDtoMapper;
    @Resource
    private DecECustomsTrackMapper decECustomsTrackMapper;
    @Override
    public Mapper<RyFinanceReportTwo> getMapper() {
        return null;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param ryFinanceReportTwoParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<RyFinanceReportTwoDto>> getListPaged(RyFinanceReportTwoParam ryFinanceReportTwoParam, PageParam pageParam) {
       // 启用分页查询
        RyFinanceReportTwo ryFinanceReportTwo = ryFinanceReportTwoDtoMapper.toPo(ryFinanceReportTwoParam);
        Page<RyFinanceReportTwo> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decECustomsTrackMapper.getListByTradeCode(ryFinanceReportTwo));
        List<RyFinanceReportTwoDto> ryFinanceReportTwoDtos = page.getResult().stream().map(head -> {
            RyFinanceReportTwoDto dto = ryFinanceReportTwoDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<RyFinanceReportTwoDto>> paged = ResultObject.createInstance(ryFinanceReportTwoDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<RyFinanceReportTwoDto> selectAll(RyFinanceReportTwoParam exportParam, UserInfoToken userInfo) {
        RyFinanceReportTwo ryFinanceReportTwo = ryFinanceReportTwoDtoMapper.toPo(exportParam);
        ryFinanceReportTwo.setTradeCode(userInfo.getCompany());
        List<RyFinanceReportTwoDto> ryFinanceReportTwoDtos = new ArrayList<>();
        List<RyFinanceReportTwo> ryFinanceReportTwos = decECustomsTrackMapper.getListByTradeCode(ryFinanceReportTwo);
        if (CollectionUtils.isNotEmpty(ryFinanceReportTwos)) {
            ryFinanceReportTwoDtos = ryFinanceReportTwos.stream().map(head -> {
                RyFinanceReportTwoDto dto = ryFinanceReportTwoDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return ryFinanceReportTwoDtos;
    }
}
