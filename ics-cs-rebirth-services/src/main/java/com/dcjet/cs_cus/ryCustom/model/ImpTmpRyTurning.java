package com.dcjet.cs_cus.ryCustom.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Table(name = "T_IMP_TMP_RY_TURNING")
public class ImpTmpRyTurning extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户
     */
    @Column(name = "TEMP_OWNER")
    private String tempOwner;
    /**
     * 标识
     */
    @Column(name = "TEMP_FLAG")
    private int tempFlag;
    /**
     * 提示
     */
    @Column(name = "TEMP_REMARK")
    private String tempRemark;
    /**
     * 荣益品号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 成品品名
     */
    @Column(name = "FAC_G_NAME")
    private String facGName;
    /**
     * 版本号
     */
    @Column(name = "VERSION")
    private String version;
    /**
     * 原始品号
     */
    @Column(name = "ORIGINAL_NO")
    private String originalNo;
    /**
     * 原料品名
     */
    @Column(name = "ORIGINAL_NAME")
    private String originalName;
    /**
     * 产品高度
     */
    @Column(name = "ERP_PRODUCT_H")
    private BigDecimal erpProductH;
    /**
     * erp刀宽
     */
    @Column(name = "ERP_KNIFE_W")
    private BigDecimal erpKnifeW;
    /**
     * erp单支重量
     */
    @Column(name = "ERP_SINGLE_W")
    private BigDecimal erpSingleW;
    /**
     * 有效长度
     */
    @Column(name = "EFF_LENGTH")
    private BigDecimal effLength;
    /**
     * 称重净耗
     */
    @Column(name = "WEIGH_NET_CON")
    private BigDecimal weighNetCon;
    /**
     * 组成用量
     */
    @Column(name = "COMPOSITION_DOSAGE")
    private BigDecimal compositionDosage;
    /**
     * 直径
     */
    @Column(name = "DIAMETER")
    private BigDecimal diameter;
    /**
     * 系统净耗
     */
    @Column(name = "SYSTEM_NET_CON")
    private BigDecimal systemNetCon;

    /**
     * 损耗量
     */
    @Column(name = "LOSS_AMOUNT")
    private BigDecimal lossAmount;
    /**
     * 损耗率
     */
    @Column(name = "LOSS_RATE")
    private BigDecimal lossRate;
    /**
     * 序号
     */
    @Column(name = "SERIAL_NO")
    private int serialNo;

    public RyTurning toRyTurning() {
        RyTurning ryTurning = new RyTurning();
        ryTurning.setFacGNo(this.facGNo);
        ryTurning.setFacGName(this.facGName);
        ryTurning.setVersion(this.version);
        ryTurning.setOriginalNo(this.originalNo);
        ryTurning.setOriginalName(this.originalName);
        ryTurning.setErpProductH(this.erpProductH);
        ryTurning.setErpKnifeW(this.erpKnifeW);
        ryTurning.setErpSingleW(this.erpSingleW);
        ryTurning.setEffLength(this.effLength);
        ryTurning.setWeighNetCon(this.weighNetCon);
        ryTurning.setCompositionDosage(this.compositionDosage);
        ryTurning.setDiameter(this.diameter);
        ryTurning.setSystemNetCon(this.systemNetCon);
        ryTurning.setLossAmount(this.lossAmount);
        ryTurning.setLossRate(this.lossRate);
        ryTurning.setSerialNo(this.serialNo);
        ryTurning.setInsertUserName(this.getInsertUserName());
        ryTurning.setInsertUser(this.getInsertUser());
        return ryTurning;
    }
}
