package com.dcjet.cs_cus.ryCustom.dao;

import com.dcjet.cs_cus.ryCustom.model.ImpTmpRyTurning;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate dcits
 * 荣益车削类导入
 *
 * @author: 何恩彧
 * @date: 2020-06-30
 */
public interface ImpTmpRyTurningMapper extends Mapper<ImpTmpRyTurning> {

    /**
     * 根据传入的数据类型获取相应数据
     *
     * @param param
     * @return 返回结果
     */
    List<ImpTmpRyTurning> selectByFlag(Map<String, Object> param);

    /**
     * 通过导入批次号删除临时表数据
     */
    int deleteByTempOwner(@Param("tempOwner") String tempOwner);
}
