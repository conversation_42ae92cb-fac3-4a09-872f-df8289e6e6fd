package com.dcjet.cs_cus.ryCustom.dao;

import com.dcjet.cs_cus.ryCustom.model.RyTurning;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * RyTurning
 *
 * <AUTHOR>
 * @date: 2020-6-23
 */
public interface RyTurningMapper extends Mapper<RyTurning> {
    /**
     * 查询获取数据
     *
     * @param ryTurning
     * @return
     */
    List<RyTurning> getList(RyTurning ryTurning);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据sids获取数据
     */
    List<RyTurning> selectBySids(@Param("list") List<String> sids);

    /**
     *
     *
     * @param tradeCode
     * @return
     */
    @Select({"<script>select ",
            "FAC_G_NO || '||' || VERSION as BUSINESS_KEY",
            "from T_RY_TURNING ",
            "where trade_code = #{tradeCode}",
            "</script>"})
   List<String> getRyTurning(@Param("tradeCode") String tradeCode);

    /**
     * 获取最大序号
     */
    @Select({"select max(SERIAL_NO) " +
            "from T_RY_TURNING " +
            "where trade_code = #{tradeCode}"
    })
    Integer getMaxSerialNo(@Param("tradeCode") String tradeCode);

}
