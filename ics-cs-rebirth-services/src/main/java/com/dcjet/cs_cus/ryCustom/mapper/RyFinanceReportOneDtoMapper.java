package com.dcjet.cs_cus.ryCustom.mapper;
import com.dcjet.cs.dto_cus.ryCustom.RyFinanceReportOneDto;
import com.dcjet.cs.dto_cus.ryCustom.RyFinanceReportOneParam;
import com.dcjet.cs.dto_cus.ryCustom.RyFinanceReportTwoParam;
import com.dcjet.cs_cus.ryCustom.model.RyFinanceReportOne;
import com.dcjet.cs_cus.ryCustom.model.RyFinanceReportTwo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-3-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RyFinanceReportOneDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    RyFinanceReportOneDto toDto(RyFinanceReportOne po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    RyFinanceReportOne toPo(RyFinanceReportOneParam param);
    /**
     * 数据库原始数据更新
     * @param ryFinanceReportTwoParam
     * @param ryFinanceReportTwo
     */
    void updatePo(RyFinanceReportTwoParam ryFinanceReportTwoParam, @MappingTarget RyFinanceReportTwo ryFinanceReportTwo);
    default void patchPo(RyFinanceReportTwoParam ryFinanceReportTwoParam, RyFinanceReportTwo ryFinanceReportTwo) {
        // TODO 自行实现局部更新
    }
}
