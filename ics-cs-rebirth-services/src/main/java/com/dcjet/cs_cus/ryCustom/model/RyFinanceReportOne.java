package com.dcjet.cs_cus.ryCustom.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-3-13
 */
@Setter
@Getter
@Table(name = "T_ERP_EXG_IE_LIST")
public class RyFinanceReportOne implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 关联编号
	 */
	@Column(name = "LINKED_NO")
	private String linkedNo;
	/**
	 * 发票号码
	 */
	@Column(name = "INVOICE_NO")
	private  String invoiceNo;
	/**
	 * 发票日期
	 */
	@Column(name = "INVOICE_DATE")
	private Date invoiceDate;
	/**
	 * 客户料号
	 */
	@Column(name = "CUSTOMER_G_NO")
	private  String customerGNo;
	/**
	 * 商品编码
	 */
	@Column(name = "CODE_T_S")
	private  String codeTS;
	/**
	 * 申报总价
	 */
	@Column(name = "DEC_TOTAL")
	private  String decTotal;
	/**
	 * 币制
	 */
	@Column(name = "CURR")
	private  String curr;
	/**
	 * 企业代码
	 */
	@Column(name = "TRADE_CODE")
	private String tradeCode;

	/**
	 * 发票日期-开始
	 */
	@Transient
	private String invoiceDateFrom;
	/**
	 * 发票日期-结束
	 */
	@Transient
	private String invoiceDateTo;

	private List<String> invoiceNos;
}
