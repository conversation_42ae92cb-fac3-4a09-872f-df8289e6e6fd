package com.dcjet.cs_cus.ryCustom.mapper;

import com.dcjet.cs.dto_cus.ryCustom.RyTurningDto;
import com.dcjet.cs.dto_cus.ryCustom.RyTurningParam;
import com.dcjet.cs_cus.ryCustom.model.RyTurning;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-6-23
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RyTurningDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    RyTurningDto toDto(RyTurning po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    RyTurning toPo(RyTurningParam param);

    /**
     * 数据库原始数据更新
     *
     * @param ryTurningParam
     * @param ryTurning
     */
    void updatePo(RyTurningParam ryTurningParam, @MappingTarget RyTurning ryTurning);

    default void patchPo(RyTurningParam ryTurningParam, RyTurning ryTurning) {
        // TODO 自行实现局部更新
    }
}
