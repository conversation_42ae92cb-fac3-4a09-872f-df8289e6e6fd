package com.dcjet.cs_cus.ryCustom.dao;

import com.dcjet.cs_cus.ryCustom.model.RyStamping;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * RyStamping
 *
 * <AUTHOR>
 * @date: 2020-6-29
 */
public interface RyStampingMapper extends Mapper<RyStamping> {
    /**
     * 查询获取数据
     *
     * @param ryStamping
     * @return
     */
    List<RyStamping> getList(RyStamping ryStamping);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据sids获取数据
     */
    List<RyStamping> selectBySids(@Param("list") List<String> sids);

    /**
     * @param tradeCode
     * @return
     */
    @Select({"<script>select ",
            "FAC_G_NO || '||' || VERSION as BUSINESS_KEY",
            "from T_RY_STAMPING ",
            "where trade_code = #{tradeCode}",
            "</script>"})
    List<String> getRyStamping(@Param("tradeCode") String tradeCode);

    /**
     * 获取最大序号
     */
    @Select({"select max(SERIAL_NO) " +
            "from T_RY_STAMPING " +
            "where trade_code = #{tradeCode}"
    })
    Integer getMaxSerialNo(@Param("tradeCode") String tradeCode);
}
