package com.dcjet.cs_cus.ryCustom.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-6-23
 */
@Setter
@Getter
@Table(name = "T_RY_TURNING")
public class RyTurning extends RyUnitConBaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * erp产品高度
     */
    @Column(name = "ERP_PRODUCT_H")
    private BigDecimal erpProductH;
    /**
     * erp刀宽
     */
    @Column(name = "ERP_KNIFE_W")
    private BigDecimal erpKnifeW;
    /**
     * erp单支重量
     */
    @Column(name = "ERP_SINGLE_W")
    private BigDecimal erpSingleW;
    /**
     * 有效长度
     */
    @Column(name = "EFF_LENGTH")
    private BigDecimal effLength;
    /**
     * 称重净耗
     */
    @Column(name = "WEIGH_NET_CON")
    private BigDecimal weighNetCon;
    /**
     * 组成用量
     */
    @Column(name = "COMPOSITION_DOSAGE")
    private BigDecimal compositionDosage;
    /**
     * 直径
     */
    @Column(name = "DIAMETER")
    private BigDecimal diameter;
    /**
     * 系统净耗
     */
    @Column(name = "SYSTEM_NET_CON")
    private BigDecimal systemNetCon;
}
