package com.dcjet.cs_cus.ryCustom.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class RyUnitConBaseModel {
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 序号
     */
    @Column(name = "SERIAL_NO")
    private Integer serialNo;
    /**
     * 荣益品号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 成品品名
     */
    @Column(name = "FAC_G_NAME")
    private String facGName;

    /**
     * 版本号
     */
    @Column(name = "VERSION")
    private String version;
    /**
     * 原料品号
     */
    @Column(name = "ORIGINAL_NO")
    private String originalNo;
    /**
     * 原料品名
     */
    @Column(name = "ORIGINAL_NAME")
    private String originalName;
    /**
     * 状态（0 停用 1 启用）
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 手册号
     */
    @Column(name = "EMS_NO")
    private String emsNo;
    /**
     * 备案料件料号
     */
    @Column(name = "COP_IMG_NO")
    private String copImgNo;
    /**
     * 备案料件序号
     */
    @Column(name = "IMG_SERIAL_NO")
    private Integer imgSerialNo;
    /**
     * 备案料件申报单位
     */
    @Column(name = "COP_IMG_UNIT")
    private String copImgUnit;
    /**
     * 备案料件品名
     */
    @Column(name = "COP_IMG_NAME")
    private String copImgName;
    /**
     * 备案成品料号
     */
    @Column(name = "COP_EXG_NO")
    private String copExgNo;
    /**
     * 备案成品序号
     */
    @Column(name = "EXG_SERIAL_NO")
    private Integer exgSerialNo;
    /**
     * 备案成品申报单位
     */
    @Column(name = "COP_EXG_UNIT")
    private String copExgUnit;
    /**
     * 备案成品名称
     */
    @Column(name = "COP_EXG_NAME")
    private String copExgName;
    /**
     * 备注1
     */
    @Column(name = "REMARK1")
    private String remark1;
    /**
     * 备注2
     */
    @Column(name = "REMARK2")
    private String remark2;
    /**
     * 备注3
     */
    @Column(name = "REMARK3")
    private String remark3;

    /**
     * 损耗量
     */
    @Column(name = "LOSS_AMOUNT")
    private BigDecimal lossAmount;
    /**
     * 损耗率
     */
    @Column(name = "LOSS_RATE")
    private BigDecimal lossRate;
    /**
     * 创建人用户名
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 更新人用户名
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;

}
