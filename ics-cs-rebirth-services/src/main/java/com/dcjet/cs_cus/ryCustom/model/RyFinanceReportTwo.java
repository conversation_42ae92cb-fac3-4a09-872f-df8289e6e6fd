package com.dcjet.cs_cus.ryCustom.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-3-13
 */
@Setter
@Getter
@Table(name = "T_ERP_EXG_IE_LIST")
public class RyFinanceReportTwo implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 报关单号
	 */
	@Column(name = "ENTRY_NO")
	private String entryNo;
	/**
	 * 监管方式
	 */
	@Column(name = "TRADE_MODE")
	private  String tradeMode;
	/**
	 * 关联编号
	 */
	@Column(name = "LINKED_NO")
	private  String linkedNo;
	/**
	 * 发票号码
	 */
	@Column(name = "INVOICE_NO")
	private  String invoiceNo;
	/**
	 * 制单日期
	 */
	@Column(name = "INSERT_TIME")
	private Date insertTime;
	/**
	 * 发票日期
	 */
	@Column(name = "INVOICE_DATE")
	private Date invoiceDate;
	/**
	 * 申报总价
	 */
	@Column(name = "DEC_TOTAL")
	private BigDecimal decTotal;
	/**
	 * 币制
	 */
	@Column(name = "CURR")
	private String curr;
	/**
	 * 企业代码
	 */
	@Column(name = "TRADE_CODE")
	private String tradeCode;
	/**
	 * 买方地址
	 */
	@Column(name = "BUY_ADDRESS")
	private String buyAddress;
	/**
	 * 客户名称
	 */
	@Column(name = "CUSTOMER_NAME")
	private String customerName;

	/**
	 * 制单日期-开始
	 */
	@Transient
	private String insertTimeFrom;
	/**
	 * 制单日期-结束
	 */
	@Transient
	private String insertTimeTo;

	private List<String> entryNos;
}
