package com.dcjet.cs_cus.ryCustom.mapper;

import com.dcjet.cs.dto_cus.ryCustom.RyStampingDto;
import com.dcjet.cs.dto_cus.ryCustom.RyStampingParam;
import com.dcjet.cs_cus.ryCustom.model.RyStamping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-6-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RyStampingDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    RyStampingDto toDto(RyStamping po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    RyStamping toPo(RyStampingParam param);

    /**
     * 数据库原始数据更新
     *
     * @param ryStampingParam
     * @param ryStamping
     */
    void updatePo(RyStampingParam ryStampingParam, @MappingTarget RyStamping ryStamping);

    default void patchPo(RyStampingParam ryStampingParam, RyStamping ryStamping) {
        // TODO 自行实现局部更新
    }
}
