package com.dcjet.cs_cus.sj.dao;

import com.dcjet.cs_cus.sj.model.GwstdGzResale;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwstdGzResale
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
public interface GwstdGzResaleMapper extends Mapper<GwstdGzResale> {
    /**
     * 查询获取数据
     *
     * @param gwstdGzResale
     * @return
     */
    List<GwstdGzResale> getList(GwstdGzResale gwstdGzResale);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
