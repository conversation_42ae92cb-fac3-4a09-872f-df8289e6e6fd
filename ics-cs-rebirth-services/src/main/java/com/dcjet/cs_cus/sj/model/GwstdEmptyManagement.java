package com.dcjet.cs_cus.sj.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Setter
@Getter
@Table(name = "t_gwstd_empty_management")
public class GwstdEmptyManagement extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 还空日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "vacant_date")
    private Date vacantDate;
    /**
     * 企业料件料号
     */
    @Column(name = "fac_g_no")
    private String facGNo;
    /**
     * 汇总数量
     */
    @Column(name = "qty")
    private BigDecimal qty;
    /**
     * 计量单位
     */
    @Column(name = "unit")
    private String unit;
    /**
     * 备注
     */
    @Column(name = "note")
    private String note;
    /**
     * 商品名称
     */
    @Column(name = "g_name")
    private String gName;
    /**
     * 还空日期-开始
     */
    @Transient
    private String vacantDateFrom;
    /**
     * 还空日期-结束
     */
    @Transient
    private String vacantDateTo;
}
