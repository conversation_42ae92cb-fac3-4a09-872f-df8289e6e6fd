package com.dcjet.cs_cus.sj.service;

import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto_cus.sj.GwstdPackagingStatisticsDto;
import com.dcjet.cs.dto_cus.sj.GwstdPackagingStatisticsParam;
import com.dcjet.cs_cus.sj.dao.GwstdPackagingStatisticsMapper;
import com.dcjet.cs_cus.sj.mapper.GwstdPackagingStatisticsDtoMapper;
import com.dcjet.cs_cus.sj.model.GwstdPackagingStatistics;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 出口报关单-表头异步导出
 * @author: WJ
 * @createDate: 2020/9/14 13:39
 */
@Component
public class ExportPackagingStatisticsService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private GwstdPackagingStatisticsDtoMapper gwstdPackagingStatisticsDtoMapper;

    @Resource
    private GwstdPackagingStatisticsMapper gwstdPackagingStatisticsMapper;
    @Resource
    private GwstdPackagingStatisticsService gwstdPackagingStatisticsService;

    @Resource
    private CommonService commonService;

    private String taskName;

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("PACKAGING_STATISTICS");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        GwstdPackagingStatisticsParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        GwstdPackagingStatistics decEEntryHead = gwstdPackagingStatisticsDtoMapper.toPo(exportParam);
        taskName = xdoi18n.XdoI18nUtil.t("出口保税报关单-表头异步导出(E_ENTRY_HEAD_0)");
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(decEEntryHead));
        Integer count = gwstdPackagingStatisticsMapper.selectDataCount(decEEntryHead);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        GwstdPackagingStatisticsParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        GwstdPackagingStatistics gwstdPackagingStatistics = gwstdPackagingStatisticsDtoMapper.toPo(exportParam);

        //报关单申报日期-开始日期减一天
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(gwstdPackagingStatistics));
        Page<GwstdPackagingStatistics> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdPackagingStatisticsMapper.getList(gwstdPackagingStatistics));

        List<GwstdPackagingStatisticsDto> gwstdPackagingStatisticsDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            gwstdPackagingStatisticsDtos = page.getResult().stream().map(head -> {
                GwstdPackagingStatisticsDto dto = gwstdPackagingStatisticsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrintHead(gwstdPackagingStatisticsDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<GwstdPackagingStatisticsDto> convertForPrintHead(List<GwstdPackagingStatisticsDto> list) {
        for (GwstdPackagingStatisticsDto item : list) {

        }
        return list;
    }

    private GwstdPackagingStatisticsParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        GwstdPackagingStatisticsParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, GwstdPackagingStatisticsParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
