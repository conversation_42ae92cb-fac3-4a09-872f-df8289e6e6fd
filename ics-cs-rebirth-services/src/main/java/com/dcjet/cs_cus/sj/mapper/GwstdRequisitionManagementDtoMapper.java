package com.dcjet.cs_cus.sj.mapper;

import com.dcjet.cs.dto_cus.sj.GwstdRequisitionManagementDto;
import com.dcjet.cs.dto_cus.sj.GwstdRequisitionManagementParam;
import com.dcjet.cs_cus.sj.model.GwstdRequisitionManagement;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwstdRequisitionManagementDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwstdRequisitionManagementDto toDto(GwstdRequisitionManagement po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwstdRequisitionManagement toPo(GwstdRequisitionManagementParam param);

    /**
     * 数据库原始数据更新
     *
     * @param gwstdRequisitionManagementParam
     * @param gwstdRequisitionManagement
     */
    void updatePo(GwstdRequisitionManagementParam gwstdRequisitionManagementParam, @MappingTarget GwstdRequisitionManagement gwstdRequisitionManagement);

    default void patchPo(GwstdRequisitionManagementParam gwstdRequisitionManagementParam, GwstdRequisitionManagement gwstdRequisitionManagement) {
        // TODO 自行实现局部更新
    }
}
