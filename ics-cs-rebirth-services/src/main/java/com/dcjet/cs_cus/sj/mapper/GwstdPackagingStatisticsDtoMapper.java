package com.dcjet.cs_cus.sj.mapper;

import com.dcjet.cs.dto_cus.sj.GwstdPackagingStatisticsDto;
import com.dcjet.cs.dto_cus.sj.GwstdPackagingStatisticsParam;
import com.dcjet.cs_cus.sj.model.GwstdPackagingStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwstdPackagingStatisticsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwstdPackagingStatisticsDto toDto(GwstdPackagingStatistics po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwstdPackagingStatistics toPo(GwstdPackagingStatisticsParam param);

    /**
     * 数据库原始数据更新
     *
     * @param gwstdPackagingStatisticsParam
     * @param gwstdPackagingStatistics
     */
    void updatePo(GwstdPackagingStatisticsParam gwstdPackagingStatisticsParam, @MappingTarget GwstdPackagingStatistics gwstdPackagingStatistics);

    default void patchPo(GwstdPackagingStatisticsParam gwstdPackagingStatisticsParam, GwstdPackagingStatistics gwstdPackagingStatistics) {
        // TODO 自行实现局部更新
    }
}
