package com.dcjet.cs_cus.sj.service;

import com.dcjet.cs.dto_cus.sj.GwstdGzResaleDto;
import com.dcjet.cs.dto_cus.sj.GwstdGzResaleParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.sj.dao.GwstdGzResaleMapper;
import com.dcjet.cs_cus.sj.mapper.GwstdGzResaleDtoMapper;
import com.dcjet.cs_cus.sj.model.GwstdGzResale;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Service
public class GwstdGzResaleService extends BaseService<GwstdGzResale> {
    @Resource
    private GwstdGzResaleMapper gwstdGzResaleMapper;
    @Resource
    private GwstdGzResaleDtoMapper gwstdGzResaleDtoMapper;

    @Override
    public Mapper<GwstdGzResale> getMapper() {
        return gwstdGzResaleMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdGzResaleParam
     * @param pageParam
     * @param userInfo
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdGzResaleDto>> getListPaged(GwstdGzResaleParam gwstdGzResaleParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        GwstdGzResale gwstdGzResale = gwstdGzResaleDtoMapper.toPo(gwstdGzResaleParam);
        gwstdGzResale.setTradeCode(userInfo.getCompany());
        Page<GwstdGzResale> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdGzResaleMapper.getList(gwstdGzResale));
        List<GwstdGzResaleDto> gwstdGzResaleDtos = page.getResult().stream().map(head -> {
            GwstdGzResaleDto dto = gwstdGzResaleDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdGzResaleDto>> paged = ResultObject.createInstance(gwstdGzResaleDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwstdGzResaleParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdGzResaleDto insert(GwstdGzResaleParam gwstdGzResaleParam, UserInfoToken userInfo) {
        GwstdGzResale gwstdGzResale = gwstdGzResaleDtoMapper.toPo(gwstdGzResaleParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwstdGzResale.setSid(sid);
        gwstdGzResale.setInsertUser(userInfo.getUserNo());
        gwstdGzResale.setInsertUserName(userInfo.getUserName());
        gwstdGzResale.setInsertTime(new Date());
        gwstdGzResale.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = gwstdGzResaleMapper.insert(gwstdGzResale);
        return insertStatus > 0 ? gwstdGzResaleDtoMapper.toDto(gwstdGzResale) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwstdGzResaleParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdGzResaleDto update(GwstdGzResaleParam gwstdGzResaleParam, UserInfoToken userInfo) {
        GwstdGzResale gwstdGzResale = gwstdGzResaleMapper.selectByPrimaryKey(gwstdGzResaleParam.getSid());
        gwstdGzResaleDtoMapper.updatePo(gwstdGzResaleParam, gwstdGzResale);
        gwstdGzResale.setUpdateUser(userInfo.getUserNo());
        gwstdGzResale.setUpdateUserName(userInfo.getUserName());
        gwstdGzResale.setUpdateTime(new Date());
        // 更新数据
        int update = gwstdGzResaleMapper.updateByPrimaryKey(gwstdGzResale);
        return update > 0 ? gwstdGzResaleDtoMapper.toDto(gwstdGzResale) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwstdGzResaleMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdGzResaleDto> selectAll(GwstdGzResaleParam exportParam, UserInfoToken userInfo) {
        GwstdGzResale gwstdGzResale = gwstdGzResaleDtoMapper.toPo(exportParam);
        // gwstdGzResale.setTradeCode(userInfo.getCompany());
        List<GwstdGzResaleDto> gwstdGzResaleDtos = new ArrayList<>();
        List<GwstdGzResale> gwstdGzResales = gwstdGzResaleMapper.getList(gwstdGzResale);
        if (CollectionUtils.isNotEmpty(gwstdGzResales)) {
            gwstdGzResaleDtos = gwstdGzResales.stream().map(head -> {
                GwstdGzResaleDto dto = gwstdGzResaleDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdGzResaleDtos;
    }
}
