package com.dcjet.cs_cus.sj.dao;

import com.dcjet.cs_cus.sj.model.GwstdEmptyManagement;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwstdEmptyManagement
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
public interface GwstdEmptyManagementMapper extends Mapper<GwstdEmptyManagement> {
    /**
     * 查询获取数据
     *
     * @param gwstdEmptyManagement
     * @return
     */
    List<GwstdEmptyManagement> getList(GwstdEmptyManagement gwstdEmptyManagement);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
