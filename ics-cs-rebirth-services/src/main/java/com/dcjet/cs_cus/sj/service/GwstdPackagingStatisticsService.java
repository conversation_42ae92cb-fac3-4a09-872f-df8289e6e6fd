package com.dcjet.cs_cus.sj.service;

import com.dcjet.cs.dto_cus.sj.GwstdPackagingStatisticsDto;
import com.dcjet.cs.dto_cus.sj.GwstdPackagingStatisticsParam;
import com.dcjet.cs_cus.sj.dao.GwstdPackagingStatisticsMapper;
import com.dcjet.cs_cus.sj.mapper.GwstdPackagingStatisticsDtoMapper;
import com.dcjet.cs_cus.sj.model.GwstdPackagingStatistics;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-9-8
 */
@Service
public class GwstdPackagingStatisticsService extends BaseService<GwstdPackagingStatistics> {
    @Resource
    private GwstdPackagingStatisticsMapper gwstdPackagingStatisticsMapper;
    @Resource
    private GwstdPackagingStatisticsDtoMapper gwstdPackagingStatisticsDtoMapper;

    @Override
    public Mapper<GwstdPackagingStatistics> getMapper() {
        return gwstdPackagingStatisticsMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdPackagingStatisticsParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdPackagingStatisticsDto>> getListPaged(GwstdPackagingStatisticsParam gwstdPackagingStatisticsParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        GwstdPackagingStatistics gwstdPackagingStatistics = gwstdPackagingStatisticsDtoMapper.toPo(gwstdPackagingStatisticsParam);
        gwstdPackagingStatistics.setTradeCode(userInfo.getCompany());
        gwstdPackagingStatistics.setPendingReturns(gwstdPackagingStatistics.getPendingReturn()!=null?gwstdPackagingStatistics.getPendingReturn().toString():null);
        gwstdPackagingStatistics.setResellPendingReturns(gwstdPackagingStatistics.getResellPendingReturn()!=null?gwstdPackagingStatistics.getResellPendingReturn().toPlainString():null);
        gwstdPackagingStatistics.setRemainingEmptys(gwstdPackagingStatistics.getRemainingEmpty()!=null?gwstdPackagingStatistics.getRemainingEmpty().toString():null);
        Page<GwstdPackagingStatistics> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdPackagingStatisticsMapper.getList(gwstdPackagingStatistics));
        List<GwstdPackagingStatisticsDto> gwstdPackagingStatisticsDtos = page.getResult().stream().map(head -> {
            GwstdPackagingStatisticsDto dto = gwstdPackagingStatisticsDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdPackagingStatisticsDto>> paged = ResultObject.createInstance(gwstdPackagingStatisticsDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdPackagingStatisticsDto> selectAll(GwstdPackagingStatisticsParam exportParam, UserInfoToken userInfo) {
        GwstdPackagingStatistics gwstdPackagingStatistics = gwstdPackagingStatisticsDtoMapper.toPo(exportParam);
        gwstdPackagingStatistics.setTradeCode(userInfo.getCompany());
        List<GwstdPackagingStatisticsDto> gwstdPackagingStatisticsDtos = new ArrayList<>();
        List<GwstdPackagingStatistics> gwstdPackagingStatisticss = gwstdPackagingStatisticsMapper.getList(gwstdPackagingStatistics);
        if (CollectionUtils.isNotEmpty(gwstdPackagingStatisticss)) {
            gwstdPackagingStatisticsDtos = gwstdPackagingStatisticss.stream().map(head -> {
                GwstdPackagingStatisticsDto dto = gwstdPackagingStatisticsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdPackagingStatisticsDtos;
    }

    public void reportPackagingStatistics(String tradeCode) {
        gwstdPackagingStatisticsMapper.deleteReportDecEHeadList(tradeCode);
        gwstdPackagingStatisticsMapper.insertReportDecEHeadList(tradeCode);
    }
}
