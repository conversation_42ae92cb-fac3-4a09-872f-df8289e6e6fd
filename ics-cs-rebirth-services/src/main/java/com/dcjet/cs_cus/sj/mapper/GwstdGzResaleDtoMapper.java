package com.dcjet.cs_cus.sj.mapper;

import com.dcjet.cs.dto_cus.sj.GwstdGzResaleDto;
import com.dcjet.cs.dto_cus.sj.GwstdGzResaleParam;
import com.dcjet.cs_cus.sj.model.GwstdGzResale;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwstdGzResaleDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwstdGzResaleDto toDto(GwstdGzResale po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwstdGzResale toPo(GwstdGzResaleParam param);

    /**
     * 数据库原始数据更新
     *
     * @param gwstdGzResaleParam
     * @param gwstdGzResale
     */
    void updatePo(GwstdGzResaleParam gwstdGzResaleParam, @MappingTarget GwstdGzResale gwstdGzResale);

    default void patchPo(GwstdGzResaleParam gwstdGzResaleParam, GwstdGzResale gwstdGzResale) {
        // TODO 自行实现局部更新
    }
}
