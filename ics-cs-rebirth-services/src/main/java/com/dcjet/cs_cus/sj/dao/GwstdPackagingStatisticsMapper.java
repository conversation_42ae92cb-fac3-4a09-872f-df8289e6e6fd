package com.dcjet.cs_cus.sj.dao;

import com.dcjet.cs_cus.sj.model.GwstdPackagingStatistics;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwstdPackagingStatistics
 *
 * <AUTHOR>
 * @date: 2022-9-8
 */
public interface GwstdPackagingStatisticsMapper extends Mapper<GwstdPackagingStatistics> {
    /**
     * 查询获取数据
     *
     * @param gwstdPackagingStatistics
     * @return
     */
    List<GwstdPackagingStatistics> getList(GwstdPackagingStatistics gwstdPackagingStatistics);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    void deleteReportDecEHeadList(String tradeCode);

    void insertReportDecEHeadList(String tradeCode);

    Integer selectDataCount(GwstdPackagingStatistics decEEntryHead);
}
