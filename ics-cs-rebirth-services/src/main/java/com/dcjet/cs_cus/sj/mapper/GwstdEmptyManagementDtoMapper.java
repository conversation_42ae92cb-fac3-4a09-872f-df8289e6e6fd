package com.dcjet.cs_cus.sj.mapper;

import com.dcjet.cs.dto_cus.sj.GwstdEmptyManagementDto;
import com.dcjet.cs.dto_cus.sj.GwstdEmptyManagementParam;
import com.dcjet.cs_cus.sj.model.GwstdEmptyManagement;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwstdEmptyManagementDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwstdEmptyManagementDto toDto(GwstdEmptyManagement po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwstdEmptyManagement toPo(GwstdEmptyManagementParam param);

    /**
     * 数据库原始数据更新
     *
     * @param gwstdEmptyManagementParam
     * @param gwstdEmptyManagement
     */
    void updatePo(GwstdEmptyManagementParam gwstdEmptyManagementParam, @MappingTarget GwstdEmptyManagement gwstdEmptyManagement);

    default void patchPo(GwstdEmptyManagementParam gwstdEmptyManagementParam, GwstdEmptyManagement gwstdEmptyManagement) {
        // TODO 自行实现局部更新
    }
}
