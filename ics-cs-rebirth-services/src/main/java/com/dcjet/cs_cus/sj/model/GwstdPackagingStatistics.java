package com.dcjet.cs_cus.sj.model;

import java.io.Serializable;
import java.math.BigDecimal;

import com.dcjet.cs.base.model.BasicModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-8
 */
@Setter
@Getter
@Table(name = "t_gwstd_packaging_statistics")
public class GwstdPackagingStatistics extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 企业料号
     */
    @Column(name = "fac_g_no")
    private String facGNo;
    /**
     * 暂时进镜
     */
    @Column(name = "temporary_entry")
    private BigDecimal temporaryEntry;
    /**
     * 退运出境
     */
    @Column(name = "outbound")
    private BigDecimal outbound;
    /**
     * 转卖进
     */
    @Column(name = "resell_in")
    private BigDecimal resellIn;
    /**
     * 转卖出
     */
    @Column(name = "resell_out")
    private BigDecimal resellOut;
    /**
     * 缴税进口
     */
    @Column(name = "pay_i_tax")
    private BigDecimal payITax;
    /**
     * 2600待退运
     */
    @Column(name = "pending_return")
    private BigDecimal pendingReturn;
    /**
     * 转卖待退运
     */
    @Column(name = "resell_pending_return")
    private BigDecimal resellPendingReturn;
    /**
     * 还空总量
     */
    @Column(name = "total_empty")
    private BigDecimal totalEmpty;
    /**
     * 还空剩余
     */
    @Column(name = "remaining_empty")
    private BigDecimal remainingEmpty;
    /**
     * 创建时间-开始
     */
    @Transient
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @Transient
    private String insertTimeTo;

    @Transient
    private String pendingReturns;
    @Transient
    private String resellPendingReturns;
    @Transient
    private String remainingEmptys;
}
