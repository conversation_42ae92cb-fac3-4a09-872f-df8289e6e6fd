package com.dcjet.cs_cus.sj.service;

import com.dcjet.cs.dto_cus.sj.GwstdEmptyManagementDto;
import com.dcjet.cs.dto_cus.sj.GwstdEmptyManagementParam;
import com.dcjet.cs_cus.sj.dao.GwstdEmptyManagementMapper;
import com.dcjet.cs_cus.sj.mapper.GwstdEmptyManagementDtoMapper;
import com.dcjet.cs_cus.sj.model.GwstdEmptyManagement;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Service
public class GwstdEmptyManagementService extends BaseService<GwstdEmptyManagement> {
    @Resource
    private GwstdEmptyManagementMapper gwstdEmptyManagementMapper;
    @Resource
    private GwstdEmptyManagementDtoMapper gwstdEmptyManagementDtoMapper;

    @Override
    public Mapper<GwstdEmptyManagement> getMapper() {
        return gwstdEmptyManagementMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdEmptyManagementParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdEmptyManagementDto>> getListPaged(GwstdEmptyManagementParam gwstdEmptyManagementParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        GwstdEmptyManagement gwstdEmptyManagement = gwstdEmptyManagementDtoMapper.toPo(gwstdEmptyManagementParam);
        gwstdEmptyManagement.setTradeCode(userInfo.getCompany());
        Page<GwstdEmptyManagement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEmptyManagementMapper.getList(gwstdEmptyManagement));
        List<GwstdEmptyManagementDto> gwstdEmptyManagementDtos = page.getResult().stream().map(head -> {
            GwstdEmptyManagementDto dto = gwstdEmptyManagementDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdEmptyManagementDto>> paged = ResultObject.createInstance(gwstdEmptyManagementDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwstdEmptyManagementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdEmptyManagementDto insert(GwstdEmptyManagementParam gwstdEmptyManagementParam, UserInfoToken userInfo) {
        GwstdEmptyManagement gwstdEmptyManagement = gwstdEmptyManagementDtoMapper.toPo(gwstdEmptyManagementParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwstdEmptyManagement.setSid(sid);
        gwstdEmptyManagement.setInsertUser(userInfo.getUserNo());
        gwstdEmptyManagement.setInsertUserName(userInfo.getUserName());
        gwstdEmptyManagement.setInsertTime(new Date());
        gwstdEmptyManagement.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = gwstdEmptyManagementMapper.insert(gwstdEmptyManagement);
        return insertStatus > 0 ? gwstdEmptyManagementDtoMapper.toDto(gwstdEmptyManagement) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwstdEmptyManagementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdEmptyManagementDto update(GwstdEmptyManagementParam gwstdEmptyManagementParam, UserInfoToken userInfo) {
        GwstdEmptyManagement gwstdEmptyManagement = gwstdEmptyManagementMapper.selectByPrimaryKey(gwstdEmptyManagementParam.getSid());
        gwstdEmptyManagementDtoMapper.updatePo(gwstdEmptyManagementParam, gwstdEmptyManagement);
        gwstdEmptyManagement.setUpdateUser(userInfo.getUserNo());
        gwstdEmptyManagement.setUpdateUserName(userInfo.getUserName());
        gwstdEmptyManagement.setUpdateTime(new Date());
        // 更新数据
        int update = gwstdEmptyManagementMapper.updateByPrimaryKey(gwstdEmptyManagement);
        return update > 0 ? gwstdEmptyManagementDtoMapper.toDto(gwstdEmptyManagement) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwstdEmptyManagementMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdEmptyManagementDto> selectAll(GwstdEmptyManagementParam exportParam, UserInfoToken userInfo) {
        GwstdEmptyManagement gwstdEmptyManagement = gwstdEmptyManagementDtoMapper.toPo(exportParam);
        gwstdEmptyManagement.setTradeCode(userInfo.getCompany());
        List<GwstdEmptyManagementDto> gwstdEmptyManagementDtos = new ArrayList<>();
        List<GwstdEmptyManagement> gwstdEmptyManagements = gwstdEmptyManagementMapper.getList(gwstdEmptyManagement);
        if (CollectionUtils.isNotEmpty(gwstdEmptyManagements)) {
            gwstdEmptyManagementDtos = gwstdEmptyManagements.stream().map(head -> {
                GwstdEmptyManagementDto dto = gwstdEmptyManagementDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdEmptyManagementDtos;
    }
}
