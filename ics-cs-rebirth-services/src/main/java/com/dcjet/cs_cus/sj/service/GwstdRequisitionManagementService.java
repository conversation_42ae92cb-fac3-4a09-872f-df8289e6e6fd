package com.dcjet.cs_cus.sj.service;

import com.dcjet.cs.dto_cus.sj.GwstdRequisitionManagementDto;
import com.dcjet.cs.dto_cus.sj.GwstdRequisitionManagementParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.sj.dao.GwstdRequisitionManagementMapper;
import com.dcjet.cs_cus.sj.mapper.GwstdRequisitionManagementDtoMapper;
import com.dcjet.cs_cus.sj.model.GwstdRequisitionManagement;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Service
public class GwstdRequisitionManagementService extends BaseService<GwstdRequisitionManagement> {
    @Resource
    private GwstdRequisitionManagementMapper gwstdRequisitionManagementMapper;
    @Resource
    private GwstdRequisitionManagementDtoMapper gwstdRequisitionManagementDtoMapper;

    @Override
    public Mapper<GwstdRequisitionManagement> getMapper() {
        return gwstdRequisitionManagementMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdRequisitionManagementParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdRequisitionManagementDto>> getListPaged(GwstdRequisitionManagementParam gwstdRequisitionManagementParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        GwstdRequisitionManagement gwstdRequisitionManagement = gwstdRequisitionManagementDtoMapper.toPo(gwstdRequisitionManagementParam);
        gwstdRequisitionManagement.setTradeCode(userInfo.getCompany());
        Page<GwstdRequisitionManagement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdRequisitionManagementMapper.getList(gwstdRequisitionManagement));
        List<GwstdRequisitionManagementDto> gwstdRequisitionManagementDtos = page.getResult().stream().map(head -> {
            GwstdRequisitionManagementDto dto = gwstdRequisitionManagementDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdRequisitionManagementDto>> paged = ResultObject.createInstance(gwstdRequisitionManagementDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwstdRequisitionManagementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdRequisitionManagementDto insert(GwstdRequisitionManagementParam gwstdRequisitionManagementParam, UserInfoToken userInfo) {
        GwstdRequisitionManagement gwstdRequisitionManagement = gwstdRequisitionManagementDtoMapper.toPo(gwstdRequisitionManagementParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwstdRequisitionManagement.setSid(sid);
        gwstdRequisitionManagement.setInsertUser(userInfo.getUserNo());
        gwstdRequisitionManagement.setInsertUserName(userInfo.getUserName());
        gwstdRequisitionManagement.setInsertTime(new Date());
        gwstdRequisitionManagement.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = gwstdRequisitionManagementMapper.insert(gwstdRequisitionManagement);
        return insertStatus > 0 ? gwstdRequisitionManagementDtoMapper.toDto(gwstdRequisitionManagement) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwstdRequisitionManagementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdRequisitionManagementDto update(GwstdRequisitionManagementParam gwstdRequisitionManagementParam, UserInfoToken userInfo) {
        GwstdRequisitionManagement gwstdRequisitionManagement = gwstdRequisitionManagementMapper.selectByPrimaryKey(gwstdRequisitionManagementParam.getSid());
        gwstdRequisitionManagementDtoMapper.updatePo(gwstdRequisitionManagementParam, gwstdRequisitionManagement);
        gwstdRequisitionManagement.setUpdateUser(userInfo.getUserNo());
        gwstdRequisitionManagement.setUpdateUserName(userInfo.getUserName());
        gwstdRequisitionManagement.setUpdateTime(new Date());
        // 更新数据
        int update = gwstdRequisitionManagementMapper.updateByPrimaryKey(gwstdRequisitionManagement);
        return update > 0 ? gwstdRequisitionManagementDtoMapper.toDto(gwstdRequisitionManagement) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwstdRequisitionManagementMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdRequisitionManagementDto> selectAll(GwstdRequisitionManagementParam exportParam, UserInfoToken userInfo) {
        GwstdRequisitionManagement gwstdRequisitionManagement = gwstdRequisitionManagementDtoMapper.toPo(exportParam);
        gwstdRequisitionManagement.setTradeCode(userInfo.getCompany());
        List<GwstdRequisitionManagementDto> gwstdRequisitionManagementDtos = new ArrayList<>();
        List<GwstdRequisitionManagement> gwstdRequisitionManagements = gwstdRequisitionManagementMapper.getList(gwstdRequisitionManagement);
        if (CollectionUtils.isNotEmpty(gwstdRequisitionManagements)) {
            gwstdRequisitionManagementDtos = gwstdRequisitionManagements.stream().map(head -> {
                GwstdRequisitionManagementDto dto = gwstdRequisitionManagementDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdRequisitionManagementDtos;
    }
}
