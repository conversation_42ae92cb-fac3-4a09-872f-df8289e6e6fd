package com.dcjet.cs.entrustFile.service;

import com.alibaba.excel.util.DateUtils;
import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.dao.BiCustomerParamsMapper;
import com.dcjet.cs.bi.dao.BiNotifyMapper;
import com.dcjet.cs.bi.dao.BiShiptoMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.bi.model.BiNotify;
import com.dcjet.cs.bi.model.BiShipto;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.bi.GwstdSealParam;
import com.dcjet.cs.dto.entrustFile.EntrustFileHeadDto;
import com.dcjet.cs.dto.entrustFile.EntrustFileHeadParam;
import com.dcjet.cs.entrustFile.dao.EntrustFileHeadMapper;
import com.dcjet.cs.entrustFile.mapper.EntrustFileHeadDtoMapper;
import com.dcjet.cs.entrustFile.model.EntrustFileHead;
import com.dcjet.cs.erp.dao.DecEPackingListMapper;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpEListNMapper;
import com.dcjet.cs.erp.model.DecEPackingList;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.invoice.dao.DecInvoiceBoxListEMapper;
import com.dcjet.cs.invoice.model.DecInvoiceBoxListE;
import com.dcjet.cs.invoice.service.DecInvoiceBoxListEService;
import com.dcjet.cs.receipt.dao.ReceiptTemplateListMapper;
import com.dcjet.cs.receipt.model.ReceiptTemplateList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.FileUtil;
import com.dcjet.cs.util.bulkSql.SpringContextUtil;
import com.dcjet.cs.util.customexport.ExportDataSource;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import io.micrometer.core.instrument.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Service
public class EntrustFileHeadService extends BaseService<EntrustFileHead> {
    protected final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**
     * 模板文件临时存储路径
     */
    @Value("${dc.export.temp: }")
    private String tempPath;

    @Resource
    private EntrustFileHeadMapper entrustFileHeadMapper;
    @Resource
    private EntrustFileHeadDtoMapper entrustFileHeadDtoMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecErpEListNMapper decErpEListNMapper;
    @Resource
    private DecInvoiceBoxListEService decInvoiceBoxListEService;
    @Resource
    private ReceiptTemplateListMapper receiptTemplateListMapper;
    @Resource
    private DecInvoiceBoxListEMapper decInvoiceBoxListEMapper;
    @Resource
    private BiCustomerParamsMapper biCustomerParamsMapper;
    @Resource
    private DecEPackingListMapper decEPackingListMapper;
    @Resource
    private FastdFsService fastdFsService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private BiShiptoMapper biShiptoMapper;
    @Resource
    private BiNotifyMapper biNotifyMapper;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    /**
     * 文件服务器客户端
     */
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Override
    public Mapper<EntrustFileHead> getMapper() {
        return entrustFileHeadMapper;
    }

    /**
     * 获取分页信息
     *
     * @param entrustFileHeadParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject getListPaged(EntrustFileHeadParam entrustFileHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        EntrustFileHead entrustFileHead = entrustFileHeadDtoMapper.toPo(entrustFileHeadParam);
        entrustFileHead.setTradeCode(userInfo.getCompany());
        Page<EntrustFileHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> entrustFileHeadMapper.getList(entrustFileHead));
        List<EntrustFileHeadDto> entrustFileHeadDtos = page.getResult().stream().map(head -> {
            EntrustFileHeadDto dto = entrustFileHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        return ResultObject.createInstance(entrustFileHeadDtos, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 功能描述:修改
     *
     * @param entrustFileHeadParam
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<EntrustFileHeadDto> update(EntrustFileHeadParam entrustFileHeadParam) {
        ResultObject<EntrustFileHeadDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        EntrustFileHead entrustFileHead = entrustFileHeadMapper.selectByPrimaryKey(entrustFileHeadParam.getSid());
        if (entrustFileHead != null) {
            entrustFileHeadDtoMapper.updatePo(entrustFileHeadParam, entrustFileHead);
            int update = entrustFileHeadMapper.updateByPrimaryKey(entrustFileHead);
            EntrustFileHeadDto entrustFileHeadDto = update > 0 ? entrustFileHeadDtoMapper.toDto(entrustFileHead) : null;
            if (entrustFileHeadDto != null) {
                resultObject.setData(entrustFileHeadDto);
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
            }
            return resultObject;
        } else {
            return null;
        }
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject delete(List<String> sids) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        if (CollectionUtils.isNotEmpty(sids)) {
            for (String sid : sids) {
                EntrustFileHead entrustFileHead = entrustFileHeadMapper.selectByPrimaryKey(sid);
                if (StringUtils.isNotEmpty(entrustFileHead.getHeadId())) {
                    //获取关联提单表头的sid，重置提取状态为未提取
                    String[] headIds = entrustFileHead.getHeadId().split(",");
                    decErpEHeadNMapper.updateStatus(Arrays.asList(headIds), ConstantsStatus.STATUS_0);
                }
                entrustFileHeadMapper.deleteByPrimaryKey(sid);
            }
            List<DecInvoiceBoxListE> decInvoiceBoxListEList = decInvoiceBoxListEMapper.getListByHeadIds(sids);
            if (CollectionUtils.isNotEmpty(decInvoiceBoxListEList)) {
                for (DecInvoiceBoxListE decInvoiceBoxListE : decInvoiceBoxListEList) {
                    fileHandler.deleteFile(decInvoiceBoxListE.getFdfsId());
                    fileHandler.deleteFile(decInvoiceBoxListE.getFdfsPdfId());
                    decInvoiceBoxListEMapper.deleteByPrimaryKey(decInvoiceBoxListE.getSid());
                }
            }
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("数据不存在！"));
        }
        return resultObject;

    }

    public ResultObject<EntrustFileHeadDto> extract(List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("提取成功"));
        if (CollectionUtils.isNotEmpty(sids)) {
            List<DecErpEHeadN> decErpEHeadNS = decErpEHeadNMapper.selectBySids(sids);
            if (CollectionUtils.isNotEmpty(decErpEHeadNS)) {
                Map<Object, List<DecErpEHeadN>> map = decErpEHeadNS.stream().collect(Collectors.groupingBy(e ->
                        e.getTransMode() + "," + e.getForwardCode() + "," + e.getOverseasShipper() + "," + e.getNotify() + "," + e.getTrafMode() + "," + e.getTradeCountry()));
                if (map.keySet().size() == 1) {
                    EntrustFileHead entrustFileHead = new EntrustFileHead();
                    entrustFileHead.setSid(UUID.randomUUID().toString());
                    entrustFileHead.setEmsListNo(decErpEHeadNS.stream().map(DecErpEHeadN::getEmsListNo).collect(Collectors.joining(",")));
                    entrustFileHead.setInvoiceNo(decErpEHeadNS.stream().filter(e -> StringUtils.isNotEmpty(e.getInvoiceNo())).
                            map(DecErpEHeadN::getInvoiceNo).collect(Collectors.joining(",")));
                    entrustFileHead.setForwardCode(decErpEHeadNS.get(0).getForwardCode());
                    entrustFileHead.setTradeTerms(decErpEHeadNS.get(0).getTradeTerms());
                    entrustFileHead.setTrafMode(decErpEHeadNS.get(0).getTrafMode());
                    entrustFileHead.setTradeCountry(decErpEHeadNS.get(0).getTradeCountry());
                    entrustFileHead.setOverseasShipper(decErpEHeadNS.get(0).getOverseasShipper());
                    entrustFileHead.setOverseasShipperName(decErpEHeadNS.get(0).getOverseasShipperName());
                    entrustFileHead.setNotify(decErpEHeadNS.get(0).getNotify());
                    entrustFileHead.setHeadId(decErpEHeadNS.stream().map(DecErpEHeadN::getSid).collect(Collectors.joining(",")));
                    entrustFileHead.setPackNum(decErpEHeadNS.stream().filter(e -> e.getPackNum() != null).map(DecErpEHeadN::getPackNum).reduce(BigDecimal.ZERO, BigDecimal::add));
                    List<DecEPackingList> decEPackingLists = decEPackingListMapper.getListByHeadIds(sids, userInfo.getCompany());
                    if (CollectionUtils.isNotEmpty(decEPackingLists)) {
                        Map<String, List<DecEPackingList>> wholeMap = decEPackingLists.stream().collect(
                                Collectors.groupingBy(e -> e.getTotalPalletL().stripTrailingZeros().toPlainString() + "*"
                                        + e.getTotalPalletW().stripTrailingZeros().toPlainString() + "*"
                                        + e.getTotalPalletH().stripTrailingZeros().toPlainString()));
                        Map<String, List<DecEPackingList>> bciMap = decEPackingLists.stream().filter(e ->
                                e.getBciH() != null && e.getBciH().compareTo(BigDecimal.ZERO) != 0).collect(
                                Collectors.groupingBy(e -> e.getTotalPalletL().stripTrailingZeros().toPlainString() + "*"
                                        + e.getTotalPalletW().stripTrailingZeros().toPlainString() + "*" + e.getBciH().stripTrailingZeros().toPlainString()));
                        Integer trayQty = decEPackingLists.stream().filter(e -> e.getPalletNum() != null).map(e -> e.getPalletNum().intValue()).reduce(0, Integer::sum);
                        if (decEPackingLists.stream().anyMatch(e -> e.getBciQty() != null)) {
                            BigDecimal bci = decEPackingLists.stream().filter(e -> e.getBciQty() != null).map(e -> e.getBciQty()).reduce(BigDecimal::add).get().setScale(0);
                            if (bci != null) {
                                trayQty += Integer.valueOf(bci.toString());
                            }
                        }
                        entrustFileHead.setTrayQty(trayQty);
                        Map<String, Integer> packingMap = new HashMap<>();
                        if (MapUtils.isNotEmpty(wholeMap)) {
                            for (String key : wholeMap.keySet()) {
                                Integer palletNum = wholeMap.get(key).stream().filter(e -> e.getPalletNum() != null).map(e -> e.getPalletNum().intValue()).reduce(Integer::sum).get();
                                if (packingMap.containsKey(key)) {
                                    packingMap.put(key, packingMap.get(key) + palletNum);
                                } else {
                                    packingMap.put(key, palletNum);
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(bciMap)) {
                            for (String key : bciMap.keySet()) {
                                Integer bciQty = Integer.valueOf(bciMap.get(key).stream().filter(e -> e.getBciQty() != null).map(e -> e.getBciQty()).reduce(BigDecimal::add).get().setScale(0).toString());
                                if (packingMap.containsKey(key)) {
                                    packingMap.put(key, packingMap.get(key) + bciQty);
                                } else {
                                    packingMap.put(key, bciQty);
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(packingMap)) {
                            StringBuilder sb = new StringBuilder();
                            for (String key : packingMap.keySet()) {
                                if (packingMap.get(key) != 0) {
                                    sb.append(key).append("CM*").append(packingMap.get(key)).append("PLTS").append("；");
                                }
                            }
                            sb.delete(sb.length() - 1, sb.length());
                            entrustFileHead.setTrayDetail(sb.toString());
                        }

                    }
                    DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.sumWtByHeadIds(sids);
                    if (decErpEHeadN != null) {
                        entrustFileHead.setNetWt(decErpEHeadN.getNetWt());
                        entrustFileHead.setGrossWt(decErpEHeadN.getGrossWt());
                        entrustFileHead.setVolume(decErpEHeadN.getVolume());
                    }
                    entrustFileHead.setInsertTime(new Date());
                    entrustFileHead.setInsertUser(userInfo.getUserNo());
                    entrustFileHead.setInsertUserName(userInfo.getUserName());
                    entrustFileHead.setTradeCode(userInfo.getCompany());
                    entrustFileHeadMapper.insert(entrustFileHead);
                    //更新预录入单的提取状态
                    decErpEHeadNMapper.updateStatus(sids, ConstantsStatus.STATUS_1);
                    resultObject.setData(entrustFileHeadDtoMapper.toDto(entrustFileHead));
                } else {
                    resultObject.setSuccess(false);
                    resultObject.setMessage(xdoi18n.XdoI18nUtil.t("提取数据中存在成交方式、货运代理、境外收货人、Notify、运输方式、运抵国不同的数据，提取失败！"));
                }
            } else {
                return null;
            }
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("待提取数据为空，提取失败！"));
        }
        return resultObject;
    }

    /**
     * 功能描述: 生成单据
     *
     * @param: headSid 表头sid
     * @param: templateHeadId 模板表头id
     * @return:
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject createBills(GwstdSealParam gwstdSealParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        String headSid = gwstdSealParam.getHeadId();
        //删除表体数据
        decInvoiceBoxListEService.deleteBodyByHeadSids(new ArrayList<String>() {{
            add(headSid);
        }});
        //获取表头数据
        EntrustFileHead entrustFileHead = entrustFileHeadMapper.selectByPrimaryKey(headSid);
        //获取关联的模板详细信息
        List<ReceiptTemplateList> receiptTemplateLists = receiptTemplateListMapper.getTemplateDetailListInfoByHeadId(entrustFileHead.getTemplateHeadId());
        if (CollectionUtils.isNotEmpty(receiptTemplateLists)) {
            //判断关联编号表体是否存在数据
            List<String> erpHeadIds = Arrays.asList(entrustFileHead.getHeadId().split(","));
            //关联出口提单表体量
            int erpBodyCount = decErpEListNMapper.getBodyCount(new DecErpEListN() {{
                setHeadIds(erpHeadIds);
            }});
            if (erpBodyCount == 0) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("关联提单表体无数据，无法生成单据！"));
            }
            //模板临时存储路径
            String selfDirName = UUID.randomUUID().toString();
            String tempDirectory = tempPath + "EntrustFile" + File.separator + selfDirName + File.separator;
            //已上传fdfs文件列表
            List<String> listFdfs = new ArrayList<>();
            try {
                //创建文件夹
                FileUtil.dirExists(tempDirectory);
                //填充数据源
                ExportDataSource exportDataSource = new ExportDataSource();
                //获取填充的表头数据
                Map<String, Object> headMap = this.getHeadConvertData(entrustFileHead);
                //获取填充表体数据的汇总值
                Map<String, BigDecimal> sumMap = entrustFileHeadMapper.getSumQty(erpHeadIds, userInfo.getCompany());
                if (MapUtils.isNotEmpty(sumMap)) {
                    Set<String> keys = sumMap.keySet();
                    for (String key : keys) {
                        headMap.put(key.toUpperCase(), sumMap.get(key));
                    }
                }
                Map<String, Object> tableMap = new HashMap<>();
                exportDataSource.setBodyHashMap(tableMap);

                //获取填充的表体数据
                List<Map<String, Object>> bodyData = entrustFileHeadMapper.getBodyDataList(erpHeadIds, userInfo.getCompany());
                List<Map<String, Object>> bigBody = new ArrayList<Map<String, Object>>();
                //将集合遍历
                if (CollectionUtils.isEmpty(bodyData)) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未维护对应箱单信息"));
                }
                for (int i = 0; i < bodyData.size(); i++) {
                    //循环new  map集合
                    Map<String, Object> obdmap = new HashMap<String, Object>();
                    Map<String, Object> oldMap = bodyData.get(i);
                    Map<String, Object> nowMap = new HashMap<String, Object>();
                    Set<String> se = oldMap.keySet();
                    for (String set : se) {
                        //在循环将大写的KEY和VALUE 放到新的Map
                        nowMap.put(set.toUpperCase(), oldMap.get(set));
                    }
                    StringBuilder sb = new StringBuilder();
                    if (nowMap.get("PALLET_L") == null || nowMap.get("PALLET_W") == null || nowMap.get("PALLET_H") == null) {
                        nowMap.put("TRAY_DETAIL", sb.toString());
                    } else {
                        String pallentL = new BigDecimal(nowMap.get("PALLET_L").toString()).stripTrailingZeros().toPlainString();
                        String pallentW = new BigDecimal(nowMap.get("PALLET_W").toString()).stripTrailingZeros().toPlainString();
                        String pallentH = new BigDecimal(nowMap.get("PALLET_H").toString()).stripTrailingZeros().toPlainString();
                        String pallentNum = (nowMap.get("PALLET_NUM_ONE") == null ? null : new BigDecimal(nowMap.get("PALLET_NUM_ONE").toString()).stripTrailingZeros().toPlainString());
                        String bciH = "";
                        if (nowMap.get("BCI_H") != null) {
                            bciH = new BigDecimal(nowMap.get("BCI_H").toString()).stripTrailingZeros().toPlainString();
                        }
                        String bciQty = "";
                        if (nowMap.get("BCI_QTY") != null) {
                            bciQty = new BigDecimal(nowMap.get("BCI_QTY").toString()).stripTrailingZeros().toPlainString();
                        }

                        if (pallentL != null && pallentW != null && pallentH != null
                                && pallentNum != null && !ConstantsStatus.STATUS_0.equals(pallentNum)) {
                            sb.append(pallentL).append("*").append(pallentW).append("*").append(pallentH).append("CM*").append(pallentNum).append("PLTS");
                        }
                        if (StringUtils.isNotEmpty(bciH) && StringUtils.isNotEmpty(bciQty) && !ConstantsStatus.STATUS_0.equals(bciQty)) {
                            sb.append("；").append(pallentL).append("*").append(pallentW)
                                    .append("*").append(bciH).append("CM*").append(bciQty).append("PLTS");
                        }
                        if (StringUtils.isNotEmpty(sb.toString()) && sb.toString().startsWith("；")) {
                            nowMap.put("TRAY_DETAIL", sb.toString().replace("；", ""));
                        } else {
                            nowMap.put("TRAY_DETAIL", sb.toString());
                        }

                    }
                    nowMap.put("ORDER_NO", entrustFileHead.getOrderNo());
                    nowMap.put("BOOKING_DATE", entrustFileHead.getBookingDate());
                    nowMap.put("TRAF_MODE", headMap.get("TRAF_MODE"));
                    nowMap.put("OVERSEAS_SHIPPER_NAME", entrustFileHead.getOverseasShipperName());
                    if (nowMap.get("TRADE_MODE") != null) {
                        String tradeMode = pCodeHolder.getValue(PCodeType.TRADE, nowMap.get("TRADE_MODE").toString());
                        nowMap.put("TRADE_MODE", tradeMode);
                    }
                    //将Map放进List集合里
                    obdmap.putAll(nowMap);
                    bigBody.add(obdmap);
                }
                tableMap.put("body", bigBody);
                exportDataSource.setVriableMap(headMap);

                //循环下载模板文件并填充数据
                for (ReceiptTemplateList templateInfo : receiptTemplateLists) {
                    String localFilePath = tempDirectory + userInfo.getCompany() + templateInfo.getFileNameOrigin();
                    //下载模板文件
                    fastdFsService.downloadFileToLocal(templateInfo.getFdfsId(), localFilePath);
                    String path = localFilePath;

                    byte[] fileBytes = ExportService.getTempExportFileStream(exportDataSource, path, false);
                    byte[] fileBytesPdf = ExportService.getTempExportFileStream(exportDataSource, path, true);

                    //将填充好的模板文件上传至文件服务器
                    String fdfsPath = fileHandler.uploadFile(fileBytes, FileUtil.getExtention(templateInfo.getFileNameOrigin()));
                    listFdfs.add(fdfsPath);
                    //将填充好的模板文件上传至文件服务器
                    String fdfsPathPdf = fileHandler.uploadFile(fileBytesPdf, "pdf");
                    listFdfs.add(fdfsPathPdf);
                    //保存文件信息
                    String sid = UUID.randomUUID().toString();
                    DecInvoiceBoxListE decInvoiceBoxListE = new DecInvoiceBoxListE();
                    decInvoiceBoxListE.setSid(sid);
                    String fileName = FileUtil.getFileName(templateInfo.getFileNameOrigin());
                    //根据单据类型判断所需的单据号
                    switch (templateInfo.getBillType()) {
                        case "4"://托书
                            fileName = xdoi18n.XdoI18nUtil.t("托书_") + fileName;
                            break;
                        case "5"://订舱单
                            fileName = xdoi18n.XdoI18nUtil.t("订舱单_") + fileName;
                            break;
                        default:
                            break;
                    }
                    decInvoiceBoxListE.setFdfsId(fdfsPath);
                    decInvoiceBoxListE.setFdfsPdfId(fdfsPathPdf);
                    decInvoiceBoxListE.setHeadId(headSid);
                    decInvoiceBoxListE.setBillType(templateInfo.getBillType());
                    decInvoiceBoxListE.setTemplateId(templateInfo.getSid());
                    decInvoiceBoxListE.setBuildFileName(fileName);
                    decInvoiceBoxListE.setTemplateName(templateInfo.getTemplateName());
                    decInvoiceBoxListE.setSerialNo(templateInfo.getSerialNo());
                    decInvoiceBoxListE.setInsertUser(userInfo.getUserNo());
                    decInvoiceBoxListE.setInsertTime(new Date());
                    decInvoiceBoxListE.setTradeCode(userInfo.getCompany());
                    decInvoiceBoxListEMapper.insert(decInvoiceBoxListE);
                }
                Thread.sleep(1000);
            } catch (Exception ex) {
                //删除已上传的文件
                decInvoiceBoxListEService.deleteFastdfsFiles(listFdfs);
                resultObject.setSuccess(false);
                resultObject.setMessage(String.format("[%s]%s",ex.getMessage(),xdoi18n.XdoI18nUtil.t("生成单据出错")));
                logger.error("生成单据出错：", ex);
            } finally {
                try {
                    //删除文件夹
                    FileUtils.deleteDirectory(new File(tempDirectory));
                } catch (Exception ex) {
                    logger.error(String.format("生成单据出错：%s", ex));
                    throw new ErrorException(500, String.format("[%s]%s",ex.getMessage(),xdoi18n.XdoI18nUtil.t("生成单据出错") ));
                }
            }
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("生成单据出错：发票模板不完整"));
            return resultObject;
        }
        return resultObject;
    }

    /**
     * 功能描述:获取模板表头code转name后数据
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/7/18
     * @param: headSid 模板表头sid
     * @return:
     */
    private Map<String, Object> getHeadConvertData(EntrustFileHead entrustFileHead) {
        Map<String, Object> headData = new HashMap<>();
        headData.put("INSERT_TIME", DateUtils.format(entrustFileHead.getInsertTime(), DateUtils.DATE_FORMAT_10));
        headData.put("ORDER_NO", entrustFileHead.getOrderNo());
        headData.put("TRADE_TERMS", entrustFileHead.getTradeTerms());
        headData.put("TRAY_QTY", entrustFileHead.getTrayQty());
        headData.put("TRAY_DETAIL", entrustFileHead.getTrayDetail());
        Example example = new Example(BiCustomerParams.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("paramsType", "INV_TRAF");
        criteria.andEqualTo("tradeCode", entrustFileHead.getTradeCode());
        criteria.andEqualTo("customParamCode", entrustFileHead.getTrafMode());
        List<BiCustomerParams> list = biCustomerParamsMapper.selectByExample(example);
        if (list.size() > 0) {
            headData.put("TRAF_MODE", list.get(0).getParamsCode());
        }
        DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(entrustFileHead.getHeadId());
        if (decErpEHeadN != null && StringUtils.isNotEmpty(decErpEHeadN.getOverseasShipper())) {
            BiClientInformation biClientInformation = new BiClientInformation();
            biClientInformation.setCustomerCode(decErpEHeadN.getOverseasShipper());
            biClientInformation.setCustomerType(CommonVariable.CLI);
            biClientInformation.setTradeCode(entrustFileHead.getTradeCode());
            BiClientInformation biResult = biClientInformationMapper.selectOne(biClientInformation);
            if (biResult != null) {
                if (StringUtils.isNotEmpty(decErpEHeadN.getShipTo())) {
                    BiShipto biShipto = new BiShipto();
                    biShipto.setTradeCode(entrustFileHead.getTradeCode());
                    biShipto.setHeadId(biResult.getSid());
                    biShipto.setShipToCode(decErpEHeadN.getShipTo());
                    BiShipto shipToRes = biShiptoMapper.selectOne(biShipto);
                    if (shipToRes != null) {
                        headData.put("OVERSEAS_SHIPPER_NAME", shipToRes.getShipToAddress());
                    }
                }
                if (StringUtils.isNotEmpty(decErpEHeadN.getNotify())) {
                    BiNotify biNotify = new BiNotify();
                    biNotify.setTradeCode(entrustFileHead.getTradeCode());
                    biNotify.setHeadId(biResult.getSid());
                    biNotify.setNotifyCode(decErpEHeadN.getNotify());
                    BiNotify notifyRes = biNotifyMapper.selectOne(biNotify);
                    if (notifyRes != null) {
                        headData.put("NOTIFY", notifyRes.getNotifyAddress());
                    }
                }
            }
        }
        return headData;
    }

    public ResponseEntity download(GwstdSealParam gwstdSealParam, UserInfoToken userInfo) {
        DecInvoiceBoxListE t = new DecInvoiceBoxListE();
        t.setHeadId(gwstdSealParam.getHeadId());
        t.setTradeCode(userInfo.getCompany());
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        List<DecInvoiceBoxListE> list = new ArrayList<>();
        try {
            ss = ssf.openSession();
            list = decInvoiceBoxListEMapper.select(t);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }

        if (CollectionUtils.isEmpty(list)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("委托书及订舱单信息未生成"));
        }

        // 将发票箱单压缩下载
        // 文件服务器返回链接地址
        String fdfsId = "";
        // PDF文件服务器返回链接地址
        String fdfsPdfId = "";
        // 单据类型{1.发票 2.箱单 3.合同}
        String billType = "";
        // 生成文件名称
        String buildFileName = "";
        // 压缩文件名
        String zipName = "entrustZip";
        // 压缩文件所在文件夹路径
        String folderPath = tempPath + File.separator + zipName;
        // 压缩文件路径
        String zipPath = folderPath + File.separator + zipName + ".zip";
        // 创建文件夹
        FileUtil.dirExists(folderPath);
        try {
            for (DecInvoiceBoxListE tt : list) {
                Class<?> cla = tt.getClass();
                // 获取当前类所有的字段
                Field[] field = cla.getDeclaredFields();
                for (Field f : field) {
                    f.setAccessible(true);
                    try {
                        if ("fdfsId".equals(f.getName())) {
                            fdfsId = null == f.get(tt) ? null : f.get(tt).toString();
                        }
                        if ("fdfsPdfId".equals(f.getName())) {
                            fdfsPdfId = null == f.get(tt) ? null : f.get(tt).toString();
                        }
                        if ("billType".equals(f.getName())) {
                            billType = CommonEnum.InvoiceBoxType.getValue(null == f.get(tt) ? null : f.get(tt).toString());
                        }
                        if ("buildFileName".equals(f.getName())) {
                            buildFileName = billType + null == f.get(tt) ? null : f.get(tt).toString();
                        }
                    } catch (IllegalAccessException e) {
                        logger.error("获取{}值出错：{}", f.getName(), e);
                    }
                }
                String tempDirectory = folderPath + File.separator + buildFileName;
                // 下载excel
                if (StringUtils.isNotBlank(fdfsId)) {
                    //获取扩展名
                    String exdName = FileUtil.getExtention(fdfsId);
                    if (StringUtils.isNotEmpty(exdName)) {
                        fastdFsService.downloadFileToLocal(fdfsId, tempDirectory + "." + exdName);
                    } else {
                        fastdFsService.downloadFileToLocal(fdfsId, tempDirectory + ".xlsx");
                    }
                }
                // 下载pdf
                if (StringUtils.isNotBlank(fdfsPdfId)) {
                    fastdFsService.downloadFileToLocal(fdfsPdfId, tempDirectory + ".pdf");
                }
            }

            // 压缩文件
            boolean isZip = FileUtil.fileToZip(folderPath, folderPath, zipName);
            if (isZip) {
                HttpHeaders h = new HttpHeaders();
                h.setContentDispositionFormData("attachment", URLEncoder.encode(zipName + ".zip", CommonVariable.UTF8));
                h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(zipPath)), h, HttpStatus.OK);
            }
        } catch (Exception e) {
            logger.error("下载委托书及订舱单信息失败：{}", e);
        } finally {
            try {
                // 删除文件夹
                FileUtils.deleteDirectory(new File(folderPath));
            } catch (Exception ex) {
                logger.error(String.format("下载委托书及订舱单信息失败：%s", ex));
                throw new ErrorException(500, String.format("[%s]%s",ex,xdoi18n.XdoI18nUtil.t("下载委托书及订舱单信息失败")));
            }
        }
        return new ResponseEntity(HttpStatus.NOT_FOUND);
    }
}
