package com.dcjet.cs.entrustFile.mapper;

import com.dcjet.cs.dto.entrustFile.EntrustFileHeadDto;
import com.dcjet.cs.dto.entrustFile.EntrustFileHeadParam;
import com.dcjet.cs.entrustFile.model.EntrustFileHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-10-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EntrustFileHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    EntrustFileHeadDto toDto(EntrustFileHead po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    EntrustFileHead toPo(EntrustFileHeadParam param);

    /**
     * 数据库原始数据更新
     *
     * @param entrustFileHeadParam
     * @param entrustFileHead
     */
    void updatePo(EntrustFileHeadParam entrustFileHeadParam, @MappingTarget EntrustFileHead entrustFileHead);

    default void patchPo(EntrustFileHeadParam entrustFileHeadParam, EntrustFileHead entrustFileHead) {
        // TODO 自行实现局部更新
    }
}
