package com.dcjet.cs.entrustFile.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_ENTRUST_FILE_HEAD")
public class EntrustFileHead extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;

    /**
     * 单据内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;

    /**
     * 订舱单号
     */
    @Column(name = "ORDER_NO")
    private String orderNo;

    /**
     * 订舱日期
     */
    @Column(name = "BOOKING_DATE")
    private Date bookingDate;

    /**
     * 发票号
     */
    @Column(name = "INVOICE_NO")
    private String invoiceNo;

    /**
     * 境外收货人
     */
    @Column(name = "OVERSEAS_SHIPPER")
    private String overseasShipper;

    /**
     * 境外收货人名称
     */
    @Column(name = "OVERSEAS_SHIPPER_NAME")
    private String overseasShipperName;

    /**
     * Notify代码
     */
    @Column(name = "NOTIFY")
    private String notify;

    /**
     * 贸易条款
     */
    @Column(name = "TRADE_TERMS")
    private String tradeTerms;

    /**
     * 运输方式
     */
    @Column(name = "TRAF_MODE")
    private String trafMode;

    /**
     * 运输工具名称
     */
    @Column(name = "TRAF_NAME")
    private String trafName;

    /**
     * 货运代理
     */
    @Column(name = "FORWARD_CODE")
    private String forwardCode;

    /**
     * 总净重
     */
    @Column(name = "NET_WT")
    private BigDecimal netWt;

    /**
     * 总毛重
     */
    @Column(name = "GROSS_WT")
    private BigDecimal grossWt;

    /**
     * 总体积
     */
    @Column(name = "VOLUME")
    private BigDecimal volume;

    /**
     * 件数
     */
    @Column(name = "PACK_NUM")
    private BigDecimal packNum;

    /**
     * 运抵国
     */
    @Column(name = "TRADE_COUNTRY")
    private String tradeCountry;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 托盘详情
     */
    @Column(name = "TRAY_DETAIL")
    private String trayDetail;

    /**
     * 关联提单表头sid
     */
    @Column(name = "HEAD_ID")
    private String headId;

    /**
     * 关联箱单模板code
     */
    @Column(name = "TEMPLATE_CODE")
    private String templateCode;

    /**
     * 关联箱单模板Sid
     */
    @Column(name = "TEMPLATE_HEAD_ID")
    private String templateHeadId;

    /**
     * 托盘总数
     */
    @Column(name = "TRAY_QTY")
    private Integer trayQty;

    @Transient
    private String bookingDateFrom;
    @Transient
    private String bookingDateTo;
    @Transient
    private String insertTimeFrom;
    @Transient
    private String insertTimeTo;
}
