package com.dcjet.cs.dev.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-5-13
 */
@Setter
@Getter
@Table(name = "T_DEV_FREE_APPLY_HEAD")
public class DevFreeApplyHead extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 业务编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 业务种类
     */
    @Column(name = "BILL_TYPE")
    private String billType;
    /**
     * 业务种类名称
     */
    @Column(name = "BILL_TYPE_NAME")
    private String billTypeName;
    /**
     * 企业名称
     */
    @Column(name = "TRADE_NAME")
    private String tradeName;
    /**
     * 审批依据
     */
    @Column(name = "APPR_FROM")
    private String apprFrom;
    /**
     * 审批依据名称
     */
    @Column(name = "APPR_FROM_NAME")
    private String apprFromName;
    /**
     * 进出口标记，I进口 E出口
     */
    @Column(name = "I_E_MARK")
    private String IEMark;
    /**
     * 征免性质
     */
    @Column(name = "CUT_MODE")
    private String cutMode;
    /**
     * 项目确认书编号
     */
    @Column(name = "ITEM_NO")
    private String itemNo;
    /**
     * 产业政策审批条目
     */
    @Column(name = "APPR_ITEM")
    private String apprItem;
    /**
     * 产业政策审批条目名称
     */
    @Column(name = "APPR_ITEM_NAME")
    private String apprItemName;
    /**
     * 审批部门
     */
    @Column(name = "APPR_DEPT")
    private String apprDept;
    /**
     * 审批部门名称
     */
    @Column(name = "APPR_DEPT_NAME")
    private String apprDeptName;
    /**
     * 许可证号
     */
    @Column(name = "LICENSE_NO")
    private String licenseNo;
    /**
     * 备案号
     */
    @Column(name = "EMS_NO")
    private String emsNo;
    /**
     * 合同协议号
     */
    @Column(name = "CONTR_NO")
    private String contrNo;
    /**
     * 成交方式
     */
    @Column(name = "TRANS_MODE")
    private String transMode;
    /**
     * 项目性质
     */
    @Column(name = "ITEM_MODE")
    private String itemMode;
    /**
     * 入境口岸/出境口岸
     */
    @Column(name = "ENTRY_PORT")
    private String entryPort;
    /**
     * 是否申报进口，0 否 1 是
     */
    @Column(name = "IS_DECLARE")
    private String isDeclare;
    /**
     * 免表编号
     */
    @Column(name = "EXEMPTS_NO")
    private String exemptsNo;
    /**
     * 有效日期
     */
//	@DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @Column(name = "VALID_DATE")
    private Date validDate;
    /**
     * 有效日期-开始
     */
    @Transient
    private String validDateFrom;
    /**
     * 有效日期-结束
     */
    @Transient
    private String validDateTo;
    /**
     * 主管海关
     */
    @Column(name = "MASTER_CUSTOMS")
    private String masterCustoms;
    /**
     * 申报地海关
     */
    @Column(name = "declare_customs")
    private String declareCustoms;
    /**
     * 联系人
     */
    @Column(name = "LINK_MAN")
    private String linkMan;
    /**
     * 联系电话
     */
    @Column(name = "LINK_MAN_TEL")
    private String linkManTel;
    /**
     * 税款担保原因
     */
    @Column(name = "TAX_ASSURE_REASON")
    private String taxAssureReason;
    /**
     * 供应商编码
     */
    @Column(name = "SUPPLIER_CODE")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @Column(name = "SUPPLIER_NAME")
    private String supplierName;
    /**
     * 申报单位编码
     */
    @Column(name = "DECLARE_CODE")
    private String declareCode;
    /**
     * 申报单位名称
     */
    @Column(name = "DECLARE_NAME")
    private String declareName;
    /**
     * 是否有效，0 否 1是
     */
    @Column(name = "IS_ENABLED")
    private String isEnabled;
    /**
     * 报关单号
     */
    @Column(name = "ENTRY_NO")
    private String entryNo;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 发送状态，0 未发送 4 已发送报文 2 已生成预录入单
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 提单内部编号
     */
    @Column(name = "ERP_EMS_LIST_NO")
    private String erpEmsListNo;
    /**
     * 提单模板ID
     */
    @Column(name = "TEMPLATE_ID")
    private String templateId;
    /**
     * 担保编号
     */
    @Column(name = "ASSURE_NO")
    private String assureNo;
    /**
     * 数据来源 0大提单 1 小提单
     */
    @Transient
    private String dataSource;
    /**
     * 免税申请表 经营单位/代码
     */
    @Transient
    private String businessUnits;

    @Column(name = "TMP_NO")
    private String tmpNo;
    @Column(name = "SEQ_NO")
    private String seqNo;
    @Column(name = "M_TYPE")
    private String mType;
    @Column(name = "PROJECT_ID")
    private String projectId;
    @Column(name = "APPLY_CO_SCC")
    private String applyCoScc;
    @Column(name = "apply_code")
    private String applyCode;
    @Column(name = "apply_name")
    private String applyName;
    @Column(name = "COM_CO_SCC")
    private String comCoScc;
    @Column(name = "com_code")
    private String comCode;
    @Column(name = "com_name")
    private String comName;
    @Column(name = "HAS_SPEC_FILE")
    private String hasSpecFile;
    @Column(name = "CONTACT_CELLPHONE")
    private String contactCellphone;
    @Column(name = "BUSINESS_TYPE")
    private String businessType;
    @Transient
    private String businessTypeName;
    @Column(name = "APPL_TYPE")
    private String applType;
    /**
     * 受托单位信用代码
     */
    @Column(name = "ENTRUSTED_CO_SCC")
    private String entrustedCoScc;
    @Column(name = "PROJECT_FUND")
    private String projectFund;
    @Column(name = "RECEIPT_STATUS")
    private String receiptStatus;

    /**
     * 发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "DECLARE_DATE")
    private Date declareDate;
    /**
     * 发送时间-开始
     */
    @Transient
    private String declareDateFrom;
    /**
     * 发送时间-结束
     */
    @Transient
    private String declareDateTo;
    /**
     * 发送人
     */
    @Column(name = "SEND_USER")
    private String sendUser;
    /**
     * 发送人
     */
    @Column(name = "SEND_USER_NAME")
    private String sendUserName;

    /**
     * 审核状态
     */
    @Column(name = "APPR_STATUS")
    private String apprStatus;

    @Transient
    private String apprStatusName;

    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "APPR_DATE")
    private Date apprDate;
    /**
     * 审核时间-开始
     */
    @Transient
    private String apprDateFrom;
    /**
     * 审核时间-结束
     */
    @Transient
    private String apprDateTo;
    /**
     * 内审员
     */
    @Column(name = "APPR_USER")
    private String apprUser;
    /**
     * 内审员
     */
    @Column(name = "APPR_USER_NAME")
    private String apprUserName;

    @Transient
    private String userNo;
    /**
     * 申请人所在地
     */
    @Column(name = "apply_address")
    private String applyAddress;
    /**
     * 企业种类
     */
    @Column(name = "enterprise_type")
    private String enterpriseType;
    @Transient
    private String enterpriseTypeName;
    /**
     * 使用地点
     */
    @Column(name = "use_address")
    private String useAddress;
    /**
     * 报关状态
     */
    @Column(name = "entry_status")
    private String entryStatus;
    /**
     * 报关单统一编号
     */
    @Column(name = "entry_seq_no")
    private String entrySeqNo;
    /**
     * 概要发送日期
     */
    @Column(name = "entry_declare_date")
    private Date entryDeclareDate;
    /**
     * 运输方式
     */
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 运输工具名称
     */
    @Column(name = "traf_name")
    private String trafName;
    /**
     * 航次号
     */
    @Column(name = "voyage_no")
    private String voyageNo;
    /**
     * 提运单号
     */
    @Column(name = "hwab")
    private String hwab;
    /**
     * 监管方式
     */
    @Column(name = "trade_mode")
    private String tradeMode;
    /**
     * 毛重
     */
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * 是否涉证
     */
    @Column(name = "card_mark")
    private String cardMark;
    /**
     * 是否涉检
     */
    @Column(name = "check_mark")
    private String checkMark;
    /**
     * 是否涉税
     */
    @Column(name = "tax_mark")
    private String taxMark;
    /**
     * 报关单申报地海关
     */
    @Column(name = "entry_customs")
    private String entryCustoms;
    /**
     * 是否申报完成 0 未申报 1 概要申报失败 2 概要申报成功 3 完整申报成功
     */
    @Column(name = "IS_DECLARE_SUCCESS")
    private String isDeclareSuccess;
    /**
     * 报关单申报单位代码
     */
    @Column(name = "ENTRY_DECLARE_CODE")
    private String entryDeclareCode;
    /**
     * 报关单申报单位名称
     */
    @Column(name = "ENTRY_DECLARE_NAME")
    private String entryDeclareName;
    /**
     * 报关单申报单位社会信用代码
     */
    @Column(name = "ENTRY_DECLARE_SCC")
    private String entryDeclareScc;

    @Transient
    private String insertTimeZh;
    @Transient
    private String seqNoStr;
    /**
     * 目的地海关
     */
    @Column(name = "DESTINATION_CUSTOMS")
    private String destinationCustoms;

}
