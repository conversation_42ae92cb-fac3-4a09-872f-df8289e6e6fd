package com.dcjet.cs.dev.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-10-8
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEV_ITEM_CONFIRMATION")
public class DevItemConfirmation extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 项目确认书编号
     */
	@Column(name = "ITEM_NO")
	private  String itemNo;
	/**
     * 申请日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "APPLY_DATE")
	private  Date applyDate;
	/**
     * 申请日期-开始
     */
	@Transient
	private String applyDateFrom;
	/**
     * 申请日期-结束
     */
	@Transient
    private String applyDateTo;
	/**
     * 申请额度
     */
	@Column(name = "APPLY_AMOUT")
	private  BigDecimal applyAmout;
	/**
     * 币制
     */
	@Column(name = "CURR")
	private  String curr;
	/**
     * 已使用额度
     */
	@Column(name = "USED_AMOUT")
	private  BigDecimal usedAmout;
	/**
     * 剩余额度
     */
	@Column(name = "REMAIN_AMOUT")
	private  BigDecimal remainAmout;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;

	/**
	 * 是否有附件，Y:存在，N:不存在
	 */
	@Transient
	private String attached;

	/**
	 * 创建人
	 */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
	 * 创建时间
	 */
	@Column(name = "INSERT_TIME")
	private Date insertTime;
	/**
	 * 更新人
	 */
	@Column(name = "UPDATE_USER")
	private  String updateUser;

	/**
	 * 更新时间
	 */
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
}
