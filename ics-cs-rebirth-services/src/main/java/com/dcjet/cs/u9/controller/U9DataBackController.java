package com.dcjet.cs.u9.controller;

import com.dcjet.cs.u9.dto.U9DataBackResponseDto;
import com.dcjet.cs.u9.service.U9DataBackService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * U9数据回传控制器
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/u9/databack")
@Api(tags = "U9数据回传接口")
public class U9DataBackController extends BaseController {
    
    @Resource
    private U9DataBackService u9DataBackService;
    
    /**
     * 发送U9数据回传
     * @param reportType 报告类型 (进口报关/出口报关/内销报关)
     * @param delcareNo 报关单号
     * @param userInfo 用户信息
     * @return 响应结果
     */
    @PostMapping("/send")
    @ApiOperation("发送U9数据回传")
    public ResultObject<U9DataBackResponseDto> sendU9DataBack(
            @ApiParam(value = "报告类型", required = true, example = "进口报关") 
            @RequestParam String reportType,
            @ApiParam(value = "报关单号", required = true, example = "123456789012345678") 
            @RequestParam String delcareNo,
            UserInfoToken userInfo) {
        
        // 参数校验
        if (StringUtils.isBlank(reportType)) {
            return failure("报告类型不能为空");
        }
        
        if (StringUtils.isBlank(delcareNo)) {
            return failure("报关单号不能为空");
        }
        
        if (!isValidReportType(reportType)) {
            return failure("报告类型不正确，支持的类型：进口报关、出口报关、内销报关");
        }
        
        String tradeCode = userInfo.getCompany();
        if (StringUtils.isBlank(tradeCode)) {
            return failure("企业编码不能为空");
        }
        
        try {
            U9DataBackResponseDto response = u9DataBackService.sendU9DataBack(tradeCode, reportType, delcareNo);
            
            if (response.getIsSuccess() != null && response.getIsSuccess()) {
                return success(response);
            } else {
                return failure(response.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("发送U9数据回传异常", e);
            return failure("系统异常：" + e.getMessage());
        }
    }
    
    /**
     * 批量发送U9数据回传
     * @param reportType 报告类型
     * @param delcareNos 报关单号列表，用逗号分隔
     * @param userInfo 用户信息
     * @return 响应结果
     */
    @PostMapping("/batchSend")
    @ApiOperation("批量发送U9数据回传")
    public ResultObject<String> batchSendU9DataBack(
            @ApiParam(value = "报告类型", required = true, example = "进口报关") 
            @RequestParam String reportType,
            @ApiParam(value = "报关单号列表，用逗号分隔", required = true, example = "123456789012345678,123456789012345679") 
            @RequestParam String delcareNos,
            UserInfoToken userInfo) {
        
        // 参数校验
        if (StringUtils.isBlank(reportType)) {
            return failure("报告类型不能为空");
        }
        
        if (StringUtils.isBlank(delcareNos)) {
            return failure("报关单号不能为空");
        }
        
        if (!isValidReportType(reportType)) {
            return failure("报告类型不正确，支持的类型：进口报关、出口报关、内销报关");
        }
        
        String tradeCode = userInfo.getCompany();
        if (StringUtils.isBlank(tradeCode)) {
            return failure("企业编码不能为空");
        }
        
        try {
            String[] delcareNoArray = delcareNos.split(",");
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();
            
            for (String delcareNo : delcareNoArray) {
                delcareNo = delcareNo.trim();
                if (StringUtils.isBlank(delcareNo)) {
                    continue;
                }
                
                try {
                    U9DataBackResponseDto response = u9DataBackService.sendU9DataBack(tradeCode, reportType, delcareNo);
                    
                    if (response.getIsSuccess() != null && response.getIsSuccess()) {
                        successCount++;
                    } else {
                        failCount++;
                        errorMessages.append("报关单号[").append(delcareNo).append("]失败：").append(response.getMessage()).append("; ");
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("报关单号[").append(delcareNo).append("]异常：").append(e.getMessage()).append("; ");
                }
            }
            
            String resultMessage = String.format("批量发送完成，成功：%d，失败：%d", successCount, failCount);
            if (errorMessages.length() > 0) {
                resultMessage += "，失败详情：" + errorMessages.toString();
            }
            
            if (failCount == 0) {
                return success(resultMessage);
            } else {
                return failure(resultMessage);
            }
            
        } catch (Exception e) {
            logger.error("批量发送U9数据回传异常", e);
            return failure("系统异常：" + e.getMessage());
        }
    }
    
    /**
     * 校验报告类型是否有效
     */
    private boolean isValidReportType(String reportType) {
        return "进口报关".equals(reportType) || "出口报关".equals(reportType) || "内销报关".equals(reportType);
    }
}
