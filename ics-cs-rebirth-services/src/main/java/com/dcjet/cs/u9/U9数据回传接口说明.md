# U9数据回传接口说明

## 功能概述

U9数据回传功能用于将关务报关单数据回传给U9系统。系统会根据报告类型和报关单号查询相关数据，然后调用外部U9接口进行数据回传。

## 接口配置

### 1. 数据库配置

需要在 `T_GWSTD_HTTP_CONFIG` 表中配置U9数据回传接口信息：

```sql
INSERT INTO T_GWSTD_HTTP_CONFIG (
    SID, 
    BASE_URL, 
    SERVICE_URL, 
    TOKEN, 
    TYPE, 
    NOTE, 
    TRADE_CODE, 
    INSERT_USER, 
    INSERT_TIME
) VALUES (
    'U9_DATA_BACK_CONFIG_001', 
    'http://u9-server:8080', 
    '/api/v1/customs/databack', 
    '', 
    'U9_DATA_BACK', 
    'U9数据回传接口配置', 
    '9999999999', 
    'SYSTEM', 
    SYSDATE
);
```

### 2. 配置说明

- `BASE_URL`: U9系统的基础URL
- `SERVICE_URL`: 数据回传接口的相对路径
- `TYPE`: 固定为 `U9_DATA_BACK`
- `TOKEN`: 如果需要认证，可以配置token

## 接口使用

### 1. 单个报关单回传

**接口地址**: `POST /u9/databack/send`

**请求参数**:
- `reportType`: 报告类型（进口报关/出口报关/内销报关）
- `delcareNo`: 报关单号

**示例**:
```bash
curl -X POST "http://localhost:8080/u9/databack/send" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer your-token" \
  -d "reportType=进口报关&delcareNo=123456789012345678"
```

### 2. 批量报关单回传

**接口地址**: `POST /u9/databack/batchSend`

**请求参数**:
- `reportType`: 报告类型（进口报关/出口报关/内销报关）
- `delcareNos`: 报关单号列表，用逗号分隔

**示例**:
```bash
curl -X POST "http://localhost:8080/u9/databack/batchSend" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer your-token" \
  -d "reportType=进口报关&delcareNos=123456789012345678,123456789012345679"
```

## 数据格式

### 发送给U9的数据格式

```json
{
  "items": [
    {
      "reportType": "进口报关",
      "delcareNo": "123456789012345678",
      "reportDate": "2025-08-10T00:00:00+08:00",
      "customerCode": "CUSTOMER001",
      "supplierCode": "SUPPLIER001",
      "itemCode": "ITEM001",
      "lot": "LOT001",
      "reportQty": 100.00,
      "rcCode": "USD",
      "reportPriceRC": 10.50,
      "reportMoneyRC": 1050.00,
      "reportWaferMoneyRC": 800.00,
      "reportProcessMoneyRC": 250.00,
      "exchangeRate": 6.8,
      "reportPriceFC": 71.40,
      "reportMoneyFC": 7140.00,
      "reportWaferMoneyFC": 5440.00,
      "reportProcessMoneyFC": 1700.00,
      "erpDocNo": "SM202508100001",
      "erpDocLineNo": "10",
      "adDocNo": "AD202508100001",
      "erpShipQty": 100.00,
      "erpShipReportMoneyRC": 1050.00,
      "erpShipReportMoneyFC": 7140.00,
      "memo": "备注信息"
    }
  ]
}
```

### U9返回的数据格式

```json
{
  "message": "数据回传成功",
  "isSuccess": true
}
```

## 数据查询逻辑

系统会根据报告类型查询不同的数据：

### 1. 进口报关数据
- 查询条件：`IE_MARK = 'I'`
- 主要字段：供应商编码、进口相关字段

### 2. 出口报关数据
- 查询条件：`IE_MARK = 'E'`
- 主要字段：客户编码、出口相关字段、ERP出货单信息

### 3. 内销报关数据
- 查询条件：`IE_MARK = 'D'`
- 主要字段：客户编码、供应商编码、内销相关字段

## 错误处理

### 常见错误及解决方案

1. **未配置U9数据回传接口**
   - 错误信息：`未配置U9数据回传接口，请联系管理员`
   - 解决方案：在数据库中配置接口信息

2. **未查询到需要回传的数据**
   - 错误信息：`未查询到需要回传的数据`
   - 解决方案：检查报关单号是否正确，数据是否存在

3. **报告类型不正确**
   - 错误信息：`报告类型不正确，支持的类型：进口报关、出口报关、内销报关`
   - 解决方案：使用正确的报告类型

4. **U9接口调用失败**
   - 错误信息：根据U9返回的具体错误信息
   - 解决方案：检查网络连接、接口配置、数据格式等

## 日志监控

系统会记录详细的日志信息，包括：
- 请求参数
- 查询到的数据
- 发送给U9的数据
- U9的响应结果
- 异常信息

可以通过日志来排查问题和监控接口调用情况。

## 注意事项

1. 确保数据库中的报关单数据完整且状态正确
2. 配置正确的U9接口地址和认证信息
3. 注意数据格式的兼容性，特别是日期和数字格式
4. 建议先在测试环境验证功能正常后再在生产环境使用
5. 对于大批量数据回传，建议分批处理避免超时
