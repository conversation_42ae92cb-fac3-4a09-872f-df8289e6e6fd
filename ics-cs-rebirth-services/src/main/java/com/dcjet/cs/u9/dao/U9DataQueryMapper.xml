<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.u9.dao.U9DataQueryMapper">

    <!-- 查询进口报关数据 -->
    <select id="queryImportData" resultType="java.util.Map">
        SELECT 
            h.ENTRY_NO as DELCARE_NO,
            h.D_DATE as REPORT_DATE,
            h.SUPPLIER_CODE as SUPPLIER_CODE,
            l.COP_G_NO as ITEM_CODE,
            l.LOT_NO as LOT,
            l.QTY as REPORT_QTY,
            COALESCE(h.CURR, 'USD') as RC_CODE,
            l.DEC_PRICE as REPORT_PRICE_RC,
            l.DEC_TOTAL as REPORT_MONEY_RC,
            l.WAFER_MONEY as REPORT_WAFER_MONEY_RC,
            l.PROCESS_MONEY as REPORT_PROCESS_MONEY_RC,
            h.EXCHANGE_RATE as EXCHANGE_RATE,
            (l.DEC_PRICE * h.EXCHANGE_RATE) as REPORT_PRICE_FC,
            (l.DEC_TOTAL * h.EXCHANGE_RATE) as REPORT_MONEY_FC,
            (l.WAFER_MONEY * h.EXCHANGE_RATE) as REPORT_WAFER_MONEY_FC,
            (l.PROCESS_MONEY * h.EXCHANGE_RATE) as REPORT_PROCESS_MONEY_FC,
            l.ERP_DOC_NO as ERP_DOC_NO,
            l.ERP_DOC_LINE_NO as ERP_DOC_LINE_NO,
            l.AD_DOC_NO as AD_DOC_NO,
            l.ERP_SHIP_QTY as ERP_SHIP_QTY,
            (l.ERP_SHIP_QTY * l.DEC_PRICE) as ERP_SHIP_REPORT_MONEY_RC,
            (l.ERP_SHIP_QTY * l.DEC_PRICE * h.EXCHANGE_RATE) as ERP_SHIP_REPORT_MONEY_FC,
            h.NOTE as MEMO
        FROM T_DEC_ENTRY_HEAD h
        INNER JOIN T_DEC_ENTRY_LIST l ON h.SID = l.HEAD_SID
        WHERE h.TRADE_CODE = #{tradeCode}
          AND h.ENTRY_NO = #{delcareNo}
          AND h.IE_MARK = 'I'
          AND h.STATUS IN ('1', '2', '3')
        ORDER BY l.G_NO
    </select>

    <!-- 查询出口报关数据 -->
    <select id="queryExportData" resultType="java.util.Map">
        SELECT 
            h.ENTRY_NO as DELCARE_NO,
            h.D_DATE as REPORT_DATE,
            h.CUSTOMER_CODE as CUSTOMER_CODE,
            l.COP_G_NO as ITEM_CODE,
            l.LOT_NO as LOT,
            l.QTY as REPORT_QTY,
            COALESCE(h.CURR, 'USD') as RC_CODE,
            l.DEC_PRICE as REPORT_PRICE_RC,
            l.DEC_TOTAL as REPORT_MONEY_RC,
            l.WAFER_MONEY as REPORT_WAFER_MONEY_RC,
            l.PROCESS_MONEY as REPORT_PROCESS_MONEY_RC,
            h.EXCHANGE_RATE as EXCHANGE_RATE,
            (l.DEC_PRICE * h.EXCHANGE_RATE) as REPORT_PRICE_FC,
            (l.DEC_TOTAL * h.EXCHANGE_RATE) as REPORT_MONEY_FC,
            (l.WAFER_MONEY * h.EXCHANGE_RATE) as REPORT_WAFER_MONEY_FC,
            (l.PROCESS_MONEY * h.EXCHANGE_RATE) as REPORT_PROCESS_MONEY_FC,
            l.ERP_DOC_NO as ERP_DOC_NO,
            l.ERP_DOC_LINE_NO as ERP_DOC_LINE_NO,
            l.AD_DOC_NO as AD_DOC_NO,
            l.ERP_SHIP_QTY as ERP_SHIP_QTY,
            (l.ERP_SHIP_QTY * l.DEC_PRICE) as ERP_SHIP_REPORT_MONEY_RC,
            (l.ERP_SHIP_QTY * l.DEC_PRICE * h.EXCHANGE_RATE) as ERP_SHIP_REPORT_MONEY_FC,
            h.NOTE as MEMO
        FROM T_DEC_ENTRY_HEAD h
        INNER JOIN T_DEC_ENTRY_LIST l ON h.SID = l.HEAD_SID
        WHERE h.TRADE_CODE = #{tradeCode}
          AND h.ENTRY_NO = #{delcareNo}
          AND h.IE_MARK = 'E'
          AND h.STATUS IN ('1', '2', '3')
        ORDER BY l.G_NO
    </select>

    <!-- 查询内销报关数据 -->
    <select id="queryDomesticData" resultType="java.util.Map">
        SELECT 
            h.ENTRY_NO as DELCARE_NO,
            h.D_DATE as REPORT_DATE,
            h.CUSTOMER_CODE as CUSTOMER_CODE,
            h.SUPPLIER_CODE as SUPPLIER_CODE,
            l.COP_G_NO as ITEM_CODE,
            l.LOT_NO as LOT,
            l.QTY as REPORT_QTY,
            COALESCE(h.CURR, 'USD') as RC_CODE,
            l.DEC_PRICE as REPORT_PRICE_RC,
            l.DEC_TOTAL as REPORT_MONEY_RC,
            l.WAFER_MONEY as REPORT_WAFER_MONEY_RC,
            l.PROCESS_MONEY as REPORT_PROCESS_MONEY_RC,
            h.EXCHANGE_RATE as EXCHANGE_RATE,
            (l.DEC_PRICE * h.EXCHANGE_RATE) as REPORT_PRICE_FC,
            (l.DEC_TOTAL * h.EXCHANGE_RATE) as REPORT_MONEY_FC,
            (l.WAFER_MONEY * h.EXCHANGE_RATE) as REPORT_WAFER_MONEY_FC,
            (l.PROCESS_MONEY * h.EXCHANGE_RATE) as REPORT_PROCESS_MONEY_FC,
            l.ERP_DOC_NO as ERP_DOC_NO,
            l.ERP_DOC_LINE_NO as ERP_DOC_LINE_NO,
            l.AD_DOC_NO as AD_DOC_NO,
            l.ERP_SHIP_QTY as ERP_SHIP_QTY,
            (l.ERP_SHIP_QTY * l.DEC_PRICE) as ERP_SHIP_REPORT_MONEY_RC,
            (l.ERP_SHIP_QTY * l.DEC_PRICE * h.EXCHANGE_RATE) as ERP_SHIP_REPORT_MONEY_FC,
            h.NOTE as MEMO
        FROM T_DEC_ENTRY_HEAD h
        INNER JOIN T_DEC_ENTRY_LIST l ON h.SID = l.HEAD_SID
        WHERE h.TRADE_CODE = #{tradeCode}
          AND h.ENTRY_NO = #{delcareNo}
          AND h.IE_MARK = 'D'
          AND h.STATUS IN ('1', '2', '3')
        ORDER BY l.G_NO
    </select>

</mapper>
