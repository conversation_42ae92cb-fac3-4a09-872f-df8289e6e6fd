package com.dcjet.cs.u9.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * U9数据回传请求DTO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class U9DataBackRequestDto {
    
    /**
     * 申报明细行
     */
    private List<U9DataBackItemDto> items;
    
    @Data
    public static class U9DataBackItemDto {
        /**
         * 报告类型 - 进口报关/出口报关/内销报关
         */
        private String reportType;
        
        /**
         * 报关单号 - 单据号
         */
        private String delcareNo;
        
        /**
         * 申报日期 - 例：2025-08-10T00:00:00+08:00
         */
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
        private Date reportDate;
        
        /**
         * 客户编码 - 出口必填
         */
        private String customerCode;
        
        /**
         * 供应商编码 - 进口必填
         */
        private String supplierCode;
        
        /**
         * 报关料号 - 企业料号
         */
        private String itemCode;
        
        /**
         * 批次号 - 晶圆批次
         */
        private String lot;
        
        /**
         * 申报数量 - 进口必填
         */
        private BigDecimal reportQty;
        
        /**
         * 币种编码 - 默认USD
         */
        private String rcCode;
        
        /**
         * 申报单价(原币) - 关务报关单-美元单价
         */
        private BigDecimal reportPriceRC;
        
        /**
         * 申报总价(原币) - 关务报关单-美元金额
         */
        private BigDecimal reportMoneyRC;
        
        /**
         * 申报晶圆金额(原币) - 关务报关单晶圆-美元金额
         */
        private BigDecimal reportWaferMoneyRC;
        
        /**
         * 申报加工费金额(原币) - 关务报关单加工费-美元金额
         */
        private BigDecimal reportProcessMoneyRC;
        
        /**
         * 报关汇率 - 关务报关单-汇率
         */
        private BigDecimal exchangeRate;
        
        /**
         * 申报单价(本币) - 关务报关单-申报单价(原币)*报关汇率
         */
        private BigDecimal reportPriceFC;
        
        /**
         * 申报总价(本币) - 关务报关单-申报总价(原币)*报关汇率
         */
        private BigDecimal reportMoneyFC;
        
        /**
         * 申报晶圆金额(本币) - 关务报关进口单-申报晶圆金额(原币)*报关汇率
         */
        private BigDecimal reportWaferMoneyFC;
        
        /**
         * 申报加工费金额(本币) - 关务报关出库单-申报加工费金额(原币)*报关汇率
         */
        private BigDecimal reportProcessMoneyFC;
        
        /**
         * 入库单号 - 来源关务报关单表体明细-U9出货单SM号 - 出口必填
         */
        private String erpDocNo;
        
        /**
         * 入库行号 - 来源关务报关单表体明细-U9出货单SM行号 - 出口必填
         */
        private String erpDocLineNo;
        
        /**
         * AD单号 - 来源关务报关单表体明细-U9出货单AD单号 - 出口必填
         */
        private String adDocNo;
        
        /**
         * ERP出库数量 - 来源关务报关单表体明细-U9出货单数量 - 出口必填
         */
        private BigDecimal erpShipQty;
        
        /**
         * ERP出库申报金额(原币) - ERP出库数量*申报单价(原币)
         */
        private BigDecimal erpShipReportMoneyRC;
        
        /**
         * ERP出库申报金额(本币) - ERP出库数量*申报单价(本币)
         */
        private BigDecimal erpShipReportMoneyFC;
        
        /**
         * 备注
         */
        private String memo;
    }
}
