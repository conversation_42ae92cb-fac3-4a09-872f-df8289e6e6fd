package com.dcjet.cs.u9.service;

import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.u9.dao.U9DataQueryMapper;
import com.dcjet.cs.u9.dto.U9DataBackRequestDto;
import com.dcjet.cs.u9.dto.U9DataBackResponseDto;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.OkHttpUtils;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * U9数据回传服务
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class U9DataBackService {

    @Resource
    private CommonService commonService;
    @Resource
    private U9DataQueryMapper u9DataQueryMapper;
    
    /**
     * 发送U9数据回传
     *
     * @param tradeCode 企业编码
     */
    public void sendU9DataBack(String tradeCode) {
        try {
            XxlJobLogger.log("开始发送U9数据回传，企业编码：{}", tradeCode);
            
            // 1. 获取接口配置
            GwstdHttpConfig httpConfig = getHttpConfig();
            
            // 2. 查询数据
            List<U9DataBackRequestDto.U9DataBackItemDto> items = queryU9Data(tradeCode);
            
            if (CollectionUtils.isEmpty(items)) {
                XxlJobLogger.log("未查询到需要回传的数据，企业编码：{}", tradeCode);
                U9DataBackResponseDto.failure("未查询到需要回传的数据");
                return;
            }
            
            // 3. 构建请求对象
            U9DataBackRequestDto request = new U9DataBackRequestDto();
            request.setItems(items);
            
            // 4. 发送请求
            String url = httpConfig.getBaseUrl() + httpConfig.getServiceUrl();
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
            String requestJson = jsonObjectMapper.toJson(request);
            
            XxlJobLogger.log("发送U9数据回传请求，URL：{}，请求数据：{}", url, requestJson);
            
            String responseJson = OkHttpUtils.httpPostJson(url, requestJson);
            
            XxlJobLogger.log("U9数据回传响应：{}", responseJson);
            
            // 5. 解析响应
            U9DataBackResponseDto response = jsonObjectMapper.fromJson(responseJson, U9DataBackResponseDto.class);
            
            if (response == null) {
                XxlJobLogger.log("U9数据回传响应解析失败，响应内容：{}", responseJson);
                U9DataBackResponseDto.failure("响应解析失败");
                return;
            }
            
            if (response.getIsSuccess() == null || !response.getIsSuccess()) {
                XxlJobLogger.log("U9数据回传失败，响应：{}", responseJson);
                U9DataBackResponseDto.failure(response.getMessage());
                return;
            }
            
            XxlJobLogger.log("U9数据回传成功，响应：{}", responseJson);

        } catch (Exception e) {
            XxlJobLogger.log("U9数据回传异常，企业编码：{}", tradeCode,e);
            U9DataBackResponseDto.failure("系统异常：" + e.getMessage());
        }
    }
    
    /**
     * 获取HTTP配置
     */
    private GwstdHttpConfig getHttpConfig() {
        try {
            return commonService.getHttpConfigInfo(Constants.U9_DATA_BACK_TYPE);
        } catch (Exception e) {
            XxlJobLogger.log("获取U9数据回传接口配置失败", e);
            throw new ErrorException(400, "未配置U9数据回传接口，请联系管理员");
        }
    }
    
    /**
     * 查询U9数据
     */
    private List<U9DataBackRequestDto.U9DataBackItemDto> queryU9Data(String tradeCode) {
        try {
            // 根据报告类型查询不同的数据
            List<Map<String, Object>> dataList;

            dataList = u9DataQueryMapper.getU9BackData(tradeCode);

            
            if (CollectionUtils.isEmpty(dataList)) {
                return new ArrayList<>();
            }
            
            // 转换为DTO
            List<U9DataBackRequestDto.U9DataBackItemDto> items = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                U9DataBackRequestDto.U9DataBackItemDto item = convertToDto(data);
                items.add(item);
            }
            
            return items;
            
        } catch (Exception e) {
            XxlJobLogger.log("查询U9数据失败，企业编码：{}", tradeCode,  e);
            throw new ErrorException(500, "查询数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 将查询结果转换为DTO
     */
    private U9DataBackRequestDto.U9DataBackItemDto convertToDto(Map<String, Object> data) {
        U9DataBackRequestDto.U9DataBackItemDto item = new U9DataBackRequestDto.U9DataBackItemDto();
        
        item.setReportType(getString(data, "REPORT_TYPE"));
        item.setDelcareNo(getString(data, "DELCARE_NO"));
        item.setReportDate(getDate(data, "REPORT_DATE"));
        item.setCustomerCode(getString(data, "CUSTOMER_CODE"));
        item.setSupplierCode(getString(data, "SUPPLIER_CODE"));
        item.setItemCode(getString(data, "ITEM_CODE"));
        item.setLot(getString(data, "LOT"));
        item.setReportQty(getBigDecimal(data, "REPORT_QTY"));
        item.setRcCode(getString(data, "RC_CODE", "USD")); // 默认USD
        item.setReportPriceRC(getBigDecimal(data, "REPORT_PRICE_RC"));
        item.setReportMoneyRC(getBigDecimal(data, "REPORT_MONEY_RC"));
        item.setReportWaferMoneyRC(getBigDecimal(data, "REPORT_WAFER_MONEY_RC"));
        item.setReportProcessMoneyRC(getBigDecimal(data, "REPORT_PROCESS_MONEY_RC"));
        item.setExchangeRate(getBigDecimal(data, "EXCHANGE_RATE"));
        item.setReportPriceFC(getBigDecimal(data, "REPORT_PRICE_FC"));
        item.setReportMoneyFC(getBigDecimal(data, "REPORT_MONEY_FC"));
        item.setReportWaferMoneyFC(getBigDecimal(data, "REPORT_WAFER_MONEY_FC"));
        item.setReportProcessMoneyFC(getBigDecimal(data, "REPORT_PROCESS_MONEY_FC"));
        item.setErpDocNo(getString(data, "ERP_DOC_NO"));
        item.setErpDocLineNo(getString(data, "ERP_DOC_LINE_NO"));
        item.setAdDocNo(getString(data, "AD_DOC_NO"));
        item.setErpShipQty(getBigDecimal(data, "ERP_SHIP_QTY"));
        item.setErpShipReportMoneyRC(getBigDecimal(data, "ERP_SHIP_REPORT_MONEY_RC"));
        item.setErpShipReportMoneyFC(getBigDecimal(data, "ERP_SHIP_REPORT_MONEY_FC"));
        item.setMemo(getString(data, "MEMO"));
        
        return item;
    }
    
    private String getString(Map<String, Object> data, String key) {
        return getString(data, key, null);
    }
    
    private String getString(Map<String, Object> data, String key, String defaultValue) {
        Object value = data.get(key);
        if (value == null) {
            return defaultValue;
        }
        return value.toString();
    }
    
    private java.util.Date getDate(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof java.util.Date) {
            return (java.util.Date) value;
        }
        if (value instanceof java.sql.Date) {
            return new java.util.Date(((java.sql.Date) value).getTime());
        }
        if (value instanceof java.sql.Timestamp) {
            return new java.util.Date(((java.sql.Timestamp) value).getTime());
        }
        return null;
    }
    
    private BigDecimal getBigDecimal(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
}
