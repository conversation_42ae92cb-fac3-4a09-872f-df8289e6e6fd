package com.dcjet.cs.u9.service;

import com.dcjet.cs.u9.dao.U9DataQueryMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * U9数据查询服务
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class U9DataQueryService {
    
    @Resource
    private U9DataQueryMapper u9DataQueryMapper;
    
    /**
     * 查询进口报关数据
     * @param tradeCode 企业编码
     * @param delcareNo 报关单号
     * @return 查询结果
     */
    public List<Map<String, Object>> queryImportData(String tradeCode, String delcareNo) {
        return u9DataQueryMapper.queryImportData(tradeCode, delcareNo);
    }
    
    /**
     * 查询出口报关数据
     * @param tradeCode 企业编码
     * @param delcareNo 报关单号
     * @return 查询结果
     */
    public List<Map<String, Object>> queryExportData(String tradeCode, String delcareNo) {
        return u9DataQueryMapper.queryExportData(tradeCode, delcareNo);
    }
    
    /**
     * 查询内销报关数据
     * @param tradeCode 企业编码
     * @param delcareNo 报关单号
     * @return 查询结果
     */
    public List<Map<String, Object>> queryDomesticData(String tradeCode, String delcareNo) {
        return u9DataQueryMapper.queryDomesticData(tradeCode, delcareNo);
    }
}
