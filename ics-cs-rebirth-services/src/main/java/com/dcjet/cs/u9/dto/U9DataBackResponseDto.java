package com.dcjet.cs.u9.dto;

import lombok.Data;

/**
 * U9数据回传响应DTO
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class U9DataBackResponseDto {
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 是否成功
     */
    private Boolean isSuccess;
    
    /**
     * 构造成功响应
     */
    public static U9DataBackResponseDto success(String message) {
        U9DataBackResponseDto response = new U9DataBackResponseDto();
        response.setIsSuccess(true);
        response.setMessage(message);
        return response;
    }
    
    /**
     * 构造失败响应
     */
    public static U9DataBackResponseDto failure(String message) {
        U9DataBackResponseDto response = new U9DataBackResponseDto();
        response.setIsSuccess(false);
        response.setMessage(message);
        return response;
    }
}
