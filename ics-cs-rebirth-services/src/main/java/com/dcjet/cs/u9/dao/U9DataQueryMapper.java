package com.dcjet.cs.u9.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * U9数据查询Mapper
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface U9DataQueryMapper {
    
    /**
     * 查询进口报关数据
     * @param tradeCode 企业编码
     * @param delcareNo 报关单号
     * @return 查询结果
     */
    List<Map<String, Object>> queryImportData(@Param("tradeCode") String tradeCode, @Param("delcareNo") String delcareNo);
    
    /**
     * 查询出口报关数据
     * @param tradeCode 企业编码
     * @param delcareNo 报关单号
     * @return 查询结果
     */
    List<Map<String, Object>> queryExportData(@Param("tradeCode") String tradeCode, @Param("delcareNo") String delcareNo);
    
    /**
     * 查询内销报关数据
     * @param tradeCode 企业编码
     * @param delcareNo 报关单号
     * @return 查询结果
     */
    List<Map<String, Object>> queryDomesticData(@Param("tradeCode") String tradeCode, @Param("delcareNo") String delcareNo);
}
