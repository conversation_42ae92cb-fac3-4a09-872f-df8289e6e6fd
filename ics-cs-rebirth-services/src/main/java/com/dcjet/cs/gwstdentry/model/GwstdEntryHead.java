package com.dcjet.cs.gwstdentry.model;

import com.dcjet.cs.entCompare.model.Compare;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_GWSTD_ENTRY_HEAD")
public class GwstdEntryHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * OID
     */
	@Id
	@Column(name = "SID")
	private String sid;
	/**
     * 申报单位社会信用代码
     */
	@Column(name = "AGENT_CODE_SCC")
	@JsonProperty("AGENT_CODE_SCC")
	private String agentCodeScc;
	/**
     * 申报单位（10位海关代码）
     */
	@Column(name = "AGENT_CODE")
	@JsonProperty("AGENT_CODE")
	@Compare(column = "agentCode",columnName = "申报单位")
	private String agentCode;
	/**
     * 申报单位名称
     */
	@Column(name = "AGENT_NAME")
	@JsonProperty("AGENT_NAME")
	private String agentName;
	/**
     * 随附单证
     */
	@Column(name = "CERT_MARK")
	@JsonProperty("CERT_MARK")
	private String certMark;
	/**
     * 备案清单类型
     */
	@Column(name = "BILL_TYPE")
	@JsonProperty("BILL_TYPE")
	private String billType;
	/**
     * 提运单号码
     */
	@Column(name = "BILL_NO")
	@JsonProperty("BILL_NO")
	private String billNo;
	/**
     * 保税仓库或者监管仓库编号
     */
	@Column(name = "BONDED_NO")
	@JsonProperty("BONDED_NO")
	private String bondedNo;
	/**
     * 境内收发货人编码
     */
	@Column(name = "TRADE_CODE")
	@JsonProperty("TRADE_CODE")
	private String tradeCode;
	/**
     * 境内收发货人社会信用代码
     */
	@Column(name = "TRADE_CODE_SCC")
	@JsonProperty("TRADE_CODE_SCC")
	private String tradeCodeScc;
	/**
     * 境内收发货人名称
     */
	@Column(name = "TRADE_NAME")
	@JsonProperty("TRADE_NAME")
	private String tradeName;
	/**
     * 境外收发货人编码
     */
	@Column(name = "OVERSEAS_TRADE_CODE")
	@JsonProperty("OVERSEAS_TRADE_CODE")
	private String overseasTradeCode;
	/**
     * 境外收发货人-企业名称
     */
	@Column(name = "OVERSEAS_TRADE_NAME")
	@JsonProperty("OVERSEASCONSIGNOR_NAME")
	private String overseasconsignorName;
	/**
     * 集装箱号
     */
	@Column(name = "CONTAINER_NO")
	@JsonProperty("CONTAINER_NO")
	private String containerNo;
	/**
     * 合同协议号
     */
	@Column(name = "CONTR_NO")
	@JsonProperty("CONTR_NO")
    @Compare(column = "contrNo",columnName = "合同协议号")
	private String contrNo;
	/**
     * 关联理由【关联报检号的关联理由】
     */
	@Column(name = "CORRELATION_REASON_FLAG")
	@JsonProperty("CORRELATION_REASON_FLAG")
	private String correlationReasonFlag;
	/**
     * 报关单申报状态编码
     */
	@Column(name = "CHK_STATUS")
	@JsonProperty("CHK_STATUS")
	private String chkStatus;
	/**
     * 码头/货场代码（为物流监控备用）
     */
	@Column(name = "CUSTOMS_FIELD")
	@JsonProperty("CUSTOMS_FIELD")
	private String customsField;
	/**
     * 征免性质编码
     */
	@Column(name = "CUT_MODE")
	@JsonProperty("CUT_MODE")
    @Compare(column = "cutMode",columnName = "征免性质编码")
	private String cutMode;
	/**
     * 发货日期
     */
	@JsonProperty("DESP_DATE")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "DESP_DATE")
	private Date despDate;
	/**
     * 发货日期-开始
     */
	@Transient
	private String despDateFrom;
	/**
     * 发货日期-结束
     */
	@Transient
    private String despDateTo;
	/**
     *  启运港(进口)/离境口岸(出口)
     */
	@Column(name = "DESP_PORT_CODE")
	@JsonProperty("DESP_PORT")
    @Compare(column = "despPort",columnName = "启运港")
	private String despPort;
	/**
     * 指运港/经停港(名称）
     */
	@Column(name = "DISTINATE_PORT")
	@JsonProperty("DEST_PORT")
    @Compare(column = "destPort",columnName = "指运港")
	private String destPort;
	/**
     * 申报日期
     */
	@JsonProperty("ENTRY_DECLARE_DATE")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "D_DATE")
	private Date entryDeclareDate;
	/**
     * 申报日期-开始
     */
	@Transient
	private String dDateFrom;
	/**
     * 申报日期-结束
     */
	@Transient
    private String dDateTo;
	/**
     * 海关编号
     */
	@Column(name = "ENTRY_ID")
	@JsonProperty("ENTRY_NO")
	private String entryNo;
	/**
     * 报关单类型
     */
	@Column(name = "ENTRY_TYPE")
	@JsonProperty("ENTRY_TYPE")
	private String entryType;
	/**
     * 运费币制
     */
	@Column(name = "FEE_CURR")
	@JsonProperty("FEE_CURR")
    @Compare(column = "feeCurr",columnName = "运费币制")
	private String feeCurr;
	/**
     * 运费标记
     */
	@Column(name = "FEE_MARK")
	@JsonProperty("FEE_MARK")
    @Compare(column = "feeMark",columnName = "运费标记")
	private String feeMark;
	/**
     * 运费／率
     */
	@Column(name = "FEE_RATE")
	@JsonProperty("FEE_RATE")
    @Compare(column = "feeRate",columnName = "运费／率")
	private BigDecimal feeRate;
	/**
     * 货物存放地点
     */
	@Column(name = "GOODSPLACE")
	@JsonProperty("GOODSPLACE")
	private String goodsplace;
	/**
     * 毛重
     */
	@Column(name = "GROSS_WT")
	@JsonProperty("GROSS_WT")
    @Compare(column = "grossWt",columnName = "毛重")
	private BigDecimal grossWt;
	/**
     * 口岸检验检疫机关企业编码
     */
	@Column(name = "INSPORG_CODE")
	@JsonProperty("INSPORG_CODE")
	private String insporgCode;
	/**
     * 保险费标记
     */
	@Column(name = "INSUR_MARK")
	@JsonProperty("INSUR_MARK")
    @Compare(column = "insurMark",columnName = "保险费标记")
	private String insurMark;
	/**
     * 保险费／率
     */
	@Column(name = "INSUR_RATE")
	@JsonProperty("INSUR_RATE")
    @Compare(column = "insurRate",columnName = "保险费／率")
	private BigDecimal insurRate;
	/**
     * 进出口标志
     */
	@Column(name = "I_E_MARK")
	@JsonProperty("I_E_MARK")
	private String iEMark;
	/**
     * 进出口岸代码（进境关别（出境关别））
     */
	@Column(name = "I_E_PORT")
	@JsonProperty("I_E_PORT")
    @Compare(column = "IEPort",columnName = "进出口岸代码")
	private String iEPort;
	/**
     * 进出口日期
     */
	@JsonProperty("I_E_DATE")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "I_E_DATE")
	private Date iEDate;
	/**
     * 进出口日期-开始
     */
	@Transient
	private String iEDateFrom;
	/**
     * 进出口日期-结束
     */
	@Transient
    private String iEDateTo;
	/**
     * 手册号码（备案号）
     */
	@Column(name = "MANUAL_NO")
	@JsonProperty("EMS_NO")
    @Compare(column = "emsNo",columnName = "手册号码")
	private String emsNo;
	/**
     * 标记唛码
     */
	@Column(name = "MARK_NO")
	@JsonProperty("MARK_NO")
	private String markNo;
	/**
     * 申报地海关
     */
	@Column(name = "MASTER_CUSTOMS")
	@JsonProperty("MASTER_CUSTOMS")
    @Compare(column = "masterCustoms",columnName = "申报地海关")
	private String masterCustoms;
	/**
     * 净重
     */
	@Column(name = "NET_WT")
	@JsonProperty("NET_WT")
    @Compare(column = "netWt",columnName = "净重")
	private BigDecimal netWt;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	@JsonProperty("NOTE")
	private String note;
	/**
     * 检验检疫受理机关编码
     */
	@Column(name = "ORG_CODE")
	@JsonProperty("ORG_CODE")
	private String orgCode;
	/**
     * 杂费标记
     */
	@Column(name = "OTHER_MARK")
	@JsonProperty("OTHER_MARK")
    @Compare(column = "otherMark",columnName = "杂费标记")
	private String otherMark;
	/**
     * 杂费币制
     */
	@Column(name = "OTHER_CURR")
	@JsonProperty("OTHER_CURR")
    @Compare(column = "otherCurr",columnName = "杂费币制")
	private String otherCurr;
	/**
     * 杂费／率
     */
	@Column(name = "OTHER_RATE")
	@JsonProperty("OTHER_RATE")
    @Compare(column = "otherRate",columnName = "杂费／率")
	private BigDecimal otherRate;
	/**
     * 消费使用单位(10位检验检疫编码)--进口/生产销售单位-10位检验检疫编码--出口
     */
	@Column(name = "OWNERCIQ_CODE")
	@JsonProperty("OWNERCIQ_CODE")
	private String ownerciqCode;
	/**
     * 货主单位/消费使用单位(18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口
     */
	@Column(name = "OWNER_SCC")
	@JsonProperty("OWNER_SCC")
	private String ownerScc;
	/**
     * 货主单位代码（消费使用单位）
     */
	@Column(name = "OWNER_CODE")
	@JsonProperty("OWNER_CODE")
	private String ownerCode;
	/**
     * 货主单位名称（消费使用单位）
     */
	@Column(name = "OWNER_NAME")
	@JsonProperty("OWNER_NAME")
	private String ownerName;
	/**
     * 件数
     */
	@Column(name = "PACK_NO")
	@JsonProperty("PACK_NUM")
	@Compare(column = "packNum",columnName = "件数")
	private Integer packNum;
	/**
     * 其他事项确认（特殊关系确认、价格影响确认、与货物有关的特许权使用费支付确认）
     */
	@Column(name = "PROMISE_ITEMS")
	@JsonProperty("PROMISE_ITEMS")
	private String promiseItems;
	/**
     * 目的地检验检疫机关企业编码
     */
	@Column(name = "PURPORG_CODE")
	@JsonProperty("PURPORG_CODE")
	private String purporgCode;
	/**
     * 关联编号字段（转出的手册、转入、转出的报关单）
     */
	@Column(name = "RELATIVE_ID")
	@JsonProperty("REL_ENTRY_NO")
	private String relEntryNo;
	/**
     * 关联备案号
     */
	@Column(name = "REL_MANUAL_NO")
	@JsonProperty("REL_EMS_NO")
	private String relEmsNo;
	/**
     * 预录入号码
     */
	@Column(name = "SEQ_NO")
	@JsonProperty("SEQ_NO")
	private String seqNo;
	/**
     * 监管方式
     */
	@Column(name = "TRADE_MODE")
	@JsonProperty("TRADE_MODE")
    @Compare(column = "tradeMode",columnName = "监管方式")
	private String tradeMode;
	/**
     * 启运国/运抵国
     */
	@Column(name = "TRADE_COUNTRY")
	@JsonProperty("TRADE_COUNTRY")
    @Compare(column = "tradeCountry",columnName = "启运国/运抵国")
	private String tradeCountry;
	/**
     * 运输工具名称
     */
	@Column(name = "TRAF_NAME")
	@JsonProperty("TRAF_NAME")
	private String trafName;
	/**
     * 成交方式
     */
	@Column(name = "TRANS_MODE")
	@JsonProperty("TRANS_MODE")
    @Compare(column = "transMode",columnName = "成交方式")
	private String transMode;
	/**
     * 运输工具航次(班)号
     */
	@Column(name = "VOYAGE_NO")
	@JsonProperty("VOYAGE_NO")
	private String voyageNo;
	/**
     * 领证机关企业编码
     */
	@Column(name = "VSAORG_CODE")
	@JsonProperty("VSAORG_CODE")
	private String vsaorgCode;
	/**
     * 领证机关企业名称
     */
	@Column(name = "VSAORGCODE_NAME")
	@JsonProperty("VSAORGCODE_NAME")
	private String vsaorgcodeName;
	/**
     * 包装种类
     */
	@Column(name = "WRAP_TYPE")
	@JsonProperty("WRAP_TYPE")
    @Compare(column = "wrapType",columnName = "包装种类")
	private String wrapType;
	/**
     * 货主单位地区代码（境内目的地）
     */
	@Column(name = "DISTRICT_CODE")
	@JsonProperty("DISTRICT_CODE")
	private String districtCode;
	/**
     * 内销比率
     */
	@Column(name = "IN_RATIO")
	@JsonProperty("IN_RATIO")
	private BigDecimal inRatio;
	/**
     * 收结汇方式（出口用）
     */
	@Column(name = "PAY_WAY")
	@JsonProperty("PAY_WAY")
	private String payWay;
	/**
     * 许可证编号
     */
	@Column(name = "LICENSE_NO")
	@JsonProperty("LICENSE_NO")
    @Compare(column = "licenseNo",columnName = "许可证编号")
	private String licenseNo;
	/**
     * 结汇证号（批准文号）
     */
	@Column(name = "APPR_NO")
	@JsonProperty("APPR_NO")
	private String apprNo;
	/**
     * 申报口岸代码
     */
	@Column(name = "DECL_PORT")
	@JsonProperty("ENTRY_PORT")
	@Compare(column = "entryPortCode",columnName = "入境口岸")
	private String entryPort;
	/**
     * 经营单位性质
     */
	@Column(name = "CO_OWNER")
	@JsonProperty("CO_OWNER")
	private String coOwner;
	/**
     * 纳税义务人识别号
     */
	@Column(name = "TAXY_RG_NO")
	@JsonProperty("TAXY_RG_NO")
	private String taxyRgNo;
	/**
     * 监管手续费标志字段
     */
	@Column(name = "MNL_JGF_FLAG")
	@JsonProperty("MNL_JGF_FLAG")
	private String mnlJgfFlag;
	/**
     * 监管手续费
     */
	@Column(name = "SERVICE_FEE")
	@JsonProperty("SERVICE_FEE")
	private BigDecimal serviceFee;
	/**
     * 监管手续费率
     */
	@Column(name = "SERVICE_RATE")
	@JsonProperty("SERVICE_RATE")
	private BigDecimal serviceRate;
	/**
     * 报关员联系方式
     */
	@Column(name = "BP_NO")
	@JsonProperty("BP_NO")
	private String bpNo;
	/**
     * 制单人
     */
	@Column(name = "TYPIST_NO")
	@JsonProperty("TYPIST_NO")
	private String typistNo;
	/**
     * 录入人
     */
	@Column(name = "INPUT_NO")
	@JsonProperty("INPUT_NO")
	private String inputNo;
	/**
     * 报关单打印日期/时间
     */
	@JsonProperty("P_DATE")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "P_DATE")
	private Date pDate;
	/**
     * 报关单打印日期/时间-开始
     */
	@Transient
	private String pDateFrom;
	/**
     * 报关单打印日期/时间-结束
     */
	@Transient
    private String pDateTo;
	/**
     * 修改次数
     */
	@Column(name = "MODI_NO")
	@JsonProperty("MODI_NO")
	private Integer modiNo;
	/**
     * 运输方式代码
     */
	@Column(name = "TRAF_MODE")
	@JsonProperty("TRAF_MODE")
    @Compare(column = "trafMode",columnName = "运输方式代码")
	private String trafMode;
	/**
     * 申报单位10位检验检疫编码
     */
	@Column(name = "AGENT_REG_CODE")
	@JsonProperty("AGENT_REG_CODE")
	private String agentRegCode;
	/**
     * 贸易国别（地区）代码
     */
	@Column(name = "TRADE_AREA_CODE")
	@JsonProperty("TRADE_NATION")
    @Compare(column = "tradeNation",columnName = "贸易国别")
	private String tradeNation;
	/**
     * 保险费币制 CURR
     */
	@Column(name = "INSUR_CURR")
	@JsonProperty("INSUR_CURR")
    @Compare(column = "insurCurr",columnName = "保险费币制")
	private String insurCurr;
	/**
     * 插入时间
     */
	@JsonProperty("MY_INSERT_TIME")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "MY_INSERT_TIME")
	private Date myInsertTime;
	/**
     * 插入时间-开始
     */
	@Transient
	private String myInsertTimeFrom;
	/**
     * 插入时间-结束
     */
	@Transient
    private String myInsertTimeTo;
	/**
     * 更新时间
     */
	@JsonProperty("MY_UPDATE_TIME")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "MY_UPDATE_TIME")
	private Date myUpdateTime;
	/**
     * 更新时间-开始
     */
	@Transient
	private String myUpdateTimeFrom;
	/**
     * 更新时间-结束
     */
	@Transient
    private String myUpdateTimeTo;
	/**
     * 数据来源（0：固定 1：电子口岸 2：爬网 3：组合）
     */
	@Column(name = "DATA_SOURCE")
	@JsonProperty("DATA_SOURCE")
	private String dataSource;
	/**
     * B/L号（检验检疫专用）
     */
	@Column(name = "CIQ_BILL_NO")
	@JsonProperty("CIQ_BILL_NO")
	private String ciqBillNo;
	/**
     * 特殊关系确认
     */
	@Column(name = "CONFIRM_SPECIAL")
	@JsonProperty("CONFIRM_SPECIAL")
	@Compare(column = "confirmSpecial",columnName = "特殊关系确认")
	private String confirmSpecial;
	/**
     * 价格影响确认
     */
	@Column(name = "CONFIRM_PRICE")
	@JsonProperty("CONFIRM_PRICE")
	@Compare(column = "confirmPrice",columnName = "价格影响确认")
	private String confirmPrice;
	/**
     * 支付特许权使用费确认
     */
	@Column(name = "CONFIRM_ROYALTIES")
	@JsonProperty("CONFIRM_ROYALTIES")
	@Compare(column = "confirmRoyalties",columnName = "支付特许权使用费确认")
	private String confirmRoyalties;
	/**
	 * 公式定价确认
	 */
	@Column(name = "confirm_formula_price")
	@JsonProperty("CONFIRM_FORMULA_PRICE")
//	@Compare(column = "confirmFormulaPrice",columnName = "公式定价确认")
	private String confirmFormulaPrice;
	/**
	 * 暂时价格确认
	 */
	@Column(name = "confirm_temp_price")
	@JsonProperty("CONFIRM_TEMP_PRICE")
//	@Compare(column = "confirmTempPrice",columnName = "暂时价格确认")
	private String confirmTempPrice;
	/**
     * 业务事项-自报自缴(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）
     */
	@Column(name = "CUS_REMARK_AUTO")
	@JsonProperty("CUS_REMARK_AUTO")
	private String cusRemarkAuto;
	/**
     * 业务事项-水运中转(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）
     */
	@Column(name = "CUS_REMARK_WATER")
	@JsonProperty("CUS_REMARK_WATER")
	private String cusRemarkWater;
	/**
     * 申报方式标志
     */
	@Column(name = "EDI_ID")
	@JsonProperty("EDI_ID")
	private String ediId;
	/**
     * EDI申报备注
     */
	@Column(name = "EDI_REMARK")
	@JsonProperty("EDI_REMARK")
	private String ediRemark;
	/**
     * EDI申报人标识
     */
	@Column(name = "PARTNER_ID")
	@JsonProperty("PARTNER_ID")
	private String partnerId;
	/**
     * 入境口岸(进口专用)
     */
	@Column(name = "ENTRY_PORT_CODE")
	@JsonProperty("ENTRY_PORT_CODE")
	private String entryPortCode;
	/**
     * 报关人员证号
     */
	@Column(name = "DCLR_NO")
	@JsonProperty("DCLR_NO")
	private String dclrNo;
	/**
     * 境内收发货人检验检疫编码
     */
	@Column(name = "TRADE_CIQ_CODE")
	@JsonProperty("TRADE_CIQ_CODE")
	private String tradeCiqCode;
	/**
     * 关联号码
     */
	@Column(name = "CORRELATION_DECLNO")
	@JsonProperty("CORRELATION_DECLNO")
	private String correlationDeclno;
	/**
	 * 关务系统入库时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "INSERT_TIME")
	private Date insertTime;
	/**
	 * 锁定标记
	 */
	@Column(name = "LOCK_MARK")
	private String lockMark;
	/**
	 * 比对标记（0 未比对 1 比对中 2 比对成功 3 比对失败）
	 */
	@Column(name = "ENTRY_COMPARE_FLAG")
	private String entryCompareFlag;
	/**
	 * 比对日期
	 */
	@Column(name = "ENTRY_COMPARE_TIME")
	private Date entryCompareTime;
	@Transient
	private String entryDeclareDateFrom;
	@Transient
	private String entryDeclareDateTo;

	/**
	 * 未申报至H2K 1.已经申报至H2K
	 */
	@Column(name = "IS_DECLARE")
	private String isDeclare;

	/**
	 * 美元申报总价
	 */
	@Column(name = "DECL_TOTAL_USD")
	private BigDecimal declTotalUsd;

	@Transient
	private String emsListNo;
	@Transient
	private String mawb;
	@Transient
	private String hawb;
	@Transient
	private String overseasTradeName;

	/**
	 * 下载状态 0.附件待下载、1.下载成功、2.下载失败、3.无附件
	 */
	@Column(name = "att_download_flag")
	private String attDownloadFlag;
}
