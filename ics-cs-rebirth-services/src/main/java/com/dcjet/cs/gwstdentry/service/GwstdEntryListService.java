package com.dcjet.cs.gwstdentry.service;

import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadDto;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadParam;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryListDto;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryListParam;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryListMapper;
import com.dcjet.cs.gwstdentry.mapper.GwstdEntryListDtoMapper;
import com.dcjet.cs.gwstdentry.model.GwstdEntryList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-3-10
 */
@Service
public class GwstdEntryListService extends BaseService<GwstdEntryList> {
    @Resource
    private GwstdEntryListMapper gwstdEntryListMapper;
    @Resource
    private GwstdEntryListDtoMapper gwstdEntryListDtoMapper;

    @Override
    public Mapper<GwstdEntryList> getMapper() {
        return gwstdEntryListMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdEntryListParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdEntryListDto>> getListPaged(GwstdEntryListParam gwstdEntryListParam, PageParam pageParam) {
        // 启用分页查询
        GwstdEntryList gwstdEntryList = gwstdEntryListDtoMapper.toPo(gwstdEntryListParam);
        Page<GwstdEntryList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryListMapper.getList(gwstdEntryList));
        List<GwstdEntryListDto> gwstdEntryListDtos = page.getResult().stream().map(head -> {
            GwstdEntryListDto dto = gwstdEntryListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdEntryListDto>> paged = ResultObject.createInstance(gwstdEntryListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public void listDelete(String seqNo) {
        gwstdEntryListMapper.listDelete(seqNo);
    }

    /**
     * 获取税金管理数据
     *
     * @param gwstdEntryHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwstdEntryHeadDto>> getTaxManageList(GwstdEntryHeadParam gwstdEntryHeadParam, PageParam pageParam) {
        // 启用分页查询
        Page<GwstdEntryHeadDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryListMapper.getTaxManageList(gwstdEntryHeadParam));
        ResultObject<List<GwstdEntryHeadDto>> paged = ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdEntryHeadDto> selectAllTaxManageList(GwstdEntryHeadParam exportParam, UserInfoToken userInfo) {
        exportParam.setTradeCode(userInfo.getCompany());
        List<GwstdEntryHeadDto> entryHeadDtos = gwstdEntryListMapper.getTaxManageList(exportParam);
        return entryHeadDtos;
    }
}
