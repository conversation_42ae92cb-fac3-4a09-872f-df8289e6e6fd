package com.dcjet.cs.gwstdentry.service;

import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadLogDto;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadLogParam;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryHeadLogMapper;
import com.dcjet.cs.gwstdentry.mapper.GwstdEntryHeadLogDtoMapper;
import com.dcjet.cs.gwstdentry.model.GwstdEntryHeadLog;
import com.dcjet.cs.gwstdentry.model.GwstdEntryMessage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-3-10
 */
@Service
public class GwstdEntryHeadLogService extends BaseService<GwstdEntryHeadLog> {
    @Resource
    private GwstdEntryHeadLogMapper gwstdEntryHeadLogMapper;
    @Resource
    private GwstdEntryHeadLogDtoMapper gwstdEntryHeadLogDtoMapper;

    @Override
    public Mapper<GwstdEntryHeadLog> getMapper() {
        return gwstdEntryHeadLogMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdEntryHeadLogParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdEntryHeadLogDto>> getListPaged(GwstdEntryHeadLogParam gwstdEntryHeadLogParam, PageParam pageParam, UserInfoToken userInfoToken) {
        // 启用分页查询
        GwstdEntryHeadLog gwstdEntryHeadLog = gwstdEntryHeadLogDtoMapper.toPo(gwstdEntryHeadLogParam);
        gwstdEntryHeadLog.setTradeCode(userInfoToken.getCompany());
        Page<GwstdEntryHeadLog> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryHeadLogMapper.getList(gwstdEntryHeadLog));
        List<GwstdEntryHeadLogDto> gwstdEntryHeadLogDtos = page.getResult().stream().map(head -> {
            GwstdEntryHeadLogDto dto = gwstdEntryHeadLogDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdEntryHeadLogDto>> paged = ResultObject.createInstance(gwstdEntryHeadLogDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public void insertLog(GwstdEntryMessage gwstdEntryMessage, Date date, String errorMsg, String returnJson,String status) {
        GwstdEntryHeadLog gwstdEntryHeadLog = new GwstdEntryHeadLog();
        String sid = UUID.randomUUID().toString();
        gwstdEntryHeadLog.setSid(sid);
        gwstdEntryHeadLog.setInsertUser("KAFKA");
        gwstdEntryHeadLog.setInsertTime(date);
        gwstdEntryHeadLog.setTradeCode(gwstdEntryMessage.getHead().getTradeCode());
        gwstdEntryHeadLog.setEntryNo(gwstdEntryMessage.getHead().getEntryNo());
        gwstdEntryHeadLog.setSeqNo(gwstdEntryMessage.getHead().getSeqNo());
        gwstdEntryHeadLog.setErrorMsg(errorMsg);
        gwstdEntryHeadLog.setReturnJson(returnJson);
        gwstdEntryHeadLog.setStatus(status);
        gwstdEntryHeadLogMapper.insert(gwstdEntryHeadLog);
    }
}
