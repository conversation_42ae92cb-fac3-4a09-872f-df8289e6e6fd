package com.dcjet.cs.gwstdentry.service;

import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryAttachMapper;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryHeadMapper;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryWorkflowMapper;
import com.dcjet.cs.gwstdentry.mapper.GwstdEntryAttachDtoMapper;
import com.dcjet.cs.gwstdentry.model.GwstdEntryAttach;
import com.dcjet.cs.gwstdentry.model.GwstdEntryHead;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.google.common.base.Strings;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.file.XdoFileHandler;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 12/20/2024
 */
@Slf4j
@Service
public class GwstdEntryAttachService extends BaseService<GwstdEntryAttach> {
    @Resource
    private GwstdEntryAttachMapper gwstdEntryAttachMapper;
    @Resource
    private GwstdEntryAttachDtoMapper gwstdEntryAttachDtoMapper;
    @Resource
    private GwstdEntryWorkflowMapper gwstdEntryWorkflowMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private GwstdEntryHeadMapper gwstdEntryHeadMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private AttachedMapper attachedMapper;

    @Value("${dc.export.temp:}")
    private String tempPath;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    // 最大重试次数 3次
    private static final int MAX_RETRIES = 2;

    @Override
    public Mapper<GwstdEntryAttach> getMapper() {
        return gwstdEntryAttachMapper;
    }

    /**
     * 上海艺康下载订阅报关单附件任务调度
     */
    public void entryAttachDownloadForEcolab(boolean isInit) {
        String tradeCode = "3226941430";

        GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.ENTRY_ATTACH_DOWNLOAD_FILE_URL);
        if (null == gwstdHttpConfig || Strings.isNullOrEmpty(gwstdHttpConfig.getBaseUrl()) || Strings.isNullOrEmpty(gwstdHttpConfig.getServiceUrl())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置报文中心接口下载附件接口地址！"));
        }
        String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();

        Example exampleList = new Example(GwstdEntryHead.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andEqualTo("tradeCode", tradeCode);
        criteriaList.andEqualTo("dataSource", ConstantsStatus.STATUS_2);
        criteriaList.andEqualTo("attDownloadFlag", CommonEnum.attDownloadFlagEnum.WAIT.getCode());
        // 需要下载附件的订阅报关单
        List<GwstdEntryHead> allGwstdEntryHeadList = gwstdEntryHeadMapper.selectByExample(exampleList);

        List<GwstdEntryHead> gwstdEntryHeadList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allGwstdEntryHeadList)) {
            gwstdEntryHeadList = allGwstdEntryHeadList.size() > 50 ? allGwstdEntryHeadList.subList(0, 50) : allGwstdEntryHeadList;
        }

        // List<GwstdEntryHead> gwstdEntryHeadList = gwstdEntryHeadMapper.getDownloadAttachList(tradeCode);

        if (CollectionUtils.isEmpty(gwstdEntryHeadList)) {
            log.info("没有需要下载附件的订阅报关单");
            return;
        }

        Example exampleList1 = new Example(GwstdEntryAttach.class);
        Example.Criteria criteriaList1 = exampleList1.createCriteria();
        criteriaList1.andEqualTo("tradeCode", tradeCode);
        criteriaList1.andEqualTo("attDownloadFlag", CommonEnum.attDownloadFlagEnum.WAIT.getCode());
        // 需要下载的订阅报关单附件
        List<GwstdEntryAttach> needDownloadAttachList = gwstdEntryAttachMapper.selectByExample(exampleList1);

        // 统一编号分组需要下载的订阅报关单附件
        Map<String, List<GwstdEntryAttach>> needDownloadAttachGroupBySeqNo = new HashMap<>();
        if (CollectionUtils.isNotEmpty(needDownloadAttachList)) {
            needDownloadAttachGroupBySeqNo = needDownloadAttachList.stream().collect(Collectors.groupingBy(GwstdEntryAttach::getSeqNo));
        }

        Example exampleList2 = new Example(DecErpEHeadN.class);
        Example.Criteria criteriaList2 = exampleList2.createCriteria();
        criteriaList2.andEqualTo("tradeCode", tradeCode);
        List<DecErpEHeadN> decErpEHeadNList = decErpEHeadNMapper.selectByExample(exampleList2);
        // 报关单号分组出口预录入单
        Map<String, List<DecErpEHeadN>> decErpEHeadNListGroupByEntryNo = new HashMap<>();
        if (CollectionUtils.isNotEmpty(decErpEHeadNList)) {
            decErpEHeadNListGroupByEntryNo = decErpEHeadNList.stream().filter(it -> StringUtils.isNotBlank(it.getEntryNo())).collect(Collectors.groupingBy(DecErpEHeadN::getEntryNo));
        }

        List<DecErpEHeadN> attDecErpEHeadNList = decErpEHeadNMapper.getListByTradeCode(tradeCode);
        // 报关单号分组出口预录入单-其他随附单据信息
        Map<String, List<DecErpEHeadN>> attDecErpEHeadNListGroupByEntryNo = new HashMap<>();
        if (CollectionUtils.isNotEmpty(attDecErpEHeadNList)) {
            attDecErpEHeadNListGroupByEntryNo = attDecErpEHeadNList.stream().filter(it -> StringUtils.isNotBlank(it.getEntryNo())).collect(Collectors.groupingBy(DecErpEHeadN::getEntryNo));
        }

        Example exampleList3 = new Example(DecErpIHeadN.class);
        Example.Criteria criteriaList3 = exampleList3.createCriteria();
        criteriaList3.andEqualTo("tradeCode", tradeCode);
        List<DecErpIHeadN> decErpIHeadNList = decErpIHeadNMapper.selectByExample(exampleList3);
        // 报关单号分组进口预录入单
        Map<String, List<DecErpIHeadN>> decErpIHeadNListGroupByEntryNo = new HashMap<>();
        if (CollectionUtils.isNotEmpty(decErpIHeadNList)) {
            decErpIHeadNListGroupByEntryNo = decErpIHeadNList.stream().filter(it -> StringUtils.isNotBlank(it.getEntryNo())).collect(Collectors.groupingBy(DecErpIHeadN::getEntryNo));
        }

        List<DecErpIHeadN> attDecErpIHeadNList = decErpIHeadNMapper.getListByTradeCode(tradeCode);
        // 报关单号分组进口预录入单-其他随附单据信息
        Map<String, List<DecErpIHeadN>> attDecErpIHeadNListGroupByEntryNo = new HashMap<>();
        if (CollectionUtils.isNotEmpty(attDecErpIHeadNList)) {
            attDecErpIHeadNListGroupByEntryNo = attDecErpIHeadNList.stream().filter(it -> StringUtils.isNotBlank(it.getEntryNo())).collect(Collectors.groupingBy(DecErpIHeadN::getEntryNo));
        }

        for (GwstdEntryHead gwstdEntryHead : gwstdEntryHeadList) {
            // 初始化，不需要，当天日期-申报日期大于60天的条件
            if (isInit) {
                downloadFileWithEntryStatus(tradeCode, url, decErpEHeadNListGroupByEntryNo, attDecErpEHeadNListGroupByEntryNo, decErpIHeadNListGroupByEntryNo, attDecErpIHeadNListGroupByEntryNo, needDownloadAttachGroupBySeqNo, gwstdEntryHead);
            } else {
                Date entryDeclareDate = gwstdEntryHead.getEntryDeclareDate();
                if (entryDeclareDate == null) {
                    log.info("下载订阅报关单附件时，报关单号：" + gwstdEntryHead.getEntryNo() + "没有申报日期");
                    continue;
                }

                LocalDate today = LocalDate.now();
                LocalDate declareDate = entryDeclareDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                long daysBetween = ChronoUnit.DAYS.between(declareDate, today);

                // 若当天日期-申报日期大于60天，订阅报关单表头中的ATT_DOWNLOAD_FLAG=1(下载附件完成)；
                if (daysBetween > 60) {
                    gwstdEntryHead.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.SUCCESS.getCode());
                } else {
                    // 当前订阅报关单状态
                    downloadFileWithEntryStatus(tradeCode, url, decErpEHeadNListGroupByEntryNo, attDecErpEHeadNListGroupByEntryNo, decErpIHeadNListGroupByEntryNo, attDecErpIHeadNListGroupByEntryNo, needDownloadAttachGroupBySeqNo, gwstdEntryHead);
                }
            }
        }
        BulkSqlOpt.batchUpdateForErp(gwstdEntryHeadList, GwstdEntryHeadMapper.class);
    }


    private void downloadFileWithEntryStatus(String tradeCode, String url, Map<String, List<DecErpEHeadN>> decErpEHeadNListGroupByEntryNo, Map<String, List<DecErpEHeadN>> attDecErpEHeadNListGroupByEntryNo, Map<String, List<DecErpIHeadN>> decErpIHeadNListGroupByEntryNo, Map<String, List<DecErpIHeadN>> attDecErpIHeadNListGroupByEntryNo, Map<String, List<GwstdEntryAttach>> needDownloadAttachGroupBySeqNo, GwstdEntryHead gwstdEntryHead) {
        // 当前订阅报关单状态
        String entryStatus = gwstdEntryWorkflowMapper.selectStatus(gwstdEntryHead.getSeqNo(), gwstdEntryHead.getTradeCode());

        // 若报关单状态=1/K/P/B/R，订阅报关单表头中的ATT_DOWNLOAD_FLAG=1(下载附件完成)，以后不在下载该票的附件
        if ("1".equals(entryStatus) || "K".equals(entryStatus) || "P".equals(entryStatus) || "B".equals(entryStatus) || "R".equals(entryStatus)) {
            gwstdEntryHead.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.SUCCESS.getCode());
        }
        // 这票订阅报关单对应的需要下载附件的集合
        List<GwstdEntryAttach> gwstdEntryAttachListBySeqNo = needDownloadAttachGroupBySeqNo.get(gwstdEntryHead.getSeqNo());
        if (CollectionUtils.isNotEmpty(gwstdEntryAttachListBySeqNo)) {
            gwstdEntryAttachListBySeqNo.stream().filter(it -> StringUtils.isNotBlank(it.getAttId())).forEach(attach -> {
                // 每个附件，调用接口下载附件
                requestWithRetry(attach, url + "?attId=" + attach.getAttId(), tradeCode);
            });

            // 更新订阅报关单附件表
            BulkSqlOpt.batchUpdateForErp(gwstdEntryAttachListBySeqNo, GwstdEntryAttachMapper.class);
        }

        // 出口
        // 若该订阅报关单已反向生成过(预录单中存在该报关单号)，则对应的预录入单的随附单据附件先删后插(删除附件信息)
        if ("E".equals(gwstdEntryHead.getIEMark()) && MapUtils.isNotEmpty(decErpEHeadNListGroupByEntryNo) && CollectionUtils.isNotEmpty(decErpEHeadNListGroupByEntryNo.get(gwstdEntryHead.getEntryNo()))) {
            try {
                // 随附单据附件存在，删除附件
                if (MapUtils.isNotEmpty(attDecErpEHeadNListGroupByEntryNo) && CollectionUtils.isNotEmpty(attDecErpEHeadNListGroupByEntryNo.get(gwstdEntryHead.getEntryNo()))) {
                    deleteAttach(attDecErpEHeadNListGroupByEntryNo.get(gwstdEntryHead.getEntryNo()).get(0).getSid(), tradeCode, "EM");
                }
                attachedMapper.insertByEntryAttachE(tradeCode, gwstdEntryHead.getEntryNo());
            } catch (Exception e) {
                log.error("删除出口预录入单随附单据失败");
            }
        }

        // 进口
        if ("I".equals(gwstdEntryHead.getIEMark()) && MapUtils.isNotEmpty(decErpIHeadNListGroupByEntryNo) && CollectionUtils.isNotEmpty(decErpIHeadNListGroupByEntryNo.get(gwstdEntryHead.getEntryNo()))) {
            try {
                // 随附单据附件存在，删除附件
                if (MapUtils.isNotEmpty(attDecErpIHeadNListGroupByEntryNo) && CollectionUtils.isNotEmpty(attDecErpIHeadNListGroupByEntryNo.get(gwstdEntryHead.getEntryNo()))) {
                    deleteAttach(attDecErpIHeadNListGroupByEntryNo.get(gwstdEntryHead.getEntryNo()).get(0).getSid(), tradeCode, "IM");
                }
                attachedMapper.insertByEntryAttachI(tradeCode, gwstdEntryHead.getEntryNo());
            } catch (Exception e) {
                log.error("删除进口预录入单随附单据失败");
            }
        }
    }

    /**
     * 删除预录入单-其他随附单据
     *
     * @param headId
     * @param tradeCode
     * @param businessType
     * @throws Exception
     */
    public void deleteAttach(String headId, String tradeCode, String businessType) throws Exception {
        Example exampleAttach = new Example(Attached.class);
        Example.Criteria criteriaAttach = exampleAttach.createCriteria();
        criteriaAttach.andEqualTo("businessSid", headId);
        criteriaAttach.andEqualTo("tradeCode", tradeCode);
        criteriaAttach.andEqualTo("businessType", businessType);
        criteriaAttach.andEqualTo("acmpType", "other");
        List<Attached> list = attachedMapper.selectByExample(exampleAttach);
        if (CollectionUtils.isNotEmpty(list)) {
            for (Attached attached : list) {
                // 不能删除源附件，反向生成的时候，只是插入附件表，并未重新在文件服务器上生成附件
                // fileHandler.deleteFile(attached.getFileName());
                attachedMapper.deleteByPrimaryKey(attached.getSid());
            }
        }
    }

    /**
     * 下载附件文件
     *
     * @param attach
     * @param urlString
     * @param tradeCode
     * @return
     */
    public String requestWithRetry(GwstdEntryAttach attach, String urlString, String tradeCode) {
        int retries = 0;
        while (retries <= MAX_RETRIES) {
            try {
                String tempUrl = downloadPdf(attach, urlString, commonService.getToken(tradeCode));
                // String tempUrl = downloadPdf(attach, urlString, "b4b5b61e-35e9-46e8-a22c-397cff3d03c2");

                String fdfsId = "";
                try {
                    try {
                        fdfsId = fileHandler.uploadFile(tempUrl);
                    } catch (Exception ex) {
                        log.error("附件上传服务器失败，进行一次补偿操作。ex：" + ex.getMessage());
                        fdfsId = fileHandler.uploadFile(tempUrl);
                    }
                    log.info("已上传文件服务器 fdfsId ：" + fdfsId);
                } catch (Exception ex) {
                    log.error("附件上传服务器失败 ex：" + ex.getMessage());
                    fdfsId = "";
                }

                if (StringUtils.isNotBlank(fdfsId)) {
                    attach.setAttDownloadMess(null);
                    attach.setFdfsId(fdfsId);
                    attach.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.SUCCESS.getCode());
                    attach.setAttDownloadTime(new Date());
                    return attach.getAttDownloadFlag();
                } else {
                    attach.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.FAIL.getCode());
                    attach.setAttDownloadMess("附件上传服务器失败");
                }
            } catch (Exception e) {
                log.error("**************调用报文中心接口下载附件失败**************", e);
            }
            retries++;
            if (retries > MAX_RETRIES) {
                log.error("**************调用报文中心接口下载附件已达到最大重试次数，操作失败**************");
                return null;  // 超过最大重试次数，返回 null
            }
            log.info("调用报文中心接口下载附件正在进行第{}次重试...", retries);
            try {
                Thread.sleep(3000);  // 等待3秒后重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();  // 恢复中断状态
            }
        }
        return null;
    }

    public String downloadPdf(GwstdEntryAttach attach, String url, String token) {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)  // 设置连接超时
                .readTimeout(10, TimeUnit.SECONDS)     // 设置读取超时
                .writeTimeout(10, TimeUnit.SECONDS)    // 设置写入超时
                .build();

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)  // 添加 Token 到请求头
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String path = tempPath + UUID.randomUUID().toString() + ".pdf";
                try (InputStream inputStream = response.body().byteStream(); OutputStream outputStream = new FileOutputStream(path)) {
                    byte[] buffer = new byte[4096];  // 创建一个缓冲区
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }

                    // 下载完成后等待 1 秒
                    Thread.sleep(1000);
                    return path;
                } catch (Exception e) {
                    attach.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.FAIL.getCode());
                    attach.setAttDownloadMess("报文中心接口下载附件写入临时文件夹出错");
                    log.error("**************报文中心接口下载附件写入临时文件夹出错**************");
                    return null;
                }
            } else {
                attach.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.FAIL.getCode());
                attach.setAttDownloadMess("报文中心接口下载附件失败，返回错误代码");
                log.info("**************报文中心接口下载附件失败，返回错误代码**************");
            }
        } catch (Exception e) {
            attach.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.FAIL.getCode());
            attach.setAttDownloadMess("报文中心接口下载附件失败");
            log.error("**************报文中心接口下载附件失败**************");
        }
        return null;
    }


    /*public static void main(String[] args) {
        String url = "http://pmc.51haitun.cn/datacenter/api/ems/GetEntryPdf?attId=2022112116191804220037"; // 替换为实际的 PDF 文件 URL
        String outputFilePath = "D:\\Download\\output.pdf";  // 保存到本地的文件路径
        String token = "86771002-a30a-4a41-8b7b-bbe4b7c1c1ec";  // 替换为实际的 Token

        try {
            downloadPdf(url, outputFilePath, token);
            System.out.println("PDF file downloaded successfully!");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void downloadPdf(String fileUrl, String outputFilePath, String token) throws IOException {
        // 创建 OkHttpClient 实例
        OkHttpClient client = new OkHttpClient();

        // 创建请求对象，设置 Authorization 请求头携带 Token
        Request request = new Request.Builder()
                .url(fileUrl)  // 设置目标 URL
                .addHeader("Authorization", "Bearer " + token)  // 添加 Token 到请求头
                .build();

        // 执行请求，获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 获取响应的字节流
                try (InputStream inputStream = response.body().byteStream();
                     OutputStream outputStream = new FileOutputStream(outputFilePath)) {

                    byte[] buffer = new byte[4096];  // 创建一个缓冲区
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }
            } else {
                System.err.println("Request failed with status code: " + response.code());
            }
        }
    }*/
}
