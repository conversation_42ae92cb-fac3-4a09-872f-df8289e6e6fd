package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("OcrXDListData")
public class OcrXDListData implements Serializable {

    @ApiModelProperty("序号")
    @JsonProperty("XDCommodity_Item")
    private OcrFieldInfo XDCommodity_Item;

    @ApiModelProperty("订单号码")
    @JsonProperty("XDCommodity_PO")
    private OcrFieldInfo XDCommodity_PO;

    @ApiModelProperty("订单项号")
    @JsonProperty("XDCommodity_PO_Item")
    private OcrFieldInfo XDCommodity_PO_Item;

    @ApiModelProperty("料号")
    @JsonProperty("XDCommodity_Gds_PartNo")
    private OcrFieldInfo XDCommodity_Gds_PartNo;

    @ApiModelProperty("商品描述")
    @JsonProperty("XDCommodity_Gds_Desc")
    private OcrFieldInfo XDCommodity_Gds_Desc;

    @ApiModelProperty("数量")
    @JsonProperty("XDCommodity_Gds_Qty")
    private OcrFieldInfo XDCommodity_Gds_Qty;

    @ApiModelProperty("数量单位")
    @JsonProperty("XDCommodity_Gds_Unit")
    private OcrFieldInfo XDCommodity_Gds_Unit;

    @ApiModelProperty("单价")
    @JsonProperty("XDCommodity_Gds_Price")
    private OcrFieldInfo XDCommodity_Gds_Price;

    @ApiModelProperty("百个单价")
    @JsonProperty("XDCommodity_Gds_PriceHundred")
    private OcrFieldInfo XDCommodity_Gds_PriceHundred;

    @ApiModelProperty("千个单价")
    @JsonProperty("XDCommodity_Gds_PriceThousand")
    private OcrFieldInfo XDCommodity_Gds_PriceThousand;

    @ApiModelProperty("币别")
    @JsonProperty("XDCommodity_Gds_Curr")
    private OcrFieldInfo XDCommodity_Gds_Curr;

    @ApiModelProperty("金额")
    @JsonProperty("XDCommodity_Gds_AMT")
    private OcrFieldInfo XDCommodity_Gds_AMT;

    @ApiModelProperty("净重")
    @JsonProperty("XDCommodity_Gds_NW")
    private OcrFieldInfo XDCommodity_Gds_NW;

    @ApiModelProperty("净重单位")
    @JsonProperty("XDCommodity_Gds_NWUnit")
    private OcrFieldInfo XDCommodity_Gds_NWUnit;

    @ApiModelProperty("毛重")
    @JsonProperty("XDCommodity_Gds_GW")
    private OcrFieldInfo XDCommodity_Gds_GW;

    @ApiModelProperty("毛重单位")
    @JsonProperty("XDCommodity_Gds_GWUnit")
    private OcrFieldInfo XDCommodity_Gds_GWUnit;

    @ApiModelProperty("原产国")
    @JsonProperty("XDCommodity_Gds_COO")
    private OcrFieldInfo XDCommodity_Gds_COO;

    @ApiModelProperty("件数")
    @JsonProperty("XDCommodity_Gds_PkgQty")
    private OcrFieldInfo XDCommodity_Gds_PkgQty;

    @ApiModelProperty("包装方式")
    @JsonProperty("XDCommodity_Gds_PkgUom")
    private OcrFieldInfo XDCommodity_Gds_PkgUom;

    @ApiModelProperty("包装编号")
    @JsonProperty("XDCommodity_Gds_PkgNo")
    private OcrFieldInfo XDCommodity_Gds_PkgNo;

    }
