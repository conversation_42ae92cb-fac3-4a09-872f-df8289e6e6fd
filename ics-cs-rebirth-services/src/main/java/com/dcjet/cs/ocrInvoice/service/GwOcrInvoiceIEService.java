package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.bi.service.BiCustomerParamsService;
import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.entry.dao.EntryHeadThirdMapper;
import com.dcjet.cs.entry.dao.EntryListThirdMapper;
import com.dcjet.cs.entry.dao.GwstdEntryHeadThirdLocMapper;
import com.dcjet.cs.entry.dao.GwstdEntryListThirdLocMapper;
import com.dcjet.cs.entry.model.EntryHeadThird;
import com.dcjet.cs.entry.model.EntryHeadThirdLoc;
import com.dcjet.cs.entry.model.EntryListThird;
import com.dcjet.cs.entry.model.EntryListThirdLoc;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.ocrInvoice.component.IdentityDataMetaData;
import com.dcjet.cs.ocrInvoice.component.exp.OcrDataValidationException;
import com.dcjet.cs.ocrInvoice.dao.*;
import com.dcjet.cs.ocrInvoice.model.*;
import com.dcjet.cs.ocrInvoice.model.ocr.*;
import com.dcjet.cs.ocrInvoice.model.task.OcrTaskStatusEnum;
import com.dcjet.cs.util.*;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.json.StringTrimModule;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.*;

/**
 * 处理OCR识别公共service
 */
@Service
public class GwOcrInvoiceIEService {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    public ThreadLocal<IdentityDataMetaData> biCustomerParamsThreadLocal = new ThreadLocal<>();
    @Resource
    protected GwstdHttpConfigMapper httpConfigMapper;
    @Resource
    private GwOcrInvoiceIHeadService ocrIHeadService;
    @Resource
    private GwOcrInvoiceIHeadMapper ocrIHeadMapper;
    @Resource
    private GwOcrInvoiceIListMapper ocrIListMapper;
    @Resource
    private GwOcrInvoiceIHeadLocMapper ocrIHeadLocMapper;
    @Resource
    private GwOcrInvoiceIListLocMapper ocrIListLocMapper;
    @Resource
    private GwOcrInvoiceEHeadService ocrEHeadService;
    @Resource
    private GwOcrInvoiceEHeadMapper ocrEHeadMapper;
    @Resource
    private GwOcrInvoiceEListMapper ocrEListMapper;
    @Resource
    private GwOcrInvoiceEHeadLocMapper ocrEHeadLocMapper;
    @Resource
    private GwOcrInvoiceEListLocMapper ocrEListLocMapper;
    @Resource
    private GwOcrLogMapper logMapper;
    @Resource
    private GwOcrLogDetailMapper logDetailMapper;
    @Resource
    private GwOcrLogService gwOcrLogService;
    @Resource
    private BiCustomerParamsService biCustomerParamsService;
    @Resource
    private EntryHeadThirdMapper entryHeadThirdMapper;
    @Resource
    private GwstdEntryHeadThirdLocMapper gwstdEntryHeadThirdLocMapper;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Value("${dc.export.temp:}")
    private String tempPath;
    @Resource
    private GwOcrLogMapper gwOcrLogMapper;
    @Resource
    private ValidatorUtil validatorUtil;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private CommonService commonService;
    @Resource
    private FastdFsService fastdFsService;

    /**
     * 识别数据
     */
    public void identifyData() {
        // 获取任务
        List<GwOcrLog> gwOcrLogs;
        {
            gwOcrLogs = gwOcrLogMapper.getTaskListByStatus("0");
            if (CollectionUtils.isEmpty(gwOcrLogs)) {
                LoggerUtil.logInfo(logger, "未获取可执行任务");
                return;
            }
        }

        // 获取url
        GwstdHttpConfig httpConfig = httpConfigMapper.selectByType("OCR_RESULT");
        if (httpConfig == null) {
            LoggerUtil.logInfo(logger, "未配置httpConfig: OCR_RESULT");
            return;
        }

        // 循环处理任务
        String currTradeCode = StringUtils.EMPTY;
        for (GwOcrLog task : gwOcrLogs) {
            // 组装token
            UserInfoToken token = new UserInfoToken() {{
                setCompany(task.getTradeCode());
                setUserNo(task.getInsertUser());
                setUserName(task.getInsertUserName());
            }};

            // 参数转换数据源
            if (!currTradeCode.equals(task.getTradeCode())) {
                setBiCustomerParams(token);
            }

            // 执行任务
            runTask(task, httpConfig, token);
            // 回填任务表
            logMapper.updateByPrimaryKey(task);
        }
    }

    public void setBiCustomerParams(UserInfoToken token) {
        biCustomerParamsThreadLocal.set(
                new IdentityDataMetaData() {{
                    setMapForCurr(biCustomerParamsService.getBiCusParaMap("CURR", token));
                    setMapForUnit(biCustomerParamsService.getBiCusParaMap("UNIT", token));
                    setMapForCountry(biCustomerParamsService.getBiCusParaMap("COUNTRY", token));
                }}
        );
    }

    /**
     * runTask
     *
     * @param task
     */
    @SneakyThrows
    protected void runTask(GwOcrLog task, GwstdHttpConfig httpConfig, UserInfoToken token) {
        // 解析获取OCR识别数据的 参数
        String requestId;
        List<Long> recordIds;
        {

            String uploadRes = task.getUploadRes();
            ResultObject<UploadResultData> ro = JsonObjectMapper.getInstance().fromJson(uploadRes, new TypeReference<ResultObject<UploadResultData>>() {
            });
            if (ro == null || ro.getData() == null || CollectionUtils.isEmpty(ro.getData().getRecord_id_list())) {
                LoggerUtil.logInfo(logger, "解析 GwOcrLog.uploadRes任务 失败 taskId: " + task.getSid());
                task.setStatus(OcrTaskStatusEnum.IDENTITY_FAIL.toString());
                task.setErrorMessage(xdoi18n.XdoI18nUtil.t("GwOcrLog.uploadRes为null"));
                return;
            }

            UploadResultData uploadResultData = ro.getData();
            requestId = uploadResultData.getRequest_id();
            recordIds = uploadResultData.getRecord_id_list();
        }

        // 循环处理 recordIdList (只取第一个）
        task.setBussinessPk("");
        Long recordId = recordIds.get(0);
        {

            // 调用接口 获取数据 (返回字符串 resultStr)
            String resultUrl = httpConfig.getBaseUrl() + httpConfig.getServiceUrl() + "?record_id=" + recordId + "&sid=" + requestId;
            Map<String, String> headers = new HashMap<String, String>(2){{
                put("Authorization", commonService.getToken(token.getCompany(), ""));
                put("corpCode", token.getCompany());
            }};
            String resultStr = OkHttpUtils.httpGet(resultUrl, headers);

            // 记录 logDetail
            GwOcrLogDetail logDetail;
            {
                logDetail = new GwOcrLogDetail().initData(token);
                logDetail.setHeadId(task.getSid());
                logDetail.setReceiveData(resultStr);
            }
            logDetailMapper.insert(logDetail);

            // 解析 OcrData
            OcrDataOut ocrData;
            {
                // 解析 ResultObject
                ResultObject<OcrDataOut> resultObject = new ObjectMapper()
                        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                        .registerModule(new StringTrimModule()).readValue(resultStr, new TypeReference<ResultObject<OcrDataOut>>() {
                        });

                // 解析失败，记录log状态，直接返回
                if (resultObject == null) {
                    task.setStatus(OcrTaskStatusEnum.IDENTITY_FAIL.toString());
                    task.setErrorMessage(xdoi18n.XdoI18nUtil.t("接口返回ResultObject为null"));
                    return;
                }
                if (!resultObject.isSuccess()) {
                    if (resultObject.getCode() == 404 || resultObject.getCode() == 500) {
                        task.setStatus(OcrTaskStatusEnum.IDENTITY_FAIL.toString()); // 接口返回false
                    } else {
                        task.setErrorMessage("errorMessage:" + resultObject.getMessage());
                    }
                    return;
                }

                // 内层 OCR data
                ocrData = resultObject.getData();
            }

            // 处理数据
            if (ocrData == null || ocrData.getData() == null) {
                LoggerUtil.logInfo(logger, "获取 OCR 识别数据失败， resultStr：" + resultStr);
                task.setStatus(OcrTaskStatusEnum.IDENTITY_FAIL.toString());
                task.setErrorMessage(xdoi18n.XdoI18nUtil.t("接口返回ocrData为null"));
                return;
            }

            //
            try {
                insertOcrDataAll(ocrData, task);
            } catch (OcrDataValidationException ex) {
                task.setStatus(OcrTaskStatusEnum.IDENTITY_FAIL.toString());
                task.setErrorMessage(ex.getMessage().length() > 2000 ? ex.getMessage().substring(0, 2000) : ex.getMessage());
            }

        }
    }

    /**
     *
     */
    private void insertOcrDataAll(OcrDataOut ocrData, GwOcrLog task) throws OcrDataValidationException {

        handleOcrTaskData(ocrData, task);

        if (OcrTaskStatusEnum.UPLOAD_SUCCESS.toString().equals(task.getStatus())) {
            task.setStatus(OcrTaskStatusEnum.IDENTITY_FAIL.toString());
            task.setErrorMessage(xdoi18n.XdoI18nUtil.t("识别数据无效"));// 1主键无效 2主节点无效
        }
    }

    /**
     * 判断是否一个pdf文件存在多票(发票&箱单&提单)
     *
     * @param ocrDataOut
     * @return
     */
    private int invoiceCount(OcrDataOut ocrDataOut) {
        OcrDataInner ocrDataInner = ocrDataOut.getData();
        int count = 0;
        if (ocrDataInner.getFPList() != null) {
            count += ocrDataInner.getFPList().size();
        }
        if (ocrDataInner.getXDList() != null) {
            count += ocrDataInner.getXDList().size();
        }
        if (ocrDataInner.getTDList() != null) {
            count += ocrDataInner.getTDList().size();
        }
        return count;
    }

    @SneakyThrows
    private Attached getNewAttached(GwOcrLog task) {
        String newTaskId = UUID.randomUUID().toString();
        //如果单pdf识别到多个发票、箱单、提单时,复制附件
        String subTaskId = task.getSubTaskId();
        List<Attached> attachedList = attachedMapper.selectByBusinessId(subTaskId);
        Attached attached = null;
        if (CollectionUtils.isNotEmpty(attachedList)) {
            String theTempPath = null;
            try {
                Attached att = attachedList.get(0);
                String uuid = UUID.randomUUID().toString();
                theTempPath = tempPath + "/" + uuid + "/";
                //判断文件夹是否存在
                File dir = new File(theTempPath);
                if (!dir.exists()) {
                    dir.mkdir();
                }
                UserInfoToken userInfo = new UserInfoToken();
                userInfo.setCompany(task.getTradeCode());
                userInfo.setUserNo(task.getInsertUser());
                String originFileName = att.getOriginFileName();
                String fastUrl = att.getFileName();
                String businessType = att.getBusinessType();
                String acmpType = att.getAcmpType();
                String filePath = theTempPath + originFileName;
                fastdFsService.downloadFileToLocal(fastUrl, filePath);
                attached = this.addAttached(newTaskId, businessType, acmpType, filePath, originFileName, userInfo);
            } catch (Exception ex) {
                FileUtil.deleteFileTree(theTempPath);
                throw new RuntimeException(ex);
            }
            //删除临时文件
            FileUtil.deleteFileTree(theTempPath);
        }
        return attached;
    }

    @SneakyThrows
    public void deleteOriginAttached(String originalSubTaskId) {
        List<Attached> attachedList = attachedMapper.selectByBusinessId(originalSubTaskId);
        if (CollectionUtils.isNotEmpty(attachedList)) {
            Attached att = attachedList.get(0);
            fileHandler.deleteFile(att.getFileName());
            attachedMapper.deleteByPrimaryKey(att.getSid());
        }
    }

    @SneakyThrows
    private void handleOcrTaskData(OcrDataOut ocrData, GwOcrLog task) {
        String originalSubTaskId = task.getSubTaskId();
        int count = invoiceCount(ocrData);
        // 处理发票
        List<OcrFPData> FPList = ocrData.getData().getFPList();
        if (CollectionUtils.isNotEmpty(FPList)) {
            for (OcrFPData ocrFPData : FPList) {
                String invoiceNo;
                if (ocrFPData.getFPINVOICE_NO() != null) {
                    invoiceNo = ocrFPData.getFPINVOICE_NO().getValue();
                } else {
                    continue;
                }
                //如果单pdf识别到多个发票、箱单、提单时,复制附件
                if (count > 1) {
                    Attached newAttached = getNewAttached(task);
                    if (newAttached != null) {
                        String newSubTaskId = newAttached.getBusinessSid();
                        task.setSubTaskId(newSubTaskId);
                    }
                }
                OcrTaskStatusEnum handleStatus;
                try {
                    if (StringUtils.equals(task.getExtendFiled1(), "I")) {
                        handleStatus = insertOcrIFPData(ocrFPData, task);
                    } else {
                        handleStatus = insertOcrEFPData(ocrFPData, task);
                    }
                    task.setStatus(handleStatus.toString());
                    if (OcrTaskStatusEnum.DATA_REPEAT.equals(handleStatus)) {
                        deleteOriginAttached(originalSubTaskId);
                        task.setErrorMessage(task.getErrorMessage() + xdoi18n.XdoI18nUtil.t("｜发票数据重复:") + invoiceNo);
                    }
                    task.setBussinessPk(task.getBussinessPk() + invoiceNo + "|");
                } catch (Exception ex) {
                    deleteOriginAttached(originalSubTaskId);
                }
            }
        }

        // 处理箱单
        List<OcrXDData> XDList = ocrData.getData().getXDList();
        if (CollectionUtils.isNotEmpty(XDList)) {
            for (OcrXDData ocrXDData : XDList) {
                String invoiceNo;
                if (ocrXDData.getXDINVOICE_NO() != null) {
                    invoiceNo = ocrXDData.getXDINVOICE_NO().getValue();
                } else {
                    continue;
                }
                //如果单pdf识别到多个发票、箱单、提单时,复制附件
                if (count > 1) {
                    Attached newAttached = getNewAttached(task);
                    if (newAttached != null) {
                        String newSubTaskId = newAttached.getBusinessSid();
                        task.setSubTaskId(newSubTaskId);
                    }
                }
                OcrTaskStatusEnum handleStatus;
                try {
                    if (StringUtils.equals(task.getExtendFiled1(), "I")) {
                        handleStatus = insertOcrIXDData(ocrXDData, task);
                    } else {
                        handleStatus = insertOcrEXDData(ocrXDData, task);
                    }
                    task.setStatus(handleStatus.toString());
                    if (OcrTaskStatusEnum.DATA_REPEAT.equals(handleStatus)) {
                        deleteOriginAttached(originalSubTaskId);
                        task.setErrorMessage(task.getErrorMessage() + xdoi18n.XdoI18nUtil.t("｜箱单数据重复:") + invoiceNo);
                    }
                } catch (Exception ex) {
                    deleteOriginAttached(originalSubTaskId);
                }
            }
            if (!OcrTaskStatusEnum.DATA_REPEAT.toString().equals(task.getStatus())) {
                task.setStatus(OcrTaskStatusEnum.IDENTITY_SUCCESS.toString());
            }
        }

        // 处理提单
        List<OcrTDData> TDList = ocrData.getData().getTDList();
        if (CollectionUtils.isNotEmpty(TDList)) {
            for (OcrTDData ocrTDData : TDList) {
                String mawb;
                String hawb = StringUtils.EMPTY;
                if (ocrTDData.getTDMAWB() != null) {
                    mawb = ocrTDData.getTDMAWB().getValue();
                } else {
                    continue;
                }
                //如果单pdf识别到多个发票、箱单、提单时,复制附件
                if (count > 1) {
                    Attached newAttached = getNewAttached(task);
                    if (newAttached != null) {
                        String newSubTaskId = newAttached.getBusinessSid();
                        task.setSubTaskId(newSubTaskId);
                    }
                }
                if (ocrTDData.getTDHAWB() != null) {
                    hawb = ocrTDData.getTDHAWB().getValue();
                }
                OcrTaskStatusEnum handleStatus;
                try {
                    if (StringUtils.equals(task.getExtendFiled1(), "I")) {
                        handleStatus = insertOcrITDData(ocrTDData, task);
                    } else {
                        handleStatus = insertOcrETDData(ocrTDData, task);
                    }
                    task.setStatus(handleStatus.toString());
                    if (OcrTaskStatusEnum.DATA_REPEAT.equals(handleStatus)) {
                        deleteOriginAttached(originalSubTaskId);
                        task.setErrorMessage(task.getErrorMessage() + xdoi18n.XdoI18nUtil.t("｜提单数据重复: 主提运单 ") + mawb + xdoi18n.XdoI18nUtil.t(" 分提运单 ") + hawb);
                    }
                } catch (Exception ex) {
                    deleteOriginAttached(originalSubTaskId);
                }
            }
            if (!OcrTaskStatusEnum.DATA_REPEAT.toString().equals(task.getStatus())) {
                task.setStatus(OcrTaskStatusEnum.IDENTITY_SUCCESS.toString());
            }
        }

        //删除原附件
        if (count > 1) {
            deleteOriginAttached(originalSubTaskId);
        }

        // 处理报关单
        List<OcrEntryData> ocrEntryDatas = ocrData.getData().getBGDList();
        if (CollectionUtils.isNotEmpty(ocrEntryDatas)) {
            OcrEntryData ocrEntryData = ocrEntryDatas.get(0);
            if (ocrEntryDatas.size() > 1) {
                for (int i = 1; i < ocrEntryDatas.size(); i++) {
                    if (CollectionUtils.isNotEmpty(ocrEntryDatas.get(i).getBGDCommodity())) {
                        ocrEntryData.getBGDCommodity().addAll(ocrEntryDatas.get(i).getBGDCommodity());
                    }
                }
            }
            OcrTaskStatusEnum handleStatus = insertOcrBGDData(ocrEntryData, task);
            task.setStatus(handleStatus.toString());
        }
    }

    /**
     * 插入ocr 进口发票数据
     *
     * @param ocrFPData
     * @param task
     * @return
     */
    private OcrTaskStatusEnum insertOcrIFPData(OcrFPData ocrFPData, GwOcrLog task) throws OcrDataValidationException {
        try {
            // 1 处理 invoiceHead （此处有存在性校验）
            GwOcrInvoiceIHead head;
            {
                head = new GwOcrInvoiceIHead().initData(task);
                // 数据组装
                head.assembly(ocrFPData);
                // 如果已经存在此发票箱单记录 返回
                if (ocrIHeadService.isExistsInvoice("INVOICE", head.getInvoiceNo(), task.getTradeCode())) {
                    return OcrTaskStatusEnum.DATA_REPEAT;
                }
                // 基础参数库转换
                convertBasicParamsI(head);
                head.setDataType("INVOICE");
            }
            ocrIHeadService.checkOcrHead(head);
            ocrIHeadMapper.insert(head);
            // 2 处理 invoiceHeadLoc
            GwOcrInvoiceIHeadLoc headLoc;
            {
                headLoc = new GwOcrInvoiceIHeadLoc().initData(task);
                // 主键与head表主键 保持一致
                headLoc.setSid(head.getSid());
                headLoc.assembly(ocrFPData);
            }
            ocrIHeadLocMapper.insert(headLoc);
            // 3 处理表体
            if (CollectionUtils.isNotEmpty(ocrFPData.getFPCommodity())) {
                List<OcrFPListData> listDatas = ocrFPData.getFPCommodity();
                Integer[] indexForSerialNo = {1};
                for (OcrFPListData ocrList : listDatas) {
                    // 3.1 处理 invoiceList
                    GwOcrInvoiceIList list;
                    {
                        list = new GwOcrInvoiceIList().initData(task);
                        list.setHeadId(head.getSid());
                        list.setSerialNo(indexForSerialNo[0]++);
                        list.setBussinessType(head.getDataType());
                        list.assembly(ocrList);
                        // 空值处理
                        list.setNullValueFromHead(head);
                        // 基础参数库转换
                        convertBasicParamsI(list);
                    }
                    ocrIHeadService.checkOcrList(list);
                    ocrIListMapper.insert(list);
                    // 3.2 处理 invoiceListLoc
                    GwOcrInvoiceIListLoc listLoc;
                    {
                        listLoc = new GwOcrInvoiceIListLoc().initData(task);
                        listLoc.setSid(list.getSid());
                        listLoc.assembly(ocrList);
                    }
                    ocrIListLocMapper.insert(listLoc);
                }
            }
        } catch (OcrDataValidationException ex) {
            LoggerUtil.logError(logger, "OCR 校验错误信息：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        } catch (Exception ex) {
            LoggerUtil.logError(logger, "OCR提单识别数据入库失败：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        }
        return OcrTaskStatusEnum.IDENTITY_SUCCESS;
    }

    /**
     * 插入ocr 出口发票数据
     *
     * @param ocrFPData
     * @param task
     * @return
     */
    private OcrTaskStatusEnum insertOcrEFPData(OcrFPData ocrFPData, GwOcrLog task) throws OcrDataValidationException {
        try {
            // 1 处理 invoiceHead （此处有存在性校验）
            GwOcrInvoiceEHead head;
            {
                head = new GwOcrInvoiceEHead().initData(task);
                // 数据组装
                head.assembly(ocrFPData);
                // 如果已经存在此发票箱单记录 返回
                if (ocrEHeadService.isExistsInvoice("INVOICE", head.getInvoiceNo(), task.getTradeCode())) {
                    return OcrTaskStatusEnum.DATA_REPEAT;
                }
                // 基础参数库转换
                convertBasicParamsE(head);
                head.setDataType("INVOICE");
            }
            ocrEHeadService.checkOcrHead(head);
            ocrEHeadMapper.insert(head);
            // 2 处理 invoiceHeadLoc
            GwOcrInvoiceEHeadLoc headLoc;
            {
                headLoc = new GwOcrInvoiceEHeadLoc().initData(task);
                // 主键与head表主键 保持一致
                headLoc.setSid(head.getSid());
                headLoc.assembly(ocrFPData);
            }
            ocrEHeadLocMapper.insert(headLoc);
            // 3 处理表体
            if (!CollectionUtils.isEmpty(ocrFPData.getFPCommodity())) {
                List<OcrFPListData> listDatas = ocrFPData.getFPCommodity();
                Integer[] indexForSerialNo = {1};
                for (OcrFPListData ocrList : listDatas) {
                    // 3.1 处理 invoiceList
                    GwOcrInvoiceEList list;
                    {
                        list = new GwOcrInvoiceEList().initData(task);
                        list.setHeadId(head.getSid());
                        list.setSerialNo(indexForSerialNo[0]++);
                        list.setBussinessType(head.getDataType());
                        list.assembly(ocrList);
                        // 空值处理
                        list.setNullValueFromHead(head);
                        // 基础参数库转换
                        convertBasicParamsE(list);
                    }
                    ocrEHeadService.checkOcrList(list);
                    ocrEListMapper.insert(list);
                    // 3.2 处理 invoiceListLoc
                    GwOcrInvoiceEListLoc listLoc;
                    {
                        listLoc = new GwOcrInvoiceEListLoc().initData(task);
                        listLoc.setSid(list.getSid());
                        listLoc.assembly(ocrList);
                    }
                    ocrEListLocMapper.insert(listLoc);
                }
            }
        } catch (OcrDataValidationException ex) {
            LoggerUtil.logError(logger, "OCR 校验错误信息：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        } catch (Exception ex) {
            LoggerUtil.logError(logger, "OCR提单识别数据入库失败：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        }
        return OcrTaskStatusEnum.IDENTITY_SUCCESS;
    }

    /**
     * 插入ocr 进口箱单数据
     *
     * @param ocrXDData
     * @param task
     */
    private OcrTaskStatusEnum insertOcrIXDData(OcrXDData ocrXDData, GwOcrLog task) throws OcrDataValidationException {
        try {
            // 1 处理 invoiceHead （此处有存在性校验）
            GwOcrInvoiceIHead head;
            {
                head = new GwOcrInvoiceIHead().initData(task);
                // 数据组装
                head.assembly(ocrXDData);
                // 如果已经存在此发票箱单记录 返回
                if (ocrIHeadService.isExistsInvoice("PACKING", head.getInvoiceNo(), task.getTradeCode())) {
                    return OcrTaskStatusEnum.DATA_REPEAT;
                }
                // 基础参数库转换
                convertBasicParamsI(head);
                head.setDataType("PACKING");
            }
            ocrIHeadService.checkOcrHead(head);
            ocrIHeadMapper.insert(head);
            // 2 处理 invoiceHeadLoc
            GwOcrInvoiceIHeadLoc headLoc;
            {
                headLoc = new GwOcrInvoiceIHeadLoc().initData(task);
                // 主键与head表主键 保持一致
                headLoc.setSid(head.getSid());
                headLoc.assembly(ocrXDData);
            }
            String err = validatorUtil.validation(headLoc);
            ocrIHeadLocMapper.insert(headLoc);
            // 3 处理表体
            if (!CollectionUtils.isEmpty(ocrXDData.getXDCommodity())) {
                List<OcrXDListData> listDatas = ocrXDData.getXDCommodity();
                Integer[] indexForSerialNo = {1};
                for (OcrXDListData ocrList : listDatas) {
                    // 3.1 处理 invoiceList
                    GwOcrInvoiceIList list;
                    {
                        list = new GwOcrInvoiceIList().initData(task);
                        list.setHeadId(head.getSid());
                        list.setSerialNo(indexForSerialNo[0]++);
                        list.setBussinessType(head.getDataType());
                        list.assembly(ocrList);
                        // 空值处理
                        list.setNullValueFromHead(head);
                        // 基础参数库转换
                        convertBasicParamsI(list);
                    }
                    //箱单提单不做校验
                    //ocrIHeadService.checkOcrList(list);
                    ocrIListMapper.insert(list);
                    // 3.2 处理 invoiceListLoc
                    GwOcrInvoiceIListLoc listLoc;
                    {
                        listLoc = new GwOcrInvoiceIListLoc().initData(task);
                        listLoc.setSid(list.getSid());
                        listLoc.assembly(ocrList);
                    }
                    ocrIListLocMapper.insert(listLoc);
                }
            }
        } catch (OcrDataValidationException ex) {
            LoggerUtil.logError(logger, "OCR 校验错误信息：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        } catch (Exception ex) {
            LoggerUtil.logError(logger, "OCR提单识别数据入库失败：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        }
        return OcrTaskStatusEnum.IDENTITY_SUCCESS;
    }

    /**
     * 插入ocr 出口箱单数据
     *
     * @param ocrXDData
     * @param task
     */
    private OcrTaskStatusEnum insertOcrEXDData(OcrXDData ocrXDData, GwOcrLog task) throws OcrDataValidationException {
        try {
            // 1 处理 invoiceHead （此处有存在性校验）
            GwOcrInvoiceEHead head;
            {
                head = new GwOcrInvoiceEHead().initData(task);
                // 数据组装
                head.assembly(ocrXDData);
                // 如果已经存在此发票箱单记录 返回
                if (ocrEHeadService.isExistsInvoice("PACKING", head.getInvoiceNo(), task.getTradeCode())) {
                    return OcrTaskStatusEnum.DATA_REPEAT;
                }
                // 基础参数库转换
                convertBasicParamsE(head);
                head.setDataType("PACKING");
            }
            ocrEHeadService.checkOcrHead(head);
            ocrEHeadMapper.insert(head);
            // 2 处理 invoiceHeadLoc
            GwOcrInvoiceEHeadLoc headLoc;
            {
                headLoc = new GwOcrInvoiceEHeadLoc().initData(task);
                // 主键与head表主键 保持一致
                headLoc.setSid(head.getSid());
                headLoc.assembly(ocrXDData);
            }
            String err = validatorUtil.validation(headLoc);
            ocrEHeadLocMapper.insert(headLoc);
            // 3 处理表体
            if (!CollectionUtils.isEmpty(ocrXDData.getXDCommodity())) {
                List<OcrXDListData> listDatas = ocrXDData.getXDCommodity();
                Integer[] indexForSerialNo = {1};
                for (OcrXDListData ocrList : listDatas) {
                    // 3.1 处理 invoiceList
                    GwOcrInvoiceEList list;
                    {
                        list = new GwOcrInvoiceEList().initData(task);
                        list.setHeadId(head.getSid());
                        list.setSerialNo(indexForSerialNo[0]++);
                        list.setBussinessType(head.getDataType());
                        list.assembly(ocrList);
                        // 空值处理
                        list.setNullValueFromHead(head);
                        // 基础参数库转换
                        convertBasicParamsE(list);
                    }
                    //箱单提单不做校验
                    //ocrEHeadService.checkOcrList(list);
                    ocrEListMapper.insert(list);
                    // 3.2 处理 invoiceListLoc
                    GwOcrInvoiceEListLoc listLoc;
                    {
                        listLoc = new GwOcrInvoiceEListLoc().initData(task);
                        listLoc.setSid(list.getSid());
                        listLoc.assembly(ocrList);
                    }
                    ocrEListLocMapper.insert(listLoc);
                }
            }
        } catch (OcrDataValidationException ex) {
            LoggerUtil.logError(logger, "OCR 校验错误信息：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        } catch (Exception ex) {
            LoggerUtil.logError(logger, "OCR提单识别数据入库失败：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        }

        return OcrTaskStatusEnum.IDENTITY_SUCCESS;
    }

    /**
     * 插入ocr 进口提单数据
     *
     * @param ocrTDData
     * @param task
     */
    private OcrTaskStatusEnum insertOcrITDData(OcrTDData ocrTDData, GwOcrLog task) throws OcrDataValidationException {
        try {
            // 1 处理 invoiceHead （此处有存在性校验）
            GwOcrInvoiceIHead head;
            {
                head = new GwOcrInvoiceIHead().initData(task);
                // 数据组装
                head.assembly(ocrTDData);
                // 如果已经存在此发票箱单记录 返回
                if (ocrIHeadService.isExistsBill("LADING", head.getMawb(), head.getHawb(), task.getTradeCode())) {
                    return OcrTaskStatusEnum.DATA_REPEAT;
                }
                // 基础参数库转换
                convertBasicParamsI(head);
                head.setDataType("LADING");
            }
            ocrIHeadService.checkOcrHead(head);
            ocrIHeadMapper.insert(head);
            // 2 处理 invoiceHeadLoc
            GwOcrInvoiceIHeadLoc headLoc;
            {
                headLoc = new GwOcrInvoiceIHeadLoc().initData(task);
                // 主键与head表主键 保持一致
                headLoc.setSid(head.getSid());
                headLoc.assembly(ocrTDData);
            }
            ocrIHeadLocMapper.insert(headLoc);
            // 3 处理表体
            if (!CollectionUtils.isEmpty(ocrTDData.getTDCommodity())) {
                List<OcrTDListData> listDatas = ocrTDData.getTDCommodity();
                Integer[] indexForSerialNo = {1};
                for (OcrTDListData ocrList : listDatas) {
                    // 3.1 处理 invoiceList
                    GwOcrInvoiceIList list;
                    {
                        list = new GwOcrInvoiceIList().initData(task);
                        list.setHeadId(head.getSid());
                        list.setSerialNo(indexForSerialNo[0]++);
                        list.setBussinessType(head.getDataType());
                        list.assembly(ocrList);
                        // 空值处理
                        list.setNullValueFromHead(head);
                        // 基础参数库转换
                        convertBasicParamsI(list);
                    }
                    //箱单提单不做校验
                    //ocrIHeadService.checkOcrList(list);
                    ocrIListMapper.insert(list);
                    // 3.2 处理 invoiceListLoc
                    GwOcrInvoiceIListLoc listLoc;
                    {
                        listLoc = new GwOcrInvoiceIListLoc().initData(task);
                        listLoc.setSid(list.getSid());
                        listLoc.assembly(ocrList);
                    }
                    ocrIListLocMapper.insert(listLoc);
                }
            }
        } catch (OcrDataValidationException ex) {
            LoggerUtil.logError(logger, "OCR 校验错误信息：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        } catch (Exception ex) {
            LoggerUtil.logError(logger, "OCR提单识别数据入库失败：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        }
        return OcrTaskStatusEnum.IDENTITY_SUCCESS;
    }

    /**
     * 插入ocr 出口提单数据
     *
     * @param ocrTDData
     * @param task
     */
    private OcrTaskStatusEnum insertOcrETDData(OcrTDData ocrTDData, GwOcrLog task) throws OcrDataValidationException {
        try {
            // 1 处理 invoiceHead （此处有存在性校验）
            GwOcrInvoiceEHead head;
            {
                head = new GwOcrInvoiceEHead().initData(task);
                // 数据组装
                head.assembly(ocrTDData);
                // 如果已经存在此发票箱单记录 返回
                if (ocrEHeadService.isExistsBill("LADING", head.getMawb(), head.getHawb(), task.getTradeCode())) {
                    return OcrTaskStatusEnum.DATA_REPEAT;
                }
                // 基础参数库转换
                convertBasicParamsE(head);
                head.setDataType("LADING");
            }
            ocrEHeadService.checkOcrHead(head);
            ocrEHeadMapper.insert(head);
            // 2 处理 invoiceHeadLoc
            GwOcrInvoiceEHeadLoc headLoc;
            {
                headLoc = new GwOcrInvoiceEHeadLoc().initData(task);
                // 主键与head表主键 保持一致
                headLoc.setSid(head.getSid());
                headLoc.assembly(ocrTDData);
            }
            ocrEHeadLocMapper.insert(headLoc);
            // 3 处理表体
            if (!CollectionUtils.isEmpty(ocrTDData.getTDCommodity())) {
                List<OcrTDListData> listDatas = ocrTDData.getTDCommodity();
                Integer[] indexForSerialNo = {1};
                for (OcrTDListData ocrList : listDatas) {
                    // 3.1 处理 invoiceList
                    GwOcrInvoiceEList list;
                    {
                        list = new GwOcrInvoiceEList().initData(task);
                        list.setHeadId(head.getSid());
                        list.setSerialNo(indexForSerialNo[0]++);
                        list.setBussinessType(head.getDataType());
                        list.assembly(ocrList);
                        // 空值处理
                        list.setNullValueFromHead(head);
                        // 基础参数库转换
                        convertBasicParamsE(list);
                    }
                    //箱单提单不做校验
                    //ocrEHeadService.checkOcrList(list);
                    ocrEListMapper.insert(list);
                    // 3.2 处理 invoiceListLoc
                    GwOcrInvoiceEListLoc listLoc;
                    {
                        listLoc = new GwOcrInvoiceEListLoc().initData(task);
                        listLoc.setSid(list.getSid());
                        listLoc.assembly(ocrList);
                    }
                    ocrEListLocMapper.insert(listLoc);
                }
            }
        } catch (OcrDataValidationException ex) {
            LoggerUtil.logError(logger, "OCR 校验错误信息：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        } catch (Exception ex) {
            LoggerUtil.logError(logger, "OCR提单识别数据入库失败：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        }
        return OcrTaskStatusEnum.IDENTITY_SUCCESS;
    }

    /**
     * 进口ocr表头基础参数转换
     *
     * @param head
     */
    public void convertBasicParamsI(GwOcrInvoiceIHead head) {
        if (StringUtils.isNotBlank(head.getCurr())) {
            head.setCurrConvert(biCustomerParamsThreadLocal.get().getMapForCurr().get(head.getCurr()));
        }
        if (StringUtils.isNotBlank(head.getUnit())) {
            head.setUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(head.getUnit()));
        }
        if (StringUtils.isNotBlank(head.getNetWtUnit())) {
            head.setNetWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(head.getNetWtUnit()));
        } else {
            head.setNetWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(head.getGrossWtUnit())) {
            head.setGrossWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(head.getGrossWtUnit()));
        } else {
            head.setGrossWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(head.getOriginCountry())) {
            head.setOriginCountryConvert(biCustomerParamsThreadLocal.get().getMapForCountry().get(head.getOriginCountry()));
        }
    }

    /**
     * 出口ocr表头基础参数转换
     *
     * @param head
     */
    public void convertBasicParamsE(GwOcrInvoiceEHead head) {
        if (StringUtils.isNotBlank(head.getCurr())) {
            head.setCurrConvert(biCustomerParamsThreadLocal.get().getMapForCurr().get(head.getCurr()));
        }
        if (StringUtils.isNotBlank(head.getUnit())) {
            head.setUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(head.getUnit()));
        }
        if (StringUtils.isNotBlank(head.getNetWtUnit())) {
            head.setNetWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(head.getNetWtUnit()));
        } else {
            head.setNetWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(head.getGrossWtUnit())) {
            head.setGrossWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(head.getGrossWtUnit()));
        } else {
            head.setGrossWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(head.getOriginCountry())) {
            head.setOriginCountryConvert(biCustomerParamsThreadLocal.get().getMapForCountry().get(head.getOriginCountry()));
        }
    }

    /**
     * 进口ocr表体基础参数转换
     *
     * @param list
     */
    public void convertBasicParamsI(GwOcrInvoiceIList list) {
        if (StringUtils.isNotBlank(list.getCurr())) {
            list.setCurrConvert(biCustomerParamsThreadLocal.get().getMapForCurr().get(list.getCurr()));
        }
        if (StringUtils.isNotBlank(list.getUnit())) {
            list.setUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(list.getUnit()));
        }
        if (StringUtils.isNotBlank(list.getNetWtUnit())) {
            list.setNetWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(list.getNetWtUnit()));
        } else {
            list.setNetWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(list.getGrossWtUnit())) {
            list.setGrossWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(list.getGrossWtUnit()));
        } else {
            list.setGrossWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(list.getOriginCountry())) {
            list.setOriginCountryConvert(biCustomerParamsThreadLocal.get().getMapForCountry().get(list.getOriginCountry()));
        }
        if (StringUtils.isNotBlank(list.getDestinationCountry())) {
            list.setDestinationCountryConvert(biCustomerParamsThreadLocal.get().getMapForCountry().get(list.getDestinationCountry()));
        }
    }

    /**
     * 出口ocr表体基础参数转换
     *
     * @param list
     */
    public void convertBasicParamsE(GwOcrInvoiceEList list) {
        if (StringUtils.isNotBlank(list.getCurr())) {
            list.setCurrConvert(biCustomerParamsThreadLocal.get().getMapForCurr().get(list.getCurr()));
        }
        if (StringUtils.isNotBlank(list.getUnit())) {
            list.setUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(list.getUnit()));
        }
        if (StringUtils.isNotBlank(list.getNetWtUnit())) {
            list.setNetWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(list.getNetWtUnit()));
        } else {
            list.setNetWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(list.getGrossWtUnit())) {
            list.setGrossWtUnitConvert(biCustomerParamsThreadLocal.get().getMapForUnit().get(list.getGrossWtUnit()));
        } else {
            list.setGrossWtUnitConvert("035");
        }
        if (StringUtils.isNotBlank(list.getOriginCountry())) {
            list.setOriginCountryConvert(biCustomerParamsThreadLocal.get().getMapForCountry().get(list.getOriginCountry()));
        }
    }

    /**
     * 插入ocr 报关单数据
     *
     * @param ocrEntryData
     * @param task
     */
    private OcrTaskStatusEnum insertOcrBGDData(OcrEntryData ocrEntryData, GwOcrLog task) throws OcrDataValidationException {
        try {
            UserInfoToken userInfo = new UserInfoToken() {{
                setUserNo(task.getInsertUser());
                setUserName(task.getInsertUserName());
                setCompany(task.getTradeCode());
            }};
            EntryHeadThird head = new EntryHeadThird();
            head.preInsert(userInfo);
            // 数据组装
            head.assembly(ocrEntryData, task.getExtendFiled1());
            head.setDataSource(ConstantsStatus.STATUS_2);
            head.setOcrStatus(ConstantsStatus.STATUS_1);
            head.setEmsListNo(task.getEmsListNo());
            head.setIEMark(task.getExtendFiled1());

            if (StringUtils.isNotBlank(head.getConfirmSpecial())) {
                head.setConfirmSpecialConvert("是".equals(head.getConfirmSpecial()) ? "1" : "9");
            } else {
                head.setConfirmSpecialConvert("");
            }
            if (StringUtils.isNotBlank(head.getConfirmPrice())) {
                head.setConfirmPriceConvert("是".equals(head.getConfirmPrice()) ? "2" : "8");
            } else {
                head.setConfirmPriceConvert("");
            }
            if (StringUtils.isNotBlank(head.getConfirmRoyalties())) {
                head.setConfirmRoyaltiesConvert("是".equals(head.getConfirmRoyalties()) ? "3" : "7");
            } else {
                head.setConfirmRoyaltiesConvert("");
            }
            if (StringUtils.isNotBlank(head.getDutySelf())) {
                head.setDutySelfConvert("是".equals(head.getDutySelf()) ? "4" : "6");
                head.setType("是".equals(head.getDutySelf()) ? "Z" : "");
            } else {
                head.setDutySelfConvert("");
            }
            if (StringUtils.isNotBlank(head.getConfirmFormulaPrice())) {
                head.setConfirmFormulaPriceConvert("是".equals(head.getConfirmFormulaPrice()) ? "A" : "Z");
            } else {
                head.setConfirmFormulaPriceConvert("");
            }
            if (StringUtils.isNotBlank(head.getConfirmTempPrice())) {
                head.setConfirmTempPriceConvert("是".equals(head.getConfirmTempPrice()) ? "B" : "Y");
            } else {
                head.setConfirmTempPriceConvert("");
            }

            String pro=head.getConfirmSpecialConvert() + head.getConfirmPriceConvert() +
                    head.getConfirmRoyaltiesConvert() + head.getConfirmFormulaPriceConvert() +
                    head.getConfirmTempPriceConvert() + head.getDutySelfConvert();
            head.setPromiseItems(DecVariable.fixPromiseItems(pro));
            head.setModifyStatus(ConstantsStatus.STATUS_1);
            entryHeadThirdMapper.insert(head);

            EntryHeadThirdLoc headLoc = new EntryHeadThirdLoc();
            headLoc.assembly(ocrEntryData, task.getExtendFiled1());
            headLoc.preInsert(userInfo);
            headLoc.setSid(head.getSid());
            gwstdEntryHeadThirdLocMapper.insert(headLoc);
            if (CollectionUtils.isNotEmpty(ocrEntryData.getBGDCommodity())) {
                List<EntryListThird> entryListThirds = new ArrayList<>();
                List<EntryListThirdLoc> entryListThirdLocs = new ArrayList<>();
                for (OcrEntryListData ocrEntryListData : ocrEntryData.getBGDCommodity()) {
                    EntryListThird list = new EntryListThird();
                    list.assembly(ocrEntryListData);
                    list.preInsert(userInfo);
                    list.setHeadId(head.getSid());
                    list.setGUnit(convertCode(list.getGUnitConvert(), PCodeType.UNIT));
                    list.setUnit1(convertCode(list.getUnit1Convert(), PCodeType.UNIT));
                    list.setUnit2(convertCode(list.getUnit2Convert(), PCodeType.UNIT));
                    list.setCurr(convertCode(list.getCurrConvert(), PCodeType.CURR_OUTDATED));
                    list.setOriginCountry(convertCode(list.getOriginCountryName(), PCodeType.COUNTRY_OUTDATED));
                    list.setDestinationCountry(convertCode(list.getDestinationCountryName(), PCodeType.COUNTRY_OUTDATED));

                    list.setDistrictCodeName(pCodeHolder.getValue(PCodeType.AREA, list.getDistrictCode()));
                    list.setDistrictPostCodeName(pCodeHolder.getValue(PCodeType.POST_AREA, list.getDistrictPostCode()));
                    entryListThirds.add(list);

                    EntryListThirdLoc listLoc = new EntryListThirdLoc();
                    listLoc.assembly(ocrEntryListData);
                    listLoc.preInsert(userInfo);
                    listLoc.setHeadId(head.getSid());
                    listLoc.setSid(list.getSid());
                    entryListThirdLocs.add(listLoc);
                }
                bulkSqlOpt.batchInsert(entryListThirds, EntryListThirdMapper.class);
                bulkSqlOpt.batchInsert(entryListThirdLocs, GwstdEntryListThirdLocMapper.class);
            }

        } catch (Exception ex) {
            LoggerUtil.logError(logger, "OCR草单识别数据入库失败：" + ex.getMessage(), ex);
            throw new OcrDataValidationException(ex);
        }
        return OcrTaskStatusEnum.IDENTITY_SUCCESS;
    }

    private String convertCode(String str, String type) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return pCodeHolder.getByName(type, str).orElse(null);
    }

    /**
     * 上传进出口资料OCR文件并添加OCR待解析任务
     */
    @SneakyThrows
    public String uploadDecIEOcrFile(MultipartFile[] file, String businessType, String emsListNo, String iemark, UserInfoToken userInfo) {
        int listLen = emsListNo.getBytes(CommonVariable.GBK).length;
        if (listLen > 32) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("单据内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!"));
        }

        String[] arr = {"xls", "xlsx", "pdf", "PDF"};

        //批次id
        String taskId = UUID.randomUUID().toString();
        String theTempPath = tempPath + "/" + taskId + "/";
        //  判断文件夹是否存在
        File dir = new File(theTempPath);
        if (!dir.exists()) {
            dir.mkdir();
        }
        if (file.length > 1) {
            for (MultipartFile mFile : file) {
               String type =  mFile.getOriginalFilename().substring(mFile.getOriginalFilename().lastIndexOf(".")+1);

//                if (!mFile.getOriginalFilename().toLowerCase().endsWith("pdf")) {
                if (!Arrays.asList(arr).contains(type)){
                    FileUtil.deleteFileTree(theTempPath);
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只支持多个pdf文件上传"));
                }
                File tempFile = new File(theTempPath + FileUtil.getValidFileName(mFile.getOriginalFilename()));
                mFile.transferTo(tempFile);
                String subTaskId = UUID.randomUUID().toString();
                this.addAttached(subTaskId, businessType, "OCR", tempFile.getPath(), FileUtil.getValidFileName(mFile.getOriginalFilename()), userInfo);
                // 压缩文件
//                FileUtil.fileToZip(theTempPath, theTempPath, FileUtil.getValidFileName(mFile.getOriginalFilename()));
//                File zipFile = new File(theTempPath + FileUtil.getValidFileName(mFile.getOriginalFilename()) + ".zip");
                File zipFile = FileUtil.zipFile(tempFile, theTempPath);
                uploadToOcrServer(iemark, emsListNo, businessType, taskId, subTaskId, zipFile, userInfo);
            }
        } else {
            File tempFile = new File(theTempPath + FileUtil.getValidFileName(file[0].getOriginalFilename()));
            file[0].transferTo(tempFile);

            String type =  file[0].getOriginalFilename().substring(file[0].getOriginalFilename().lastIndexOf(".")+1);

//            if (file[0].getOriginalFilename().toLowerCase().endsWith("pdf")) {
            if (Arrays.asList(arr).contains(type)){
                String subTaskId = UUID.randomUUID().toString();
                this.addAttached(subTaskId, businessType, "OCR", tempFile.getPath(), FileUtil.getValidFileName(file[0].getOriginalFilename()), userInfo);
                // 压缩文件
                File zipFile = FileUtil.zipFile(tempFile, theTempPath);
                uploadToOcrServer(iemark, emsListNo, businessType, taskId, subTaskId, zipFile, userInfo);
            } else {
                if (!file[0].getOriginalFilename().toLowerCase().endsWith("zip")) {
                    FileUtil.deleteFileTree(theTempPath);
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只支持zip压缩文件上传"));
                }
                //上传的文件格式为zip格式,将zip包解压缩后再处理
                FileUtil.unzipUseZip4j(theTempPath + FileUtil.getValidFileName(file[0].getOriginalFilename()), theTempPath);
                FileUtil.deleteFile(tempFile.getPath());//删除压缩包
                /** 获取本地存储目录下的文件*/
                Collection<File> fileCollection = FileUtil.localListFiles(dir);
                if (CollectionUtils.isNotEmpty(fileCollection)) {
                    for (File localFile : fileCollection) {
//                        String localFilePath = localFile.getPath();
                        String localFilePath = localFile.getPath().substring(localFile.getPath().lastIndexOf(".")+1);
                        if (!Arrays.asList(arr).contains(localFilePath)) {
                            FileUtil.deleteFileTree(theTempPath);
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只支持多个pdf文件上传"));
                        }
                    }
                } else {
                    FileUtil.deleteFileTree(theTempPath);
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("压缩包中不存在文件"));
                }
                for (File localFile : fileCollection) {
                    String subTaskId = UUID.randomUUID().toString();
                    this.addAttached(subTaskId, businessType, "OCR", localFile.getPath(), localFile.getName(), userInfo);
                    // 压缩文件
                    File zipFile = FileUtil.zipFile(localFile, theTempPath);
                    uploadToOcrServer(iemark, emsListNo, businessType, taskId, subTaskId, zipFile, userInfo);
                }
            }
        }
        //最后删除临时目录
        FileUtil.deleteFileTree(theTempPath);
        return taskId;
    }

    /**
     * 一个subTaskId对应一个附件上传
     *
     * @param iemark
     * @param emsListNo
     * @param businessType
     * @param taskId
     * @param zipFile
     * @param userInfo
     * @throws IOException
     */
    private void uploadToOcrServer(String iemark, String emsListNo, String businessType, String taskId, String subTaskId, File zipFile, UserInfoToken userInfo) throws IOException {
        WarringPassConfigDto warringPassConfigDto = warringPassConfigService.selectAll(userInfo).get(0);
        String ocrGroupNo = warringPassConfigDto.getOcrGroupNo();
        if (StringUtils.isBlank(ocrGroupNo)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置OCR识别模板分组号"));
        }

        GwstdHttpConfig httpConfig = httpConfigMapper.selectByType("OCR_UPLOAD");
        if (httpConfig == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置httpConfig: OCR_UPLOAD"));
        }
        // 获取url
        String url = httpConfig.getBaseUrl() + httpConfig.getServiceUrl() + "?sid=" + taskId
                + "&task_code=" + businessType + "&template_type=" + ocrGroupNo + "&task_id=" + taskId;
        Map<String, String> headers = new HashMap<String, String>(2){{
            put("Authorization", userInfo.getAccessToken());
            put("corpCode", userInfo.getCompany());
        }};

        String resultStr = OkHttpUtils.httpPostFile(url, zipFile, headers);
        ResultObject resultObject = JsonObjectMapper.getInstance().fromJson(resultStr, ResultObject.class);
        GwOcrLog log = new GwOcrLog().initData(userInfo);
        log.setTaskId(taskId);
        log.setSubTaskId(subTaskId);
        log.setBussinessType(businessType);
        log.setEmsListNo(emsListNo);
        if (resultObject.isSuccess()) {
            log.setStatus("0");
        } else {
            log.setStatus("1");
            log.setErrorMessage(resultObject.getMessage());
        }
        log.setUploadRes(resultStr);
        log.setExtendFiled1(iemark);
        gwOcrLogMapper.insert(log);
    }

    public Attached addAttached(String subTaskId, String businessType, String acmpType, String filePath, String originalFileName, UserInfoToken userInfo) {
        String fileName = "";
        int repeatCount = 0;
        while (repeatCount < 3) {
            try {
                FileUtil.isValidFileType(filePath);
                fileName = fileHandler.uploadFile(filePath);
                break;
            } catch (Exception e) {
                logger.error(e.getMessage());
                fileName = "";
                repeatCount++;
            }
        }
        if (StringUtils.isBlank(fileName)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("文件上传失败！"));
        }
        //将文件上传到文件服务器
        return this.addInvoiceAttached(subTaskId, businessType, fileName, originalFileName, acmpType, userInfo);
    }

    /**
     * 添加出口附件
     */
    public Attached addInvoiceAttached(String businessSid, String businessType, String fastdUrl, String originFileName, String acmpType, UserInfoToken userInfo) {
        Attached attached = new Attached();
        attached.setSid(UUID.randomUUID().toString());
        attached.setBusinessSid(businessSid);
        attached.setBusinessType(businessType);
        attached.setAcmpType(acmpType);
        attached.setOriginFileName(originFileName);
        attached.setFileName(fastdUrl);
        attached.setTradeCode(userInfo.getCompany());
        attached.setInsertUser(userInfo.getUserNo());
        attached.setInsertTime(new Date());
        attached.setDataSource(CommonEnum.dataSourceEnum.SOURCE_15.getCode());//OCR附件类型
        attachedMapper.insert(attached);
        return attached;
    }
}
