package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 报关单草单表头
 * @author: WJ
 * @createDate: 2022/8/1 16:15
 */
@Data
public class OcrEntryData implements Serializable {
    @ApiModelProperty("报关单号")
    @JsonProperty("BGDCUSTOMS_NO")
    private OcrFieldInfo BGDCUSTOMS_NO;

    @ApiModelProperty("运输方式代码")
    @JsonProperty("BGDTRANS_MDOE_CODE")
    private OcrFieldInfo BGDTRANS_MDOE_CODE;

    @ApiModelProperty("监管方式代码")
    @JsonProperty("BGDTRADE_MODE_CODE")
    private OcrFieldInfo BGDTRADE_MODE_CODE;

    @ApiModelProperty("征免性质代码")
    @JsonProperty("BGDTAX_KIND_CODE")
    private OcrFieldInfo BGDTAX_KIND_CODE;

    @ApiModelProperty("包装种类代码")
    @JsonProperty("BGDPACKAGE_TYPE_CODE")
    private OcrFieldInfo BGDPACKAGE_TYPE_CODE;

    @ApiModelProperty("成交方式代码")
    @JsonProperty("BGDINCOTERMS_CODE")
    private OcrFieldInfo BGDINCOTERMS_CODE;

    @ApiModelProperty("贸易国代码")
    @JsonProperty("BGDTRADE_CNT_CODE")
    private OcrFieldInfo BGDTRADE_CNT_CODE;

    @ApiModelProperty("申报地海关")
    @JsonProperty("BGDDEC_CUSTOMS")
    private OcrFieldInfo BGDDEC_CUSTOMS;

    @ApiModelProperty("申报日期")
    @JsonProperty("BGDAPP_DATE")
    private OcrFieldInfo BGDAPP_DATE;

    @ApiModelProperty("运输工具名称及航次号")
    @JsonProperty("BGDTRANS_NAME")
    private OcrFieldInfo BGDTRANS_NAME;

    @ApiModelProperty("提运单号")
    @JsonProperty("BGDBL_NO")
    private OcrFieldInfo BGDBL_NO;

    @ApiModelProperty("运输方式")
    @JsonProperty("BGDTRANS_MDOE")
    private OcrFieldInfo BGDTRANS_MDOE;

    @ApiModelProperty("监管方式")
    @JsonProperty("BGDTRADE_MODE")
    private OcrFieldInfo BGDTRADE_MODE;

    @ApiModelProperty("征免性质")
    @JsonProperty("BGDTAX_KIND")
    private OcrFieldInfo BGDTAX_KIND;

    @ApiModelProperty("合同协议号")
    @JsonProperty("BGDCONTRACT_NO")
    private OcrFieldInfo BGDCONTRACT_NO;

    @ApiModelProperty("贸易国")
    @JsonProperty("BGDTRADE_COUNTRY")
    private OcrFieldInfo BGDTRADE_COUNTRY;

    @ApiModelProperty("包装种类")
    @JsonProperty("BGDPACKAGE_TYPE")
    private OcrFieldInfo BGDPACKAGE_TYPE;

    @ApiModelProperty("件数")
    @JsonProperty("BGDPACKAGE")
    private OcrFieldInfo BGDPACKAGE;

    @ApiModelProperty("毛重")
    @JsonProperty("BGDTOTAL_GW")
    private OcrFieldInfo BGDTOTAL_GW;

    @ApiModelProperty("净重")
    @JsonProperty("BGDTOTAL_NW")
    private OcrFieldInfo BGDTOTAL_NW;

    @ApiModelProperty("成交方式")
    @JsonProperty("BGDINCOTERMS")
    private OcrFieldInfo BGDINCOTERMS;

    @ApiModelProperty("申报单位统一代码")
    @JsonProperty("BGDDECLARE_CODE")
    private OcrFieldInfo BGDDECLARE_CODE;

    @ApiModelProperty("申报单位名称")
    @JsonProperty("BGDDECLARE_NAME")
    private OcrFieldInfo BGDDECLARE_NAME;

    @ApiModelProperty("特殊关系确认")
    @JsonProperty("BGDCONFIRM_SPECIAL")
    private OcrFieldInfo BGDCONFIRM_SPECIAL;

    @ApiModelProperty("价格影响确认")
    @JsonProperty("BGDCONFIRM_PRICE")
    private OcrFieldInfo BGDCONFIRM_PRICE;

    @ApiModelProperty("支付特许权使用费确认")
    @JsonProperty("BGDCONFIRM_ROYALTIES")
    private OcrFieldInfo BGDCONFIRM_ROYALTIES;

    @ApiModelProperty("公式定价确认")
    @JsonProperty("BGDCONFIRM_FORMULA_PRICE")
    private OcrFieldInfo BGDCONFIRM_FORMULA_PRICE;

    @ApiModelProperty("暂定价格确认")
    @JsonProperty("BGDCONFIRM_TEMP_PRICE")
    private OcrFieldInfo BGDCONFIRM_TEMP_PRICE;

    @ApiModelProperty("自报自缴")
    @JsonProperty("BGDDUTY_SELF")
    private OcrFieldInfo BGDDUTY_SELF;

    @ApiModelProperty("备案号")
    @JsonProperty("BGDMANUAL_NO")
    private OcrFieldInfo BGDMANUAL_NO;

    @ApiModelProperty("许可证号")
    @JsonProperty("BGDLICENSE_NO")
    private OcrFieldInfo BGDLICENSE_NO;

    @ApiModelProperty("随附单证及编号")
    @JsonProperty("BGDACMP_NO")
    private OcrFieldInfo BGDACMP_NO;

    @ApiModelProperty("运费")
    @JsonProperty("BGDFREIGHT")
    private OcrFieldInfo BGDFREIGHT;

    @ApiModelProperty("保费")
    @JsonProperty("BGDINSURANCE")
    private OcrFieldInfo BGDINSURANCE;

    @ApiModelProperty("杂费 ")
    @JsonProperty("BGDOTHER_FEE")
    private OcrFieldInfo BGDOTHER_FEE;

    @ApiModelProperty("标记唛码及备注")
    @JsonProperty("BGDNOTE")
    private OcrFieldInfo BGDNOTE;

    /**
     * 进口
     */
    @ApiModelProperty("进口日期")
    @JsonProperty("BGDIMPORT_DATE")
    private OcrFieldInfo BGDIMPORT_DATE;

    @ApiModelProperty("货物存放地点")
    @JsonProperty("BGDSTORAGE_PLACE")
    private OcrFieldInfo BGDSTORAGE_PLACE;

    @ApiModelProperty("启运港名称")
    @JsonProperty("BGDTRAN_PORT")
    private OcrFieldInfo BGDTRAN_PORT;

    @ApiModelProperty("启运港")
    @JsonProperty("BGDTRAN_PORT_CODE")
    private OcrFieldInfo BGDTRAN_PORT_CODE;

    @ApiModelProperty("经停港名称")
    @JsonProperty("BGDDEPT_PORT")
    private OcrFieldInfo BGDDEPT_PORT;

    @ApiModelProperty("经停港")
    @JsonProperty("BGDDEPT_PORT_CODE")
    private OcrFieldInfo BGDDEPT_PORT_CODE;

    @ApiModelProperty("进境关别名称")
    @JsonProperty("BGDIMPORT_CUSTOMS")
    private OcrFieldInfo BGDIMPORT_CUSTOMS;

    @ApiModelProperty("进境关别")
    @JsonProperty("BGDIMPORT_CUS_CODE")
    private OcrFieldInfo BGDIMPORT_CUS_CODE;

    @ApiModelProperty("消费使用单位名称")
    @JsonProperty("BGDOWNER_NAME")
    private OcrFieldInfo BGDOWNER_NAME;

    @ApiModelProperty("消费使用单位")
    @JsonProperty("BGDOWNER_CODE")
    private OcrFieldInfo BGDOWNER_CODE;

    @ApiModelProperty("入境口岸名称")
    @JsonProperty("BGDENTRY_PORT")
    private OcrFieldInfo BGDENTRY_PORT;

    @ApiModelProperty("入境口岸")
    @JsonProperty("BGDENTRY_PORT_CODE")
    private OcrFieldInfo BGDENTRY_PORT_CODE;

    @ApiModelProperty("启运国（地区）名称")
    @JsonProperty("BGDDEPT_COUNTRY")
    private OcrFieldInfo BGDDEPT_COUNTRY;

    @ApiModelProperty("启运国（地区）")
    @JsonProperty("BGDDEPT_COUNTRY_CODE")
    private OcrFieldInfo BGDDEPT_COUNTRY_CODE;

    @ApiModelProperty("境外收货人")
    @JsonProperty("BGDCONSIGNEE_CODE")
    private OcrFieldInfo BGDCONSIGNEE_CODE;

    @ApiModelProperty("境外收货人")
    @JsonProperty("BGDCONSIGNEE")
    private OcrFieldInfo BGDCONSIGNEE;

    @ApiModelProperty("境内发货人")
    @JsonProperty("BGDSHIPPER_CODE")
    private OcrFieldInfo BGDSHIPPER_CODE;

    @ApiModelProperty("境内发货人")
    @JsonProperty("BGDSHIPPER")
    private OcrFieldInfo BGDSHIPPER;

    /**
     * 出口
     */
    @ApiModelProperty("出口日期")
    @JsonProperty("BGDEXPORT_DATE")
    private OcrFieldInfo BGDEXPORT_DATE;

    @ApiModelProperty("生产销售单位代码")
    @JsonProperty("BGDMANUFACTURE_CODE")
    private OcrFieldInfo BGDMANUFACTURE_CODE;

    @ApiModelProperty("生产销售单位")
    @JsonProperty("BGDMANUFACTURE")
    private OcrFieldInfo BGDMANUFACTURE;

    @ApiModelProperty("指运港代码")
    @JsonProperty("BGDDEST_PORT_CODE")
    private OcrFieldInfo BGDDEST_PORT_CODE;

    @ApiModelProperty("指运港")
    @JsonProperty("BGDDEST_PORT")
    private OcrFieldInfo BGDDEST_PORT;

    @ApiModelProperty("出境关别代码")
    @JsonProperty("BGDEXPORT_CUS_CODE")
    private OcrFieldInfo BGDEXPORT_CUS_CODE;

    @ApiModelProperty("出境关别")
    @JsonProperty("BGDEXPORT_CUSTOMS")
    private OcrFieldInfo BGDEXPORT_CUSTOMS;

    @ApiModelProperty("离境口岸代码")
    @JsonProperty("BGDDEP_PORT_CODE")
    private OcrFieldInfo BGDDEP_PORT_CODE;

    @ApiModelProperty("离境口岸")
    @JsonProperty("BGDDEP_PORT")
    private OcrFieldInfo BGDDEP_PORT;

    @ApiModelProperty("运抵国代码")
    @JsonProperty("BGDDEST_COUNTRY_CODE")
    private OcrFieldInfo BGDDEST_COUNTRY_CODE;

    @ApiModelProperty("运抵国")
    @JsonProperty("BGDDEST_COUNTRY")
    private OcrFieldInfo BGDDEST_COUNTRY;

    @ApiModelProperty("统一编号")
    @JsonProperty("BGDSEQ_NO")
    private OcrFieldInfo BGDSEQ_NO;

    /**
     * 表体
     */
    @JsonProperty("BGDCommodity")
    private List<OcrEntryListData> BGDCommodity;
}