package com.dcjet.cs.ocrInvoice.model;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListLocDto;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrFPListData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrTDListData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrXDListData;
import com.dcjet.cs.util.ExtractNumUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Setter
@Getter
@Table(name = "t_gw_ocr_invoice_e_list")
public class GwOcrInvoiceEList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private String sid;
	/**
     * 流水号
     */
	@Column(name = "serial_no")
	private Integer serialNo;
	/**
     * 订单号
     */
	@Column(name = "order_no")
	private String orderNo;
	/**
     * 订单序号
     */
	@Column(name = "order_serial_no")
	private String orderSerialNo;
	/**
     * 企业料号
     */
	@Column(name = "fac_g_no")
	private String facGNo;
	/**
     * 商品描述
     */
	@Column(name = "good_desc")
	private String goodDesc;
	/**
     * 交易数量
     */
	@Column(name = "qty")
	private BigDecimal qty;
	/**
     * 交易单位
     */
	@Column(name = "unit")
	private String unit;
	/**
     * 转换后单位
     */
	@Column(name = "unit_convert")
	private String unitConvert;
	/**
     * 法一数量
     */
	@Column(name = "qty_1")
	private BigDecimal qty1;
	/**
     * 单价
     */
	@Column(name = "dec_price")
	private BigDecimal decPrice;
	/**
     * 百个单价
     */
	@Column(name = "dec_price_h")
	private BigDecimal decPriceH;
	/**
     * 千个单价
     */
	@Column(name = "dec_price_t")
	private BigDecimal decPriceT;
	/**
     * 申报总价
     */
	@Column(name = "dec_total")
	private BigDecimal decTotal;
	/**
     * 币制
     */
	@Column(name = "curr")
	private String curr;
	/**
     * 转换后币制
     */
	@Column(name = "curr_convert")
	private String currConvert;
	/**
     * 净重
     */
	@Column(name = "net_wt")
	private BigDecimal netWt;
	/**
     * 净重单位
     */
	@Column(name = "net_wt_unit")
	private String netWtUnit;
	/**
     * 总毛重
     */
	@Column(name = "gross_wt")
	private BigDecimal grossWt;
	/**
     * 毛重单位
     */
	@Column(name = "gross_wt_unit")
	private String grossWtUnit;
	/**
     * 原产国
     */
	@Column(name = "origin_country")
	private String originCountry;
	/**
     * 转换后原产国
     */
	@Column(name = "origin_country_convert")
	private String originCountryConvert;
	/**
     * 件数
     */
	@Column(name = "pack_num")
	private Integer packNum;
	/**
     * 包装种类
     */
	@Column(name = "wrap_type")
	private String wrapType;
	/**
     * 包装编号
     */
	@Column(name = "wrap_no")
	private String wrapNo;
	/**
     * 发票箱单表头的SID
     */
	@Column(name = "head_id")
	private String headId;
	/**
     * 业务类型(INVOICE.发票 PACKING.箱单)
     */
	@Column(name = "bussiness_type")
	private String bussinessType;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private Date insertTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private String updateUser;
	/**
     * 修改人名称
     */
	@Column(name = "update_user_name")
	private String updateUserName;
	/**
     * 修改数据时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private Date updateTime;
	/**
     * 转换后净重单位
     */
	@Column(name = "net_wt_unit_convert")
	private String netWtUnitConvert;
	/**
     * 转换后毛重单位
     */
	@Column(name = "gross_wt_unit_convert")
	private String grossWtUnitConvert;

	@Column(name = "task_id")
	private String taskId;

	@Transient
	private Boolean needLoc;

	@Transient
	private GwOcrInvoiceIListLocDto loc;

	public GwOcrInvoiceEList initData(GwOcrLog log) {
		this.sid = UUID.randomUUID().toString();
		this.tradeCode = log.getTradeCode();
		this.insertUser = log.getInsertUser();
		this.insertUserName = log.getInsertUserName();
		this.insertTime = new Date();

		this.taskId = log.getTaskId();

		return this;
	}

	public void setNullValueFromHead(GwOcrInvoiceEHead head) {
		if (StringUtils.isEmpty(this.orderNo)) {
			this.orderNo = head.getOrderNo();
		}
		if (StringUtils.isEmpty(this.curr)) {
			this.curr = head.getCurr();
		}
		if (StringUtils.isEmpty(this.originCountry)) {
			this.originCountry = head.getOriginCountry();
		}
	}

	public void assembly(OcrFPListData ocrList) {

		if (ocrList.getFPCommodity_PO() != null) {
			this.setOrderNo(ocrList.getFPCommodity_PO().getValue());
		}
		if (ocrList.getFPCommodity_PO_Item() != null) {
			this.setOrderSerialNo(ocrList.getFPCommodity_PO_Item().getValue());
		}
		if (ocrList.getFPCommodity_Gds_PartNo() != null) {
			this.setFacGNo(ocrList.getFPCommodity_Gds_PartNo().getValue());
		}
		if (ocrList.getFPCommodity_Gds_Desc() != null) {
			this.setGoodDesc(ocrList.getFPCommodity_Gds_Desc().getValue());
		}
		if (ocrList.getFPCommodity_Gds_Qty() != null) {
			this.setQty(ExtractNumUtil.extractBigDecimal(ocrList.getFPCommodity_Gds_Qty().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_Unit() != null) {
			this.setUnit(ocrList.getFPCommodity_Gds_Unit().getValue());
		}
		if (ocrList.getFPCommodity_Gds_Price() != null) {
			this.setDecPrice(ExtractNumUtil.extractBigDecimal(ocrList.getFPCommodity_Gds_Price().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_PriceHundred() != null) {
			this.setDecPriceH(ExtractNumUtil.extractBigDecimal(ocrList.getFPCommodity_Gds_PriceHundred().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_PriceThousand() != null) {
			this.setDecPriceT(ExtractNumUtil.extractBigDecimal(ocrList.getFPCommodity_Gds_PriceThousand().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_AMT() != null) {
			this.setDecTotal(ExtractNumUtil.extractBigDecimal(ocrList.getFPCommodity_Gds_AMT().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_Curr() != null) {
			this.setCurr(ocrList.getFPCommodity_Gds_Curr().getValue());
		}
		if (ocrList.getFPCommodity_Gds_NW() != null) {
			this.setNetWt(ExtractNumUtil.extractBigDecimal(ocrList.getFPCommodity_Gds_NW().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_NWUnit() != null) {
			this.setNetWtUnit(ocrList.getFPCommodity_Gds_NWUnit().getValue());
		}
		if (ocrList.getFPCommodity_Gds_GW() != null) {
			this.setGrossWt(ExtractNumUtil.extractBigDecimal(ocrList.getFPCommodity_Gds_GW().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_GWUnit() != null) {
			this.setGrossWtUnit(ocrList.getFPCommodity_Gds_GWUnit().getValue());
		}
		if (ocrList.getFPCommodity_Gds_COO() != null) {
			this.setOriginCountry(ocrList.getFPCommodity_Gds_COO().getValue());
		}
		if (ocrList.getFPCommodity_Gds_PkgQty() != null) {
			this.setPackNum(ExtractNumUtil.extractInteger(ocrList.getFPCommodity_Gds_PkgQty().getValue()));
		}
		if (ocrList.getFPCommodity_Gds_PkgUom() !=  null) {
			this.setWrapType(ocrList.getFPCommodity_Gds_PkgUom().getValue());
		}
		if (ocrList.getFPCommodity_Gds_PkgNo() != null) {
			this.setWrapNo(ocrList.getFPCommodity_Gds_PkgNo().getValue());
		}

	}

	public void assembly(OcrXDListData ocrList) {

		if (ocrList.getXDCommodity_PO() != null) {
			this.setOrderNo(ocrList.getXDCommodity_PO().getValue());
		}
		if (ocrList.getXDCommodity_PO_Item() != null) {
			this.setOrderSerialNo(ocrList.getXDCommodity_PO_Item().getValue());
		}
		if (ocrList.getXDCommodity_Gds_PartNo() != null) {
			this.setFacGNo(ocrList.getXDCommodity_Gds_PartNo().getValue());
		}
		if (ocrList.getXDCommodity_Gds_Desc() != null) {
			this.setGoodDesc(ocrList.getXDCommodity_Gds_Desc().getValue());
		}
		if (ocrList.getXDCommodity_Gds_Qty() != null) {
			this.setQty(ExtractNumUtil.extractBigDecimal(ocrList.getXDCommodity_Gds_Qty().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_Unit() != null) {
			this.setUnit(ocrList.getXDCommodity_Gds_Unit().getValue());
		}
		if (ocrList.getXDCommodity_Gds_Price() != null) {
			this.setDecPrice(ExtractNumUtil.extractBigDecimal(ocrList.getXDCommodity_Gds_Price().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_PriceHundred() != null) {
			this.setDecPriceH(ExtractNumUtil.extractBigDecimal(ocrList.getXDCommodity_Gds_PriceHundred().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_PriceThousand() != null) {
			this.setDecPriceT(ExtractNumUtil.extractBigDecimal(ocrList.getXDCommodity_Gds_PriceThousand().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_AMT() != null) {
			this.setDecTotal(ExtractNumUtil.extractBigDecimal(ocrList.getXDCommodity_Gds_AMT().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_Curr() != null) {
			this.setCurr(ocrList.getXDCommodity_Gds_Curr().getValue());
		}
		if (ocrList.getXDCommodity_Gds_NW() != null) {
			this.setNetWt(ExtractNumUtil.extractBigDecimal(ocrList.getXDCommodity_Gds_NW().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_NWUnit() != null) {
			this.setNetWtUnit(ocrList.getXDCommodity_Gds_NWUnit().getValue());
		}
		if (ocrList.getXDCommodity_Gds_GW() != null) {
			this.setGrossWt(ExtractNumUtil.extractBigDecimal(ocrList.getXDCommodity_Gds_GW().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_GWUnit() != null) {
			this.setGrossWtUnit(ocrList.getXDCommodity_Gds_GWUnit().getValue());
		}
		if (ocrList.getXDCommodity_Gds_COO() != null) {
			this.setOriginCountry(ocrList.getXDCommodity_Gds_COO().getValue());
		}
		if (ocrList.getXDCommodity_Gds_PkgQty() != null) {
			this.setPackNum(ExtractNumUtil.extractInteger(ocrList.getXDCommodity_Gds_PkgQty().getValue()));
		}
		if (ocrList.getXDCommodity_Gds_PkgUom() !=  null) {
			this.setWrapType(ocrList.getXDCommodity_Gds_PkgUom().getValue());
		}
		if (ocrList.getXDCommodity_Gds_PkgNo() != null) {
			this.setWrapNo(ocrList.getXDCommodity_Gds_PkgNo().getValue());
		}

	}

	public void assembly(OcrTDListData ocrList) {

		if (ocrList.getTDCommodity_PO() != null) {
			this.setOrderNo(ocrList.getTDCommodity_PO().getValue());
		}
		if (ocrList.getTDCommodity_PO_Item() != null) {
			this.setOrderSerialNo(ocrList.getTDCommodity_PO_Item().getValue());
		}
		if (ocrList.getTDCommodity_Gds_PartNo() != null) {
			this.setFacGNo(ocrList.getTDCommodity_Gds_PartNo().getValue());
		}
		if (ocrList.getTDCommodity_Gds_Desc() != null) {
			this.setGoodDesc(ocrList.getTDCommodity_Gds_Desc().getValue());
		}
		if (ocrList.getTDCommodity_Gds_Qty() != null) {
			this.setQty(ExtractNumUtil.extractBigDecimal(ocrList.getTDCommodity_Gds_Qty().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_Unit() != null) {
			this.setUnit(ocrList.getTDCommodity_Gds_Unit().getValue());
		}
		if (ocrList.getTDCommodity_Gds_Price() != null) {
			this.setDecPrice(ExtractNumUtil.extractBigDecimal(ocrList.getTDCommodity_Gds_Price().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_PriceHundred() != null) {
			this.setDecPriceH(ExtractNumUtil.extractBigDecimal(ocrList.getTDCommodity_Gds_PriceHundred().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_PriceThousand() != null) {
			this.setDecPriceT(ExtractNumUtil.extractBigDecimal(ocrList.getTDCommodity_Gds_PriceThousand().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_AMT() != null) {
			this.setDecTotal(ExtractNumUtil.extractBigDecimal(ocrList.getTDCommodity_Gds_AMT().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_Curr() != null) {
			this.setCurr(ocrList.getTDCommodity_Gds_Curr().getValue());
		}
		if (ocrList.getTDCommodity_Gds_NW() != null) {
			this.setNetWt(ExtractNumUtil.extractBigDecimal(ocrList.getTDCommodity_Gds_NW().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_NWUnit() != null) {
			this.setNetWtUnit(ocrList.getTDCommodity_Gds_NWUnit().getValue());
		}
		if (ocrList.getTDCommodity_Gds_GW() != null) {
			this.setGrossWt(ExtractNumUtil.extractBigDecimal(ocrList.getTDCommodity_Gds_GW().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_GWUnit() != null) {
			this.setGrossWtUnit(ocrList.getTDCommodity_Gds_GWUnit().getValue());
		}
		if (ocrList.getTDCommodity_Gds_COO() != null) {
			this.setOriginCountry(ocrList.getTDCommodity_Gds_COO().getValue());
		}
		if (ocrList.getTDCommodity_Gds_PkgQty() != null) {
			this.setPackNum(ExtractNumUtil.extractInteger(ocrList.getTDCommodity_Gds_PkgQty().getValue()));
		}
		if (ocrList.getTDCommodity_Gds_PkgUom() !=  null) {
			this.setWrapType(ocrList.getTDCommodity_Gds_PkgUom().getValue());
		}
		if (ocrList.getTDCommodity_Gds_PkgNo() != null) {
			this.setWrapNo(ocrList.getTDCommodity_Gds_PkgNo().getValue());
		}

	}

}
