package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.GwOcrLogDetailDto;
import com.dcjet.cs.dto.ocrInvoice.GwOcrLogDetailParam;
import com.dcjet.cs.ocrInvoice.dao.GwOcrLogDetailMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrLogDetailDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrLogDetail;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Service
public class GwOcrLogDetailService extends BaseService<GwOcrLogDetail> {
    @Resource
    private GwOcrLogDetailMapper gwOcrLogDetailMapper;
    @Resource
    private GwOcrLogDetailDtoMapper gwOcrLogDetailDtoMapper;
    @Override
    public Mapper<GwOcrLogDetail> getMapper() {
        return gwOcrLogDetailMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrLogDetailParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwOcrLogDetailDto>> getListPaged(GwOcrLogDetailParam gwOcrLogDetailParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrLogDetail gwOcrLogDetail = gwOcrLogDetailDtoMapper.toPo(gwOcrLogDetailParam);
        Page<GwOcrLogDetail> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwOcrLogDetailMapper.getList(gwOcrLogDetail));
        List<GwOcrLogDetailDto> gwOcrLogDetailDtos = page.getResult().stream().map(head -> {
            GwOcrLogDetailDto dto = gwOcrLogDetailDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrLogDetailDto>> paged = ResultObject.createInstance(gwOcrLogDetailDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrLogDetailParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrLogDetailDto insert(GwOcrLogDetailParam gwOcrLogDetailParam, UserInfoToken userInfo) {
        GwOcrLogDetail gwOcrLogDetail = gwOcrLogDetailDtoMapper.toPo(gwOcrLogDetailParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrLogDetail.setSid(sid);
        gwOcrLogDetail.setInsertUser(userInfo.getUserNo());
        gwOcrLogDetail.setInsertTime(new Date());
        // 新增数据
        int insertStatus = gwOcrLogDetailMapper.insert(gwOcrLogDetail);
        return  insertStatus > 0 ? gwOcrLogDetailDtoMapper.toDto(gwOcrLogDetail) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwOcrLogDetailParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrLogDetailDto update(GwOcrLogDetailParam gwOcrLogDetailParam, UserInfoToken userInfo) {
        GwOcrLogDetail gwOcrLogDetail = gwOcrLogDetailMapper.selectByPrimaryKey(gwOcrLogDetailParam.getSid());
        gwOcrLogDetailDtoMapper.updatePo(gwOcrLogDetailParam, gwOcrLogDetail);
        gwOcrLogDetail.setUpdateUser(userInfo.getUserNo());
        gwOcrLogDetail.setUpdateTime(new Date());
        // 更新数据
        int update = gwOcrLogDetailMapper.updateByPrimaryKey(gwOcrLogDetail);
        return update > 0 ? gwOcrLogDetailDtoMapper.toDto(gwOcrLogDetail) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		gwOcrLogDetailMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrLogDetailDto> selectAll(GwOcrLogDetailParam exportParam, UserInfoToken userInfo) {
        GwOcrLogDetail gwOcrLogDetail = gwOcrLogDetailDtoMapper.toPo(exportParam);
        // gwOcrLogDetail.setTradeCode(userInfo.getCompany());
        List<GwOcrLogDetailDto> gwOcrLogDetailDtos = new ArrayList<>();
        List<GwOcrLogDetail> gwOcrLogDetails = gwOcrLogDetailMapper.getList(gwOcrLogDetail);
        if (CollectionUtils.isNotEmpty(gwOcrLogDetails)) {
            gwOcrLogDetailDtos = gwOcrLogDetails.stream().map(head -> {
                GwOcrLogDetailDto dto = gwOcrLogDetailDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrLogDetailDtos;
    }
}
