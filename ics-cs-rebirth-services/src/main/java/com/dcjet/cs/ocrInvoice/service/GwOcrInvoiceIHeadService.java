package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.dao.BiCustomerParamsMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.ocrInvoice.OcrInvoiceSumInfo;
import com.dcjet.cs.dto.ocrInvoice.ocrI.*;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.erp.model.DecErpIListN;
import com.dcjet.cs.erp.service.DecCommonService;
import com.dcjet.cs.erp.service.DecErpIListNService;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.ocrInvoice.component.exp.OcrDataValidationException;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceIHeadMapper;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceIListMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceIHeadDtoMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceIListDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHead;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.FileUtil;
import com.dcjet.cs.util.ValidatorUtil;
import com.dcjet.cs.util.pageSort.DcPageSort;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.dcjet.cs.util.FunctionUtil.distinctByKey;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Service
public class GwOcrInvoiceIHeadService extends BaseService<GwOcrInvoiceIHead> {

    @Resource
    private GwOcrInvoiceIHeadMapper mapper;
    @Resource
    private GwOcrInvoiceIHeadDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceIHead> getMapper() {
        return mapper;
    }
    @Resource
    private GwOcrInvoiceIListMapper listMapper;
    @Resource
    private GwOcrInvoiceIListDtoMapper listDtoMapper;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecCommonService decCommonService;
    @Resource
    private BiCustomerParamsMapper biCustomerParamsMapper;
    @Resource
    private GwOcrInvoiceIEService gwOcrInvoiceIEService;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Value("${dc.export.temp:}")
    private String tempPath;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    private static Map<String, Field> mapColumn = new LinkedHashMap<>();
    static {
        try {
            mapColumn.put("总数量", GwOcrInvoiceIHead.class.getDeclaredField("qty"));
            mapColumn.put("总金额", GwOcrInvoiceIHead.class.getDeclaredField("totalAmount"));
            mapColumn.put("总件数", GwOcrInvoiceIHead.class.getDeclaredField("packNum"));
            mapColumn.put("总体积", GwOcrInvoiceIHead.class.getDeclaredField("volume"));
            mapColumn.put("总净重", GwOcrInvoiceIHead.class.getDeclaredField("netWt"));
            mapColumn.put("总毛重", GwOcrInvoiceIHead.class.getDeclaredField("grossWt"));
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    };
    @Resource
    private ValidatorUtil validatorUtil;
    @Resource
    private DecErpIListNService decErpIListNService;
    @Resource
    private FastdFsService fastdFsService;

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceIHeadQueryParam
     * @param pageParam
     * @return
     */
    @SneakyThrows
    @DcPageSort
    public ResultObject<List<GwOcrInvoiceIHeadDto>> getListPaged(GwOcrInvoiceIHeadQueryParam gwOcrInvoiceIHeadQueryParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceIHead gwOcrInvoiceIHead = dtoMapper.toPo(gwOcrInvoiceIHeadQueryParam);
        Page<GwOcrInvoiceIHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceIHead));
        List<GwOcrInvoiceIHeadDto> gwOcrInvoiceIHeadDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceIHeadDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        // 设置businessNo
        gwOcrInvoiceIHeadDtos.stream().forEach(e-> e.setBusinessNoByDataType());
		ResultObject<List<GwOcrInvoiceIHeadDto>> paged = ResultObject.createInstance(gwOcrInvoiceIHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceIHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIHeadDto insert(GwOcrInvoiceIHeadParam gwOcrInvoiceIHeadParam, UserInfoToken userInfo) {
        GwOcrInvoiceIHead gwOcrInvoiceIHead = dtoMapper.toPo(gwOcrInvoiceIHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceIHead.setSid(sid);
        gwOcrInvoiceIHead.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceIHead.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceIHead);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceIHead) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceIHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIHeadDto update(GwOcrInvoiceIHeadParam gwOcrInvoiceIHeadParam, UserInfoToken userInfo) {
        GwOcrInvoiceIHead head = mapper.selectByPrimaryKey(gwOcrInvoiceIHeadParam.getSid());
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据已删除，请重新查询"));
        }
        if ("2".equals(head.getCreateStatus())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已生成状态的数据不可编辑"));
        }
        dtoMapper.updatePo(gwOcrInvoiceIHeadParam, head);
        if (checkRepeatForUpdate(head)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据重复"));
        }
        head.setUpdateUser(userInfo.getUserNo());
        head.setUpdateUserName(userInfo.getUserName());
        head.setUpdateTime(new Date());
        head.setModifyStatus("2");
        //企业参数库转换
        gwOcrInvoiceIEService.setBiCustomerParams(userInfo);
        gwOcrInvoiceIEService.convertBasicParamsI(head);
        // 更新数据
        int update = mapper.updateByPrimaryKey(head);
        return update > 0 ? dtoMapper.toDto(head) : null;
    }

    private boolean checkRepeatForUpdate(GwOcrInvoiceIHead gwOcrInvoiceIHead) {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeCode", gwOcrInvoiceIHead.getTradeCode());
        criteria.andNotEqualTo("sid", gwOcrInvoiceIHead.getSid());
        String dataType = gwOcrInvoiceIHead.getDataType();
        criteria.andEqualTo("dataType", dataType);
        if ("INVOICE".equals(dataType) || "PACKING".equals(dataType)) {
            criteria.andEqualTo("invoiceNo", gwOcrInvoiceIHead.getInvoiceNo());
        } else if ("LADING".equals(dataType)) {
            criteria.andEqualTo("mawb", gwOcrInvoiceIHead.getMawb());
            if (StringUtils.isNotEmpty(gwOcrInvoiceIHead.getHawb())) {
                criteria.andEqualTo("hawb", gwOcrInvoiceIHead.getHawb());
            }
        } else {
            criteria.andCondition("1=2");
        }
        int cnt = mapper.selectCountByExample(example);
        return cnt > 0;
    }


    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) throws Exception {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sid", sids);
        criteria.andNotEqualTo("createStatus", "1");
        int cnt = mapper.selectCountByExample(example);
        if (cnt > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有状态为[已识别]的数据才可以删除"));
        }

        List<String> listSids = mapper.selectSubTaskId(sids);
        // 删除附件
        Example example2 = new Example(Attached.class);
        Example.Criteria criteria2 = example2.createCriteria();
        criteria2.andIn("businessSid", listSids);
        criteria2.andEqualTo("tradeCode", userInfo.getCompany());
        List<Attached> attachedList = attachedMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(attachedList)) {
            for (Attached attached : attachedList) {
                fileHandler.deleteFile(attached.getFileName());
                attachedMapper.deleteByPrimaryKey(attached.getSid());
            }
        }
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceIHeadDto> selectAll(GwOcrInvoiceIHeadQueryParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceIHead gwOcrInvoiceIHead = dtoMapper.toPo(exportParam);
        gwOcrInvoiceIHead.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceIHeadDto> gwOcrInvoiceIHeadDtos = new ArrayList<>();
        List<GwOcrInvoiceIHead> gwOcrInvoiceIHeads = mapper.getList(gwOcrInvoiceIHead);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceIHeads)) {
            gwOcrInvoiceIHeadDtos = gwOcrInvoiceIHeads.stream().map(head -> {
                GwOcrInvoiceIHeadDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceIHeadDtos;
    }

    /**
     * getOne
     * @param gwOcrInvoiceIHeadQueryParam
     * @return
     */
    public GwOcrInvoiceIHeadDto getOne(GwOcrInvoiceIHeadQueryParam gwOcrInvoiceIHeadQueryParam) {
        // 启用分页查询
        GwOcrInvoiceIHead gwOcrInvoiceIHead = dtoMapper.toPo(gwOcrInvoiceIHeadQueryParam);
        List<GwOcrInvoiceIHeadDto> gwOcrInvoiceIHeadDtos = mapper.getList(gwOcrInvoiceIHead).stream().map(head -> {
            GwOcrInvoiceIHeadDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(gwOcrInvoiceIHeadDtos)) {
            return null;
        }
        return gwOcrInvoiceIHeadDtos.get(0);
    }

    /**
     * 是否已存在发票、箱单
     * @param dataType
     * @param invoiceNo
     * @return
     */
    public Boolean isExistsInvoice(String dataType, String invoiceNo, String tradeCode) {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeCode", tradeCode);
        criteria.andEqualTo("dataType", dataType);
        criteria.andEqualTo("invoiceNo", invoiceNo);
        int cnt = mapper.selectCountByExample(example);
        return cnt > 0;
    }

    /**
     * 是否存在提单
     * @param dataType
     * @param mawb 主提运单号
     * @param hawb 分提运单号
     * @param tradeCode
     * @return
     */
    public Boolean isExistsBill(String dataType, String mawb, String hawb, String tradeCode) {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeCode", tradeCode);
        criteria.andEqualTo("dataType", dataType);
        criteria.andEqualTo("mawb", mawb);
        if (StringUtils.isNotEmpty(hawb)) {
            criteria.andEqualTo("hawb", hawb);
        }
        int cnt = mapper.selectCountByExample(example);
        return cnt > 0;
    }

    /**
     * 功能描述 ocr识别数据进口预录入单信息
     * <AUTHOR>
     * @date 2021-10-09
     * @version 1.0
     * @param gwOcrIHeadDecParam 1
     * @param userInfo 2
     * @return void
    */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<DecErpIHeadN> createDec(GwOcrIHeadDecParam gwOcrIHeadDecParam, UserInfoToken userInfo) throws Exception {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("生成成功"));
        List<GwOcrInvoiceIHead> gwOcrInvoiceIHeads = mapper.selectBySids(gwOcrIHeadDecParam.getSids());
        List<String> invoiceListSid = gwOcrInvoiceIHeads.stream().filter(x->"INVOICE".equals(x.getDataType())).map(GwOcrInvoiceIHead::getSid).collect(Collectors.toList());
        List<String> packingListSid = gwOcrInvoiceIHeads.stream().filter(x->"PACKING".equals(x.getDataType())).map(GwOcrInvoiceIHead::getSid).collect(Collectors.toList());
        List<GwOcrInvoiceIList> gwOcrInvoiceILists = new ArrayList<>();
        List<GwOcrInvoiceIList> gwOcrPackingILists = new ArrayList<>();
        if(invoiceListSid.size()>0) {
            gwOcrInvoiceILists = listMapper.selectByHeadIds(invoiceListSid);
        }
        if(packingListSid.size()>0) {
            gwOcrPackingILists = listMapper.selectByHeadIds(packingListSid);
        }
        if(gwOcrInvoiceIHeads.size() > 0) {
            List<GwOcrInvoiceIHead> createList = gwOcrInvoiceIHeads.stream().filter(x->"2".equals(x.getCreateStatus())).collect(Collectors.toList());
            if(createList.size()>0) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("选中的发票中有已生成数据，无法生成"));
            }
            List<GwOcrInvoiceIHead> createList2 = gwOcrInvoiceIHeads.stream().filter(e -> StringUtils.isNotBlank(e.getEmsListNo())).filter(distinctByKey(GwOcrInvoiceIHead::getEmsListNo)).collect(Collectors.toList());
            if(createList2.size()>1) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("存在不同单据内部编号的发票/箱单，生成失败！"));
            }
            List<GwOcrInvoiceIHead> decList = gwOcrInvoiceIHeads.stream().filter(x->"LADING".equals(x.getDataType())).collect(Collectors.toList());
            if(decList.size() > 1) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("不允许选择多份提单生成，生成失败！"));
            }
            List<GwOcrInvoiceIHead> gwOcrInvoiceIHeadSums = mapper.selectSumBySids(gwOcrIHeadDecParam.getSids());
            String invoiceNoList = gwOcrInvoiceIHeads.stream().filter(x->x.getInvoiceNo() != null).map(GwOcrInvoiceIHead::getInvoiceNo).distinct().collect(Collectors.joining("/"));
            DecErpIHeadN decErpIHeadN = new DecErpIHeadN();
            String sid = UUID.randomUUID().toString();
            decErpIHeadN.setSid(sid);
            decErpIHeadN.setEntryClearanceType("M");
            decErpIHeadN.setDeclTrnrel("0");
            decErpIHeadN.setMergeType("0");
            decErpIHeadN.setDataSource(CommonEnum.dataSourceEnum.SOURCE_15.getCode());
            decErpIHeadN.setEmsListNo(gwOcrIHeadDecParam.getEmsListNo());
            gwOcrInvoiceIHeadSums.removeAll(Collections.singleton(null));
            if(gwOcrInvoiceIHeadSums.size() > 0) {
                GwOcrInvoiceIHead gwOcrInvoiceIHead = gwOcrInvoiceIHeadSums.stream().findFirst().get();
                decErpIHeadN.setTradeTerms(gwOcrInvoiceIHead.getTradeTerms());
                decErpIHeadN.setDestinationCountry(gwOcrInvoiceIHead.getOriginCountryConvert());
                decErpIHeadN.setInvoiceNo(invoiceNoList);//斜杠汇总
                /* 业务需求,不带入总数量
                if (gwOcrInvoiceIHead.getQty() != null && gwOcrInvoiceIHead.getQty().compareTo(BigDecimal.ZERO) > 0) {
                    decErpIHeadN.setSumQty(gwOcrInvoiceIHead.getQty());//汇总
                }*/
                if (gwOcrInvoiceIHead.getTotalAmount() != null && gwOcrInvoiceIHead.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    decErpIHeadN.setSumDecTotal(gwOcrInvoiceIHead.getTotalAmount());//汇总
                }
                if (gwOcrInvoiceIHead.getPackNum() != null && gwOcrInvoiceIHead.getPackNum() > 0) {
                    decErpIHeadN.setPackNum(new BigDecimal(gwOcrInvoiceIHead.getPackNum()));//汇总
                }
                if (gwOcrInvoiceIHead.getVolume() != null && gwOcrInvoiceIHead.getVolume().compareTo(BigDecimal.ZERO) > 0) {
                    decErpIHeadN.setVolume(gwOcrInvoiceIHead.getVolume());//汇总
                }
                if (gwOcrInvoiceIHead.getNetWt() != null && gwOcrInvoiceIHead.getNetWt().compareTo(BigDecimal.ZERO) > 0) {
                    decErpIHeadN.setNetWt(gwOcrInvoiceIHead.getNetWt());
                    if (StringUtils.isNotBlank(gwOcrInvoiceIHead.getNetWtUnitConvert()) && "036".equals(gwOcrInvoiceIHead.getNetWtUnitConvert().toUpperCase())) {
                        decErpIHeadN.setNetWt(gwOcrInvoiceIHead.getNetWt().divide(new BigDecimal(1000), 5, BigDecimal.ROUND_HALF_UP));
                    }
                }
                if (gwOcrInvoiceIHead.getGrossWt() != null && gwOcrInvoiceIHead.getGrossWt().compareTo(BigDecimal.ZERO) > 0) {
                    decErpIHeadN.setGrossWt(gwOcrInvoiceIHead.getGrossWt());
                    if (StringUtils.isNotBlank(gwOcrInvoiceIHead.getGrossWtUnitConvert()) && "036".equals(gwOcrInvoiceIHead.getGrossWtUnitConvert().toUpperCase())) {
                        decErpIHeadN.setGrossWt(gwOcrInvoiceIHead.getGrossWt().divide(new BigDecimal(1000), 5, BigDecimal.ROUND_HALF_UP));
                    }
                }
                //赋值境外收发货人
                decErpIHeadN.setOverseasShipper(null);
            }
            if(decList.size()>0) {
                if (decErpIHeadN.getPackNum() == null&&decList.get(0).getPackNum()!=null) {
                    decErpIHeadN.setPackNum(BigDecimal.valueOf(decList.get(0).getPackNum()));
                }
                if (decErpIHeadN.getGrossWt() == null) {
                    decErpIHeadN.setGrossWt(decList.get(0).getGrossWt());
                }
                BiCustomerParams biCustomerParams = new BiCustomerParams();
                biCustomerParams.setTradeCode(userInfo.getCompany());
                biCustomerParams.setParamsType("PORT");
                List<BiCustomerParams> biCustomerPort = biCustomerParamsMapper.getList(biCustomerParams);
                decErpIHeadN.setMawb(decList.get(0).getMawb());
                decErpIHeadN.setHawb(decList.get(0).getHawb());
                decErpIHeadN.setVoyageNo(decList.get(0).getFltNo());
                decErpIHeadN.setCweight(decList.get(0).getTotalChwt());
                if(StringUtils.isNotBlank(decList.get(0).getDepport())) {
                    List<BiCustomerParams> biCustomerParamsList = biCustomerPort.stream().filter(x -> x.getParamsCode().equals(decList.get(0).getDepport())).collect(Collectors.toList());
                    if (biCustomerParamsList.size() > 0) {
                        decErpIHeadN.setDespPort(biCustomerParamsList.get(0).getCustomParamCode());
                        decErpIHeadN.setTradeCountry(biCustomerParamsList.get(0).getNote());
                    }
                }
                if(StringUtils.isNotBlank(decList.get(0).getDesport())) {
                    List<BiCustomerParams> biCustomerParamsList = biCustomerPort.stream().filter(x -> x.getParamsCode().equals(decList.get(0).getDesport())).collect(Collectors.toList());
                    if (biCustomerParamsList.size() > 0) {
                        decErpIHeadN.setDestPort(biCustomerParamsList.get(0).getCustomParamCode());
                    }
                }
            }
            //当勾选的数据类型中包含箱单时,每行发票净重、毛重按均摊方式赋值
            if(gwOcrPackingILists.size() > 0) {
                //对箱单中的净重单位进行转换
                for(GwOcrInvoiceIList gwOcrInvoiceIList : gwOcrPackingILists) {
                    if(StringUtils.isNotBlank(gwOcrInvoiceIList.getNetWtUnitConvert())&&"036".equals(gwOcrInvoiceIList.getNetWtUnitConvert().toUpperCase())) {
                        gwOcrInvoiceIList.setNetWt(gwOcrInvoiceIList.getNetWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                    }
                    if(StringUtils.isNotBlank(gwOcrInvoiceIList.getGrossWtUnitConvert())&&"036".equals(gwOcrInvoiceIList.getGrossWtUnitConvert().toUpperCase())) {
                        gwOcrInvoiceIList.setGrossWt(gwOcrInvoiceIList.getGrossWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                    }
                }
                for(GwOcrInvoiceIList iList : gwOcrInvoiceILists) {
                    String facGNo = iList.getFacGNo();
                    BigDecimal qty = iList.getQty();
                    if(qty != null) {
                        //获取当前料号在箱单中的汇总数量
                        BigDecimal qtyPackingTotal = gwOcrPackingILists.stream().filter(s -> StringUtils.equals(s.getFacGNo(),facGNo) && s.getQty() != null).map(GwOcrInvoiceIList::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //获取当前料号在箱单中的汇总净重
                        BigDecimal netWtPackingTotal = gwOcrPackingILists.stream().filter(s -> StringUtils.equals(s.getFacGNo(),facGNo) && s.getNetWt() != null).map(GwOcrInvoiceIList::getNetWt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //获取当前料号在箱单中的汇总毛重
                        BigDecimal grossWtPackingTotal = gwOcrPackingILists.stream().filter(s -> StringUtils.equals(s.getFacGNo(),facGNo) && s.getGrossWt() != null).map(GwOcrInvoiceIList::getGrossWt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if(qtyPackingTotal != null && qtyPackingTotal.compareTo(BigDecimal.ZERO) != 0) {
                            //计算表体当前料号均摊净重
                            BigDecimal netWt = netWtPackingTotal.multiply(qty.divide(qtyPackingTotal,8, BigDecimal.ROUND_HALF_UP));
                            iList.setNetWt(netWt);
                        }
                        if(grossWtPackingTotal != null && grossWtPackingTotal.compareTo(BigDecimal.ZERO) != 0) {
                            //计算表体当前料号均摊毛重
                            BigDecimal grossWt = grossWtPackingTotal.multiply(qty.divide(qtyPackingTotal,8, BigDecimal.ROUND_HALF_UP));
                            iList.setGrossWt(grossWt);
                        }
                    }
                }
            }
            List<DecErpIListN> decErpIListNList = new ArrayList<>();
            for(GwOcrInvoiceIList gwOcrInvoiceIList : gwOcrInvoiceILists) {
                DecErpIListN decErpIListN = new DecErpIListN();
                decErpIListN.setEmsListNo(gwOcrIHeadDecParam.getEmsListNo());
                decErpIListN.setOrderNo(gwOcrInvoiceIList.getOrderNo());
                decErpIListN.setFacGNo(gwOcrInvoiceIList.getFacGNo());
                decErpIListN.setQty(gwOcrInvoiceIList.getQty());
                decErpIListN.setQty1(gwOcrInvoiceIList.getQty1());
                decErpIListN.setDecPrice(gwOcrInvoiceIList.getDecPrice());
                decErpIListN.setDecTotal(gwOcrInvoiceIList.getDecTotal());
                decErpIListN.setCurr(gwOcrInvoiceIList.getCurrConvert());
                decErpIListN.setNetWt(gwOcrInvoiceIList.getNetWt());

                decErpIListN.setUnitErp(gwOcrInvoiceIList.getUnit());//用于申报数量比例因子转换
                GwOcrInvoiceIHead gwOcrInvoiceIHeadSelf = gwOcrInvoiceIHeads.stream().filter(x->x.getSid().equals(gwOcrInvoiceIList.getHeadId())).findFirst().get();
                decErpIListN.setInvoiceNo(gwOcrInvoiceIHeadSelf.getInvoiceNo());
                if(StringUtils.isNotBlank(gwOcrInvoiceIList.getNetWtUnitConvert())&&"036".equals(gwOcrInvoiceIList.getNetWtUnitConvert().toUpperCase())) {
                    decErpIListN.setNetWt(gwOcrInvoiceIList.getNetWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                }
                decErpIListN.setGrossWt(gwOcrInvoiceIList.getGrossWt());
                if(StringUtils.isNotBlank(gwOcrInvoiceIList.getGrossWtUnitConvert())&&"036".equals(gwOcrInvoiceIList.getGrossWtUnitConvert().toUpperCase())) {
                    decErpIListN.setGrossWt(gwOcrInvoiceIList.getGrossWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                }
//                decErpIListN.setSerialNo(new BigDecimal(gwOcrInvoiceIList.getSerialNo()));
                decErpIListN.setOriginCountry(gwOcrInvoiceIList.getOriginCountryConvert());
                decErpIListNList.add(decErpIListN);
            }
            decErpIHeadN.setGMark("I");
            result = decCommonService.insertAllI(decErpIHeadN, decErpIListNList,userInfo,null,true,null,gwOcrIHeadDecParam.getTemplateId());
            if(!result.isSuccess()) {
                return result;
            }
            decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(sid);

            //更新表体汇总数据到表头
            decErpIHeadN = decErpIListNService.updateSumAll(decErpIHeadN, decErpIHeadN.getSid());

            if(gwOcrInvoiceIHeadSums.size() > 0) {
                //判断如果OCR有值,取OCR值
                GwOcrInvoiceIHead gwOcrInvoiceIHead = gwOcrInvoiceIHeadSums.stream().findFirst().get();
                //判断如果境外发货人代码不为空
                if(StringUtils.isNotBlank(gwOcrInvoiceIHead.getShipperCode())) {
                    BiClientInformation biClientInformation = new BiClientInformation();
                    biClientInformation.setTradeCode(userInfo.getCompany());
                    //客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)
                    biClientInformation.setCustomerType(CommonVariable.PRD);
                    biClientInformation.setCustomerCode(gwOcrInvoiceIHead.getShipperCode());
                    List<BiClientInformation> biClientInformations = biClientInformationMapper.select(biClientInformation);
                    if (biClientInformations.size() == 1) {
                        //境外发货人代码
                        decErpIHeadN.setOverseasShipper(gwOcrInvoiceIHead.getShipperCode());
                        //境外发货人名称
                        decErpIHeadN.setOverseasShipperName(biClientInformations.get(0).getCompanyName());
                        //境外发货人AEO
                        decErpIHeadN.setOverseasShipperAeo(biClientInformations.get(0).getAeoCode());
                        decErpIHeadNMapper.updateByPrimaryKey(decErpIHeadN);
                    }else{
                        //判断境外发货人不为空
                        if(StringUtils.isNotBlank(gwOcrInvoiceIHead.getShipper())) {
                            //境外发货人代码
                            decErpIHeadN.setOverseasShipper(gwOcrInvoiceIHead.getShipperCode());
                            //境外发货人名称
                            decErpIHeadN.setOverseasShipperName(gwOcrInvoiceIHead.getShipper());
                            decErpIHeadNMapper.updateByPrimaryKey(decErpIHeadN);
                        }
                    }
                }else {
                    //判断境外发货人不为空
                    if(StringUtils.isNotBlank(gwOcrInvoiceIHead.getShipper())) {
                        //境外发货人代码
                        decErpIHeadN.setOverseasShipper(null);
                        //境外发货人Aeo
                        decErpIHeadN.setOverseasShipperAeo(null);
                        //境外发货人名称
                        decErpIHeadN.setOverseasShipperName(gwOcrInvoiceIHead.getShipper());
                        decErpIHeadNMapper.updateByPrimaryKey(decErpIHeadN);
                    }
                }
            }
            //更新OCR表头状态
            for(GwOcrInvoiceIHead gwOcrInvoiceIHead1 :gwOcrInvoiceIHeads) {
                gwOcrInvoiceIHead1.setCreateStatus("2");
                gwOcrInvoiceIHead1.setHeadId(sid);
                gwOcrInvoiceIHead1.setEmsListNo(gwOcrIHeadDecParam.getEmsListNo());
                mapper.updateByPrimaryKey(gwOcrInvoiceIHead1);
            }
            //插入附件
            String uuid = UUID.randomUUID().toString();
            String theTempPath = tempPath + "/" + uuid + "/";
            //判断文件夹是否存在
            File dir = new File(theTempPath);
            if (!dir.exists()) {
                dir.mkdir();
            }
            //定义附件Map,用于判断同附件名附件是否已生成,解决附件重复问题
            Map<String,Attached> attachedMap = new HashMap<>();
            for(GwOcrInvoiceIHead ocrHead : gwOcrInvoiceIHeads) {
                Example example = new Example(Attached.class);
                Example.Criteria criteria = example.createCriteria();
                if(StringUtils.isBlank(ocrHead.getSubTaskId())) {
                    continue;
                }
                criteria.andEqualTo("businessSid", ocrHead.getSubTaskId());
                List<Attached> attachedList = attachedMapper.selectByExample(example);
                for(Attached att : attachedList) {
                    String originFileName = att.getOriginFileName();
                    if(!attachedMap.containsKey(originFileName)) {
                        String fastUrl = att.getFileName();
                        String filePath = theTempPath + originFileName;
                        fastdFsService.downloadFileToLocal(fastUrl,filePath);
                        File file = new File(filePath);
                        if(file.exists()) {
                            gwOcrInvoiceIEService.addAttached(decErpIHeadN.getSid(),DecVariable.ATTACHED_IM, CommonEnum.OCR_FILE_TYPE_ENUM.getValue(ocrHead.getDataType()),filePath,originFileName,userInfo);
                        }
                        attachedMap.put(originFileName,att);
                    }
                }
            }
            //删除临时文件
            FileUtil.deleteFileTree(theTempPath);
        }
        return result;
    }

    /**
     * 进口ocr汇总预览
     * @param sids
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    @SneakyThrows
    public List<OcrInvoiceSumInfo> getSumInfo(List<String> sids) {
        if(CollectionUtils.isEmpty(sids)) {
            return null;
        }
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andIn("sid", sids);
        List<GwOcrInvoiceIHead> list = mapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 汇总
        Map<String, Optional<GwOcrInvoiceIHead>> map = list.stream().collect(Collectors.groupingBy(e->e.getDataType(), Collectors.reducing((a,b) -> sumab(a, b))));
        // 行列转置
        List<OcrInvoiceSumInfo> sumInfos = new ArrayList<>();
        for(Map.Entry<String, Field> entry : mapColumn.entrySet()) {
            OcrInvoiceSumInfo sumInfo = new OcrInvoiceSumInfo();
            sumInfo.setColType("head");
            sumInfo.setColName(entry.getKey());
            entry.getValue().setAccessible(true);
            if (entry.getValue().getType() != BigDecimal.class) {
                if (map.containsKey("INVOICE")) {
                    if (map.get("INVOICE").get().getPackNum() != null) {
                        sumInfo.setFpValue(new BigDecimal(map.get("INVOICE").get().getPackNum()));
                    }
                }
                if (map.containsKey("PACKING")) {
                    if (map.get("PACKING").get().getPackNum() != null) {
                        sumInfo.setXdValue(new BigDecimal(map.get("PACKING").get().getPackNum()));
                    }
                }
            } else {
                if (map.containsKey("INVOICE")) {
                    sumInfo.setFpValue((BigDecimal) entry.getValue().get(map.get("INVOICE").get()));
                }
                if (map.containsKey("PACKING")) {
                    sumInfo.setXdValue((BigDecimal) entry.getValue().get(map.get("PACKING").get()));
                }
            }
            sumInfo.clacDiff();

            sumInfos.add(sumInfo);
        }
        return sumInfos;
    }

    private GwOcrInvoiceIHead sumab(GwOcrInvoiceIHead a, GwOcrInvoiceIHead b) {
        if (b.getQty() != null) {
            if (a.getQty() != null) {
                a.setQty(a.getQty().add(b.getQty()));
            } else {
                a.setQty(b.getQty());
            }
        }
        if (b.getTotalAmount() != null) {
            if (a.getTotalAmount() != null) {
                a.setTotalAmount(a.getTotalAmount().add(b.getTotalAmount()));
            } else {
                a.setTotalAmount(b.getTotalAmount());
            }
        }
        if (b.getPackNum() != null) {
            if (a.getPackNum() != null) {
                a.setPackNum(a.getPackNum() + b.getPackNum());
            } else {
                a.setPackNum(b.getPackNum());
            }
        }
        if (b.getVolume() != null) {
            if (a.getVolume() != null) {
                a.setVolume(a.getVolume().add(b.getVolume()));
            } else {
                a.setVolume(b.getVolume());
            }
        }
        if (b.getNetWt() != null) {
            if (a.getNetWt() != null) {
                a.setNetWt(a.getNetWt().add(b.getNetWt()));
            } else {
                a.setNetWt(b.getNetWt());
            }
        }
        if (b.getGrossWt() != null) {
            if (a.getGrossWt() != null) {
                a.setGrossWt(a.getGrossWt().add(b.getGrossWt()));
            } else {
                a.setGrossWt(b.getGrossWt());
            }
        }
        return a;
    }

    /**
     * OCR进口表头信息校验
     * @param head
     * @return
     * @throws OcrDataValidationException
     */
    public String checkOcrHead(GwOcrInvoiceIHead head) throws OcrDataValidationException {
        GwOcrInvoiceIHeadParam tobeCheck = dtoMapper.poToEntity(head);
        String err = validatorUtil.validation(tobeCheck);
        if (err != null) {
            throw new OcrDataValidationException(err);
        }
        return null;
    }

    /**
     * OCR进口表体信息校验
     * @param list
     * @return
     * @throws OcrDataValidationException
     */
    public String checkOcrList(GwOcrInvoiceIList list) throws OcrDataValidationException {
        //针对发票校验
        String headId = list.getHeadId();
        GwOcrInvoiceIHead head = mapper.selectByPrimaryKey(headId);
        if(StringUtils.equals("INVOICE",head.getDataType())) {
            GwOcrInvoiceIListParam tobeCheck = listDtoMapper.poToEntity(list);
            String err = validatorUtil.validation(tobeCheck);
            if (err != null) {
                throw new OcrDataValidationException(err);
            }
        }
        return null;
    }
}
