package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("")
public class OcrTDData implements Serializable {

    @ApiModelProperty("发票号码")
    @JsonProperty("TDINVOICE_NO")
    private OcrFieldInfo TDINVOICE_NO;

    @ApiModelProperty("发票日期")
    @JsonProperty("TDINVOICE_DATE")
    private OcrFieldInfo TDINVOICE_DATE;

    @ApiModelProperty("装箱单号码")
    @JsonProperty("TDPACKING_NO")
    private OcrFieldInfo TDPACKING_NO;

    @ApiModelProperty("关联号码")
    @JsonProperty("TDREFERENCE_NO")
    private OcrFieldInfo TDREFERENCE_NO;

    @ApiModelProperty("发货人")
    @JsonProperty("TDSHIPPER")
    private OcrFieldInfo TDSHIPPER;

    @ApiModelProperty("发货人代码")
    @JsonProperty("TDSHIPPER_CODE")
    private OcrFieldInfo TDSHIPPER_CODE;

    @ApiModelProperty("收货人")
    @JsonProperty("TDCONSIGNEE")
    private OcrFieldInfo TDCONSIGNEE;

    @ApiModelProperty("成交方式")
    @JsonProperty("TDINCOTERMS")
    private OcrFieldInfo TDINCOTERMS;

    @ApiModelProperty("总数量")
    @JsonProperty("TDTOTAL_QTY")
    private OcrFieldInfo TDTOTAL_QTY;

    @ApiModelProperty("总数量单位")
    @JsonProperty("TDTOTAL_QTYUNIT")
    private OcrFieldInfo TDTOTAL_QTYUNIT;

    @ApiModelProperty("总金额")
    @JsonProperty("TDTOTAL_AMOUNT")
    private OcrFieldInfo TDTOTAL_AMOUNT;

    @ApiModelProperty("币别")
    @JsonProperty("TDTOTAL_CURR")
    private OcrFieldInfo TDTOTAL_CURR;

    @ApiModelProperty("统一原产国")
    @JsonProperty("TDHEAD_COO")
    private OcrFieldInfo TDHEAD_COO;

    @ApiModelProperty("统一订单")
    @JsonProperty("TDHEAD_PO")
    private OcrFieldInfo TDHEAD_PO;

    @ApiModelProperty("托盘数")
    @JsonProperty("TDTOTAL_PALLET")
    private OcrFieldInfo TDTOTAL_PALLET;

    @ApiModelProperty("散箱数")
    @JsonProperty("TDBULK_CTNS")
    private OcrFieldInfo TDBULK_CTNS;

    @ApiModelProperty("总箱数")
    @JsonProperty("TDTOTAL_CTNS")
    private OcrFieldInfo TDTOTAL_CTNS;

    @ApiModelProperty("总件数")
    @JsonProperty("TDTOTAL_PKG")
    private OcrFieldInfo TDTOTAL_PKG;

    @ApiModelProperty("总净重")
    @JsonProperty("TDTOTAL_NW")
    private OcrFieldInfo TDTOTAL_NW;

    @ApiModelProperty("净重单位")
    @JsonProperty("TDTOTAL_NWUnit")
    private OcrFieldInfo TDTOTAL_NWUnit;

    @ApiModelProperty("总毛重")
    @JsonProperty("TDTOTAL_GW")
    private OcrFieldInfo TDTOTAL_GW;

    @ApiModelProperty("毛重单位")
    @JsonProperty("TDTOTAL_GWUnit")
    private OcrFieldInfo TDTOTAL_GWUnit;

    @ApiModelProperty("总体积")
    @JsonProperty("TDTOTAL_MEAS")
    private OcrFieldInfo TDTOTAL_MEAS;

    @ApiModelProperty("表体")
    @JsonProperty("TDCommodity")
    private List<OcrTDListData> TDCommodity;

    @ApiModelProperty("主提运单号")
    @JsonProperty("TDMAWB")
    private OcrFieldInfo TDMAWB;

    @ApiModelProperty("分提运单号")
    @JsonProperty("TDHAWB")
    private OcrFieldInfo TDHAWB;

    @ApiModelProperty("货代")
    @JsonProperty("TDFORWARDER")
    private OcrFieldInfo TDFORWARDER;

    @ApiModelProperty("启运港")
    @JsonProperty("TDDEPPORT")
    private OcrFieldInfo TDDEPPORT;

    @ApiModelProperty("目的港")
    @JsonProperty("TDDESPORT")
    private OcrFieldInfo TDDESPORT;

    @ApiModelProperty("提单日期")
    @JsonProperty("TDBILL_DATE")
    private OcrFieldInfo TDBILL_DATE;

    @ApiModelProperty("航班号")
    @JsonProperty("TDFLT_NO")
    private OcrFieldInfo TDFLT_NO;

    @ApiModelProperty("航班日期")
    @JsonProperty("TDFLT_DATE")
    private OcrFieldInfo TDFLT_DATE;

    @ApiModelProperty("头程航班号")
    @JsonProperty("TDFLT_NO_1ST")
    private OcrFieldInfo TDFLT_NO_1ST;

    @ApiModelProperty("头程航班日期")
    @JsonProperty("TDFLT_DATE_1ST")
    private OcrFieldInfo TDFLT_DATE_1ST;

    @ApiModelProperty("运费")
    @JsonProperty("TDFREIGHT")
    private OcrFieldInfo TDFREIGHT;

    @ApiModelProperty("计费重量")
    @JsonProperty("TDTOTAL_CHWT")
    private OcrFieldInfo TDTOTAL_CHWT;


    
}
