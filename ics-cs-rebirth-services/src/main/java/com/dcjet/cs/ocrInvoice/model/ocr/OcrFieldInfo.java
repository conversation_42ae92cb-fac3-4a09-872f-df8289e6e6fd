package com.dcjet.cs.ocrInvoice.model.ocr;

import com.xdo.common.json.JsonObjectMapper;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("")
public class OcrFieldInfo implements Serializable {

    private String value;

    private List<List<BigDecimal>> locations;

    private String imagename;

    public String toJson() {
        return JsonObjectMapper.getInstance().toJson(this);
    }
}
