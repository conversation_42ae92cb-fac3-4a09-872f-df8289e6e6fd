package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListLocParam;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceEListLocMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceEListLocDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEListLoc;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2023-02-13
 */
@Service
public class GwOcrInvoiceEListLocService extends BaseService<GwOcrInvoiceEListLoc> {
    @Resource
    private GwOcrInvoiceEListLocMapper mapper;
    @Resource
    private GwOcrInvoiceEListLocDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceEListLoc> getMapper() {
        return mapper;
    }

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceEListLocParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwOcrInvoiceEListLocDto>> getListPaged(GwOcrInvoiceEListLocParam gwOcrInvoiceEListLocParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceEListLoc gwOcrInvoiceEListLoc = dtoMapper.toPo(gwOcrInvoiceEListLocParam);
        Page<GwOcrInvoiceEListLoc> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceEListLoc));
        List<GwOcrInvoiceEListLocDto> gwOcrInvoiceEListLocDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceEListLocDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrInvoiceEListLocDto>> paged = ResultObject.createInstance(gwOcrInvoiceEListLocDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceEListLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEListLocDto insert(GwOcrInvoiceEListLocParam gwOcrInvoiceEListLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceEListLoc gwOcrInvoiceEListLoc = dtoMapper.toPo(gwOcrInvoiceEListLocParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceEListLoc.setSid(sid);
        gwOcrInvoiceEListLoc.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceEListLoc.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceEListLoc);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceEListLoc) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceEListLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEListLocDto update(GwOcrInvoiceEListLocParam gwOcrInvoiceEListLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceEListLoc gwOcrInvoiceEListLoc = mapper.selectByPrimaryKey(gwOcrInvoiceEListLocParam.getSid());
        dtoMapper.updatePo(gwOcrInvoiceEListLocParam, gwOcrInvoiceEListLoc);
        gwOcrInvoiceEListLoc.setUpdateUser(userInfo.getUserNo());
        gwOcrInvoiceEListLoc.setUpdateTime(new Date());
        // 更新数据
        int update = mapper.updateByPrimaryKey(gwOcrInvoiceEListLoc);
        return update > 0 ? dtoMapper.toDto(gwOcrInvoiceEListLoc) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceEListLocDto> selectAll(GwOcrInvoiceEListLocParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceEListLoc gwOcrInvoiceEListLoc = dtoMapper.toPo(exportParam);
        gwOcrInvoiceEListLoc.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceEListLocDto> gwOcrInvoiceEListLocDtos = new ArrayList<>();
        List<GwOcrInvoiceEListLoc> gwOcrInvoiceEListLocs = mapper.getList(gwOcrInvoiceEListLoc);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceEListLocs)) {
            gwOcrInvoiceEListLocDtos = gwOcrInvoiceEListLocs.stream().map(head -> {
                GwOcrInvoiceEListLocDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceEListLocDtos;
    }

    /**
     *
     * @param sid
     * @return
     */
    public GwOcrInvoiceEListLocDto getOne(String sid) {
        GwOcrInvoiceEListLoc loc = mapper.selectByPrimaryKey(sid);
        if (loc == null) {
            return null;
        }
        return dtoMapper.toDto(loc);
    }
}
