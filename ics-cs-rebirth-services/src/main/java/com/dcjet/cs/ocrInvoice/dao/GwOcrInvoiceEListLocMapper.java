package com.dcjet.cs.ocrInvoice.dao;

import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEListLoc;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIListLoc;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* GwOcrInvoiceIListLoc
* <AUTHOR>
* @date: 2021-9-30
*/
public interface GwOcrInvoiceEListLocMapper extends Mapper<GwOcrInvoiceEListLoc> {
    /**
     * 查询获取数据
     * @param gwOcrInvoiceEListLoc
     * @return
     */
    List<GwOcrInvoiceEListLoc> getList(GwOcrInvoiceEListLoc gwOcrInvoiceEListLoc);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
