package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.GwOcrLogDetailDto;
import com.dcjet.cs.dto.ocrInvoice.GwOcrLogDetailParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrLogDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrLogDetailDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrLogDetailDto toDto(GwOcrLogDetail po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrLogDetail toPo(GwOcrLogDetailParam param);
    /**
     * 数据库原始数据更新
     * @param gwOcrLogDetailParam
     * @param gwOcrLogDetail
     */
    void updatePo(GwOcrLogDetailParam gwOcrLogDetailParam, @MappingTarget GwOcrLogDetail gwOcrLogDetail);
    default void patchPo(GwOcrLogDetailParam gwOcrLogDetailParam, GwOcrLogDetail gwOcrLogDetail) {
        // TODO 自行实现局部更新
    }
}
