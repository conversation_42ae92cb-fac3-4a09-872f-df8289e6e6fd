package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListLocParam;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceIListLocMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceIListLocDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIListLoc;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Service
public class GwOcrInvoiceIListLocService extends BaseService<GwOcrInvoiceIListLoc> {
    @Resource
    private GwOcrInvoiceIListLocMapper mapper;
    @Resource
    private GwOcrInvoiceIListLocDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceIListLoc> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceIListLocParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwOcrInvoiceIListLocDto>> getListPaged(GwOcrInvoiceIListLocParam gwOcrInvoiceIListLocParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceIListLoc gwOcrInvoiceIListLoc = dtoMapper.toPo(gwOcrInvoiceIListLocParam);
        Page<GwOcrInvoiceIListLoc> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceIListLoc));
        List<GwOcrInvoiceIListLocDto> gwOcrInvoiceIListLocDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceIListLocDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrInvoiceIListLocDto>> paged = ResultObject.createInstance(gwOcrInvoiceIListLocDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceIListLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIListLocDto insert(GwOcrInvoiceIListLocParam gwOcrInvoiceIListLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceIListLoc gwOcrInvoiceIListLoc = dtoMapper.toPo(gwOcrInvoiceIListLocParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceIListLoc.setSid(sid);
        gwOcrInvoiceIListLoc.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceIListLoc.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceIListLoc);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceIListLoc) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceIListLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIListLocDto update(GwOcrInvoiceIListLocParam gwOcrInvoiceIListLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceIListLoc gwOcrInvoiceIListLoc = mapper.selectByPrimaryKey(gwOcrInvoiceIListLocParam.getSid());
        dtoMapper.updatePo(gwOcrInvoiceIListLocParam, gwOcrInvoiceIListLoc);
        gwOcrInvoiceIListLoc.setUpdateUser(userInfo.getUserNo());
        gwOcrInvoiceIListLoc.setUpdateTime(new Date());
        // 更新数据
        int update = mapper.updateByPrimaryKey(gwOcrInvoiceIListLoc);
        return update > 0 ? dtoMapper.toDto(gwOcrInvoiceIListLoc) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceIListLocDto> selectAll(GwOcrInvoiceIListLocParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceIListLoc gwOcrInvoiceIListLoc = dtoMapper.toPo(exportParam);
        // gwOcrInvoiceIListLoc.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceIListLocDto> gwOcrInvoiceIListLocDtos = new ArrayList<>();
        List<GwOcrInvoiceIListLoc> gwOcrInvoiceIListLocs = mapper.getList(gwOcrInvoiceIListLoc);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceIListLocs)) {
            gwOcrInvoiceIListLocDtos = gwOcrInvoiceIListLocs.stream().map(head -> {
                GwOcrInvoiceIListLocDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceIListLocDtos;
    }

    /**
     *
     * @param sid
     * @return
     */
    public GwOcrInvoiceIListLocDto getOne(String sid) {
        GwOcrInvoiceIListLoc loc = mapper.selectByPrimaryKey(sid);
        if (loc == null) {
            return null;
        }
        return dtoMapper.toDto(loc);
    }
}
