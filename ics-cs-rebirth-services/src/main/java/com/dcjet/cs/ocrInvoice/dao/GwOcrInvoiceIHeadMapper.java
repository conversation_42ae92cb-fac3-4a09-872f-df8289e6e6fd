package com.dcjet.cs.ocrInvoice.dao;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwOcrInvoiceIHead
* <AUTHOR>
* @date: 2021-9-29
*/
public interface GwOcrInvoiceIHeadMapper extends Mapper<GwOcrInvoiceIHead> {
    /**
     * 查询获取数据
     * @param gwOcrInvoiceIHead
     * @return
     */
    List<GwOcrInvoiceIHead> getList(GwOcrInvoiceIHead gwOcrInvoiceIHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据sid列表获取数据
     * @param sids 表头sid
     * @return
     */
    List<GwOcrInvoiceIHead> selectBySids(@Param("sids") List<String> sids);

    /**
     * 根据sid列表获取数据
     * @param sids 表头sid
     * @return
     */
    List<String> selectSubTaskId(@Param("sids") List<String> sids);

    List<GwOcrInvoiceIHead> selectSumBySids(@Param("sids") List<String> sids);

    int updateStatus(@Param("list") List<String> list);
}
