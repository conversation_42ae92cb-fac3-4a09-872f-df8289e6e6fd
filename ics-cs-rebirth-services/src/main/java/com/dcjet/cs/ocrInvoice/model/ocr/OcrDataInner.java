package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("OcrData")
public class OcrDataInner implements Serializable {

    @JsonProperty("FPList")
    private List<OcrFPData> FPList;

    @JsonProperty("XDList")
    private List<OcrXDData> XDList;

    @JsonProperty("TDList")
    private List<OcrTDData> TDList;

    @JsonProperty("BGDList")
    private List<OcrEntryData> BGDList;

}
