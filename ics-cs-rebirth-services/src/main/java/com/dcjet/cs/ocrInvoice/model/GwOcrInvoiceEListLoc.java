package com.dcjet.cs.ocrInvoice.model;

import com.dcjet.cs.ocrInvoice.model.ocr.OcrFPListData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrTDListData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrXDListData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-9-30
 */
@Setter
@Getter
@Table(name = "t_gw_ocr_invoice_e_list_loc")
public class GwOcrInvoiceEListLoc implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 对应OCR发票箱单表体的SID
     */
	 @Id
	@Column(name = "sid")
	private String sid;
	/**
     * 订单号
     */
	@Column(name = "order_no_loc")
	private String orderNoLoc;
	/**
     * 订单序号
     */
	@Column(name = "order_serial_no_loc")
	private String orderSerialNoLoc;
	/**
     * 企业料号
     */
	@Column(name = "fac_g_no_loc")
	private String facGNoLoc;
	/**
     * 商品描述
     */
	@Column(name = "good_desc_loc")
	private String goodDescLoc;
	/**
     * 交易数量
     */
	@Column(name = "qty_loc")
	private String qtyLoc;
	/**
     * 交易单位
     */
	@Column(name = "unit_loc")
	private String unitLoc;
	/**
     * 法一数量
     */
	@Column(name = "qty_1_loc")
	private String qty1Loc;
	/**
     * 单价
     */
	@Column(name = "dec_price_loc")
	private String decPriceLoc;
	/**
     * 百个单价
     */
	@Column(name = "dec_price_h_loc")
	private String decPriceHLoc;
	/**
     * 千个单价
     */
	@Column(name = "dec_price_t_loc")
	private String decPriceTLoc;
	/**
     * 申报总价
     */
	@Column(name = "dec_total_loc")
	private String decTotalLoc;
	/**
     * 币制
     */
	@Column(name = "curr_loc")
	private String currLoc;
	/**
     * 净重
     */
	@Column(name = "net_wt_loc")
	private String netWtLoc;
	/**
     * 净重单位
     */
	@Column(name = "net_wt_unit_loc")
	private String netWtUnitLoc;
	/**
     * 总毛重
     */
	@Column(name = "gross_wt_loc")
	private String grossWtLoc;
	/**
     * 毛重单位
     */
	@Column(name = "gross_wt_unit_loc")
	private String grossWtUnitLoc;
	/**
     * 原产国
     */
	@Column(name = "origin_country_loc")
	private String originCountryLoc;
	/**
     * 件数
     */
	@Column(name = "pack_num_loc")
	private String packNumLoc;
	/**
     * 包装种类
     */
	@Column(name = "wrap_type_loc")
	private String wrapTypeLoc;
	/**
     * 包装编号
     */
	@Column(name = "wrap_no_loc")
	private String wrapNoLoc;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private Date insertTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private String updateUser;
	/**
     * 修改人名称
     */
	@Column(name = "update_user_name")
	private String updateUserName;
	/**
     * 修改数据时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private Date updateTime;

	public GwOcrInvoiceEListLoc initData(GwOcrLog log) {

		this.tradeCode = log.getTradeCode();
		this.insertUser = log.getInsertUser();
		this.insertUserName = log.getInsertUserName();
		this.insertTime = new Date();


		return this;
	}

	public void assembly(OcrFPListData ocrList) {
		if (ocrList.getFPCommodity_PO() != null) {
			this.setOrderNoLoc(ocrList.getFPCommodity_PO().toJson());
		}
		if (ocrList.getFPCommodity_PO_Item() != null) {
			this.setOrderSerialNoLoc(ocrList.getFPCommodity_PO_Item().toJson());
		}
		if (ocrList.getFPCommodity_Gds_PartNo() != null) {
			this.setFacGNoLoc(ocrList.getFPCommodity_Gds_PartNo().toJson());
		}
		if (ocrList.getFPCommodity_Gds_Desc() != null) {
			this.setGoodDescLoc(ocrList.getFPCommodity_Gds_Desc().toJson());
		}
		if (ocrList.getFPCommodity_Gds_Qty() != null) {
			this.setQtyLoc(ocrList.getFPCommodity_Gds_Qty().toJson());
		}
		if (ocrList.getFPCommodity_Gds_Unit() != null) {
			this.setUnitLoc(ocrList.getFPCommodity_Gds_Unit().toJson());
		}
		if (ocrList.getFPCommodity_Gds_Price() != null) {
			this.setDecPriceLoc(ocrList.getFPCommodity_Gds_Price().toJson());
		}
		if (ocrList.getFPCommodity_Gds_PriceHundred() != null) {
			this.setDecPriceHLoc(ocrList.getFPCommodity_Gds_PriceHundred().toJson());
		}
		if (ocrList.getFPCommodity_Gds_PriceThousand() != null) {
			this.setDecPriceTLoc(ocrList.getFPCommodity_Gds_PriceThousand().toJson());
		}
		if (ocrList.getFPCommodity_Gds_AMT() != null) {
			this.setDecTotalLoc(ocrList.getFPCommodity_Gds_AMT().toJson());
		}
		if (ocrList.getFPCommodity_Gds_Curr() != null) {
			this.setCurrLoc(ocrList.getFPCommodity_Gds_Curr().toJson());
		}
		if (ocrList.getFPCommodity_Gds_NW() != null) {
			this.setNetWtLoc(ocrList.getFPCommodity_Gds_NW().toJson());
		}
		if (ocrList.getFPCommodity_Gds_NWUnit() != null) {
			this.setNetWtUnitLoc(ocrList.getFPCommodity_Gds_NWUnit().toJson());
		}
		if (ocrList.getFPCommodity_Gds_GW() != null) {
			this.setGrossWtLoc(ocrList.getFPCommodity_Gds_GW().toJson());
		}
		if (ocrList.getFPCommodity_Gds_GWUnit() != null) {
			this.setGrossWtUnitLoc(ocrList.getFPCommodity_Gds_GWUnit().toJson());
		}
		if (ocrList.getFPCommodity_Gds_COO() != null) {
			this.setOriginCountryLoc(ocrList.getFPCommodity_Gds_COO().toJson());
		}
		if (ocrList.getFPCommodity_Gds_PkgQty() != null) {
			this.setPackNumLoc(ocrList.getFPCommodity_Gds_PkgQty().toJson());
		}
		if (ocrList.getFPCommodity_Gds_PkgUom() !=  null) {
			this.setWrapTypeLoc(ocrList.getFPCommodity_Gds_PkgUom().toJson());
		}
		if (ocrList.getFPCommodity_Gds_PkgNo() != null) {
			this.setWrapNoLoc(ocrList.getFPCommodity_Gds_PkgNo().toJson());
		}
	}

	public void assembly(OcrXDListData ocrList) {
		if (ocrList.getXDCommodity_PO() != null) {
			this.setOrderNoLoc(ocrList.getXDCommodity_PO().toJson());
		}
		if (ocrList.getXDCommodity_PO_Item() != null) {
			this.setOrderSerialNoLoc(ocrList.getXDCommodity_PO_Item().toJson());
		}
		if (ocrList.getXDCommodity_Gds_PartNo() != null) {
			this.setFacGNoLoc(ocrList.getXDCommodity_Gds_PartNo().toJson());
		}
		if (ocrList.getXDCommodity_Gds_Desc() != null) {
			this.setGoodDescLoc(ocrList.getXDCommodity_Gds_Desc().toJson());
		}
		if (ocrList.getXDCommodity_Gds_Qty() != null) {
			this.setQtyLoc(ocrList.getXDCommodity_Gds_Qty().toJson());
		}
		if (ocrList.getXDCommodity_Gds_Unit() != null) {
			this.setUnitLoc(ocrList.getXDCommodity_Gds_Unit().toJson());
		}
		if (ocrList.getXDCommodity_Gds_Price() != null) {
			this.setDecPriceLoc(ocrList.getXDCommodity_Gds_Price().toJson());
		}
		if (ocrList.getXDCommodity_Gds_PriceHundred() != null) {
			this.setDecPriceHLoc(ocrList.getXDCommodity_Gds_PriceHundred().toJson());
		}
		if (ocrList.getXDCommodity_Gds_PriceThousand() != null) {
			this.setDecPriceTLoc(ocrList.getXDCommodity_Gds_PriceThousand().toJson());
		}
		if (ocrList.getXDCommodity_Gds_AMT() != null) {
			this.setDecTotalLoc(ocrList.getXDCommodity_Gds_AMT().toJson());
		}
		if (ocrList.getXDCommodity_Gds_Curr() != null) {
			this.setCurrLoc(ocrList.getXDCommodity_Gds_Curr().toJson());
		}
		if (ocrList.getXDCommodity_Gds_NW() != null) {
			this.setNetWtLoc(ocrList.getXDCommodity_Gds_NW().toJson());
		}
		if (ocrList.getXDCommodity_Gds_NWUnit() != null) {
			this.setNetWtUnitLoc(ocrList.getXDCommodity_Gds_NWUnit().toJson());
		}
		if (ocrList.getXDCommodity_Gds_GW() != null) {
			this.setGrossWtLoc(ocrList.getXDCommodity_Gds_GW().toJson());
		}
		if (ocrList.getXDCommodity_Gds_GWUnit() != null) {
			this.setGrossWtUnitLoc(ocrList.getXDCommodity_Gds_GWUnit().toJson());
		}
		if (ocrList.getXDCommodity_Gds_COO() != null) {
			this.setOriginCountryLoc(ocrList.getXDCommodity_Gds_COO().toJson());
		}
		if (ocrList.getXDCommodity_Gds_PkgQty() != null) {
			this.setPackNumLoc(ocrList.getXDCommodity_Gds_PkgQty().toJson());
		}
		if (ocrList.getXDCommodity_Gds_PkgUom() !=  null) {
			this.setWrapTypeLoc(ocrList.getXDCommodity_Gds_PkgUom().toJson());
		}
		if (ocrList.getXDCommodity_Gds_PkgNo() != null) {
			this.setWrapNoLoc(ocrList.getXDCommodity_Gds_PkgNo().toJson());
		}
	}

	public void assembly(OcrTDListData ocrList) {
		if (ocrList.getTDCommodity_PO() != null) {
			this.setOrderNoLoc(ocrList.getTDCommodity_PO().toJson());
		}
		if (ocrList.getTDCommodity_PO_Item() != null) {
			this.setOrderSerialNoLoc(ocrList.getTDCommodity_PO_Item().toJson());
		}
		if (ocrList.getTDCommodity_Gds_PartNo() != null) {
			this.setFacGNoLoc(ocrList.getTDCommodity_Gds_PartNo().toJson());
		}
		if (ocrList.getTDCommodity_Gds_Desc() != null) {
			this.setGoodDescLoc(ocrList.getTDCommodity_Gds_Desc().toJson());
		}
		if (ocrList.getTDCommodity_Gds_Qty() != null) {
			this.setQtyLoc(ocrList.getTDCommodity_Gds_Qty().toJson());
		}
		if (ocrList.getTDCommodity_Gds_Unit() != null) {
			this.setUnitLoc(ocrList.getTDCommodity_Gds_Unit().toJson());
		}
		if (ocrList.getTDCommodity_Gds_Price() != null) {
			this.setDecPriceLoc(ocrList.getTDCommodity_Gds_Price().toJson());
		}
		if (ocrList.getTDCommodity_Gds_PriceHundred() != null) {
			this.setDecPriceHLoc(ocrList.getTDCommodity_Gds_PriceHundred().toJson());
		}
		if (ocrList.getTDCommodity_Gds_PriceThousand() != null) {
			this.setDecPriceTLoc(ocrList.getTDCommodity_Gds_PriceThousand().toJson());
		}
		if (ocrList.getTDCommodity_Gds_AMT() != null) {
			this.setDecTotalLoc(ocrList.getTDCommodity_Gds_AMT().toJson());
		}
		if (ocrList.getTDCommodity_Gds_Curr() != null) {
			this.setCurrLoc(ocrList.getTDCommodity_Gds_Curr().toJson());
		}
		if (ocrList.getTDCommodity_Gds_NW() != null) {
			this.setNetWtLoc(ocrList.getTDCommodity_Gds_NW().toJson());
		}
		if (ocrList.getTDCommodity_Gds_NWUnit() != null) {
			this.setNetWtUnitLoc(ocrList.getTDCommodity_Gds_NWUnit().toJson());
		}
		if (ocrList.getTDCommodity_Gds_GW() != null) {
			this.setGrossWtLoc(ocrList.getTDCommodity_Gds_GW().toJson());
		}
		if (ocrList.getTDCommodity_Gds_GWUnit() != null) {
			this.setGrossWtUnitLoc(ocrList.getTDCommodity_Gds_GWUnit().toJson());
		}
		if (ocrList.getTDCommodity_Gds_COO() != null) {
			this.setOriginCountryLoc(ocrList.getTDCommodity_Gds_COO().toJson());
		}
		if (ocrList.getTDCommodity_Gds_PkgQty() != null) {
			this.setPackNumLoc(ocrList.getTDCommodity_Gds_PkgQty().toJson());
		}
		if (ocrList.getTDCommodity_Gds_PkgUom() !=  null) {
			this.setWrapTypeLoc(ocrList.getTDCommodity_Gds_PkgUom().toJson());
		}
		if (ocrList.getTDCommodity_Gds_PkgNo() != null) {
			this.setWrapNoLoc(ocrList.getTDCommodity_Gds_PkgNo().toJson());
		}
	}
}
