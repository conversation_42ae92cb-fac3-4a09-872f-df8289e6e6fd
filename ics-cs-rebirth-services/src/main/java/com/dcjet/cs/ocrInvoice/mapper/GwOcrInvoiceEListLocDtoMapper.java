package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListLocParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEListLoc;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-30
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceEListLocDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceEListLocDto toDto(GwOcrInvoiceEListLoc po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceEListLoc toPo(GwOcrInvoiceEListLocParam param);
    /**
     * 数据库原始数据更新
     * @param gwOcrInvoiceEListLocParam
     * @param gwOcrInvoiceEListLoc
     */
    void updatePo(GwOcrInvoiceEListLocParam gwOcrInvoiceEListLocParam, @MappingTarget GwOcrInvoiceEListLoc gwOcrInvoiceEListLoc);
    default void patchPo(GwOcrInvoiceEListLocParam gwOcrInvoiceEListLocParam, GwOcrInvoiceEListLoc gwOcrInvoiceEListLoc) {
        // TODO 自行实现局部更新
    }
}
