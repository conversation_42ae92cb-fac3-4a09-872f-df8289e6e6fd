package com.dcjet.cs.ocrInvoice.dao;

import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIListLoc;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwOcrInvoiceIListLoc
* <AUTHOR>
* @date: 2021-9-30
*/
public interface GwOcrInvoiceIListLocMapper extends Mapper<GwOcrInvoiceIListLoc> {
    /**
     * 查询获取数据
     * @param gwOcrInvoiceIListLoc
     * @return
     */
    List<GwOcrInvoiceIListLoc> getList(GwOcrInvoiceIListLoc gwOcrInvoiceIListLoc);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
