package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListParam;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceEListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceEListDto toDto(GwOcrInvoiceEList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceEList toPo(GwOcrInvoiceEListParam param);

    /**
     * model -> param
     * @param model
     * @return
     */
    GwOcrInvoiceEListParam poToEntity(GwOcrInvoiceEList model);

    /**
     * 数据库原始数据更新
     * @param gwOcrInvoiceEListParam
     * @param gwOcrInvoiceEList
     */
    void updatePo(GwOcrInvoiceEListParam gwOcrInvoiceEListParam, @MappingTarget GwOcrInvoiceEList gwOcrInvoiceEList);
    default void patchPo(GwOcrInvoiceEListParam gwOcrInvoiceEListParam, GwOcrInvoiceEList gwOcrInvoiceEList) {
        // TODO 自行实现局部更新
    }
}
