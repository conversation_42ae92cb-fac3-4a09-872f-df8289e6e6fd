package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("OcrTDListData")
public class OcrTDListData implements Serializable {

    @ApiModelProperty("序号")
    @JsonProperty("TDCommodity_Item")
    private OcrFieldInfo TDCommodity_Item;

    @ApiModelProperty("订单号码")
    @JsonProperty("TDCommodity_PO")
    private OcrFieldInfo TDCommodity_PO;

    @ApiModelProperty("订单项号")
    @JsonProperty("TDCommodity_PO_Item")
    private OcrFieldInfo TDCommodity_PO_Item;

    @ApiModelProperty("料号")
    @JsonProperty("TDCommodity_Gds_PartNo")
    private OcrFieldInfo TDCommodity_Gds_PartNo;

    @ApiModelProperty("商品描述")
    @JsonProperty("TDCommodity_Gds_Desc")
    private OcrFieldInfo TDCommodity_Gds_Desc;

    @ApiModelProperty("数量")
    @JsonProperty("TDCommodity_Gds_Qty")
    private OcrFieldInfo TDCommodity_Gds_Qty;

    @ApiModelProperty("数量单位")
    @JsonProperty("TDCommodity_Gds_Unit")
    private OcrFieldInfo TDCommodity_Gds_Unit;

    @ApiModelProperty("单价")
    @JsonProperty("TDCommodity_Gds_Price")
    private OcrFieldInfo TDCommodity_Gds_Price;

    @ApiModelProperty("百个单价")
    @JsonProperty("TDCommodity_Gds_PriceHundred")
    private OcrFieldInfo TDCommodity_Gds_PriceHundred;

    @ApiModelProperty("千个单价")
    @JsonProperty("TDCommodity_Gds_PriceThousand")
    private OcrFieldInfo TDCommodity_Gds_PriceThousand;

    @ApiModelProperty("币别")
    @JsonProperty("TDCommodity_Gds_Curr")
    private OcrFieldInfo TDCommodity_Gds_Curr;

    @ApiModelProperty("金额")
    @JsonProperty("TDCommodity_Gds_AMT")
    private OcrFieldInfo TDCommodity_Gds_AMT;

    @ApiModelProperty("净重")
    @JsonProperty("TDCommodity_Gds_NW")
    private OcrFieldInfo TDCommodity_Gds_NW;

    @ApiModelProperty("净重单位")
    @JsonProperty("TDCommodity_Gds_NWUnit")
    private OcrFieldInfo TDCommodity_Gds_NWUnit;

    @ApiModelProperty("毛重")
    @JsonProperty("TDCommodity_Gds_GW")
    private OcrFieldInfo TDCommodity_Gds_GW;

    @ApiModelProperty("毛重单位")
    @JsonProperty("TDCommodity_Gds_GWUnit")
    private OcrFieldInfo TDCommodity_Gds_GWUnit;

    @ApiModelProperty("原产国")
    @JsonProperty("TDCommodity_Gds_COO")
    private OcrFieldInfo TDCommodity_Gds_COO;

    @ApiModelProperty("件数")
    @JsonProperty("TDCommodity_Gds_PkgQty")
    private OcrFieldInfo TDCommodity_Gds_PkgQty;

    @ApiModelProperty("包装方式")
    @JsonProperty("TDCommodity_Gds_PkgUom")
    private OcrFieldInfo TDCommodity_Gds_PkgUom;

    @ApiModelProperty("包装编号")
    @JsonProperty("TDCommodity_Gds_PkgNo")
    private OcrFieldInfo TDCommodity_Gds_PkgNo;

    }
