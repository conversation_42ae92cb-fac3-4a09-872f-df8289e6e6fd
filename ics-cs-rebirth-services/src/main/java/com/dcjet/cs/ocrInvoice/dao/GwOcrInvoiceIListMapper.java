package com.dcjet.cs.ocrInvoice.dao;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListSumInfo;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwOcrInvoiceIList
* <AUTHOR>
* @date: 2021-9-29
*/
public interface GwOcrInvoiceIListMapper extends Mapper<GwOcrInvoiceIList> {
    /**
     * 查询获取数据
     * @param gwOcrInvoiceIList
     * @return
     */
    List<GwOcrInvoiceIList> getList(GwOcrInvoiceIList gwOcrInvoiceIList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据headid列表获取数据
     * @param headIds 表头sid
     * @return
     */
    List<GwOcrInvoiceIList> selectByHeadIds(@Param("headIds") List<String> headIds);

    @Select("select sum(qty) as qty, sum(dec_total) as total_amount, sum(net_wt) as net_wt, sum(gross_wt) as gross_wt, sum(pack_num) as pack_num from t_gw_ocr_invoice_i_list where head_id = #{headId}")
    GwOcrInvoiceIListSumInfo sumByHeadId(@Param("headId") String headId);
}
