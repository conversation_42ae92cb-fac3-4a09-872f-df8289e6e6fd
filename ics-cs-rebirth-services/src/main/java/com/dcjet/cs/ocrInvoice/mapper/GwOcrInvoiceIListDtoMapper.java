package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceIListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceIListDto toDto(GwOcrInvoiceIList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceIList toPo(GwOcrInvoiceIListParam param);

    /**
     * model -> param
     * @param model
     * @return
     */
    GwOcrInvoiceIListParam poToEntity(GwOcrInvoiceIList model);

    /**
     * 数据库原始数据更新
     * @param gwOcrInvoiceIListParam
     * @param gwOcrInvoiceIList
     */
    void updatePo(GwOcrInvoiceIListParam gwOcrInvoiceIListParam, @MappingTarget GwOcrInvoiceIList gwOcrInvoiceIList);
    default void patchPo(GwOcrInvoiceIListParam gwOcrInvoiceIListParam, GwOcrInvoiceIList gwOcrInvoiceIList) {
        // TODO 自行实现局部更新
    }
}
