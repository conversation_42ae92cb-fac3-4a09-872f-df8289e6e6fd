package com.dcjet.cs.ocrInvoice.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Setter
@Getter
@Table(name = "t_gw_ocr_log")
public class GwOcrLog implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 业务主键(多个时用|分隔)
     */
	@Column(name = "bussiness_pk")
	private  String bussinessPk;
	/**
     * 执行状态(0.调用上传接口成功 1.调用上传接口失败 2.OCR识别完成解析成功 3.OCR识别完成但解析失败)
     */
	@Column(name = "status")
	private  String status;
	/**
     * 解析失败的原因
     */
	@Column(name = "error_message")
	private  String errorMessage;
	/**
     * 业务类型(INV_PACK.发票箱单)
     */
	@Column(name = "bussiness_type")
	private  String bussinessType;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 调用OCR上传接口成功后返回的信息(JSON存储)
     */
	@Column(name = "upload_res")
	private  String uploadRes;
	/**
     * 回填写入业务表的SID
     */
	@Column(name = "task_id")
	private  String taskId;
	/**
	 * 子批次号
	 */
	@Column(name = "sub_task_id")
	private  String subTaskId;
	/**
	 * 单据内部编号
	 */
	@Column(name = "ems_list_no")
	private  String emsListNo;
	/**
	 * 备用字段1(预留)
	 */
	@Column(name = "extend_filed1")
	private String extendFiled1;

	public GwOcrLog initData(UserInfoToken token) {
		this.sid = UUID.randomUUID().toString();
		this.tradeCode = token.getCompany();
		this.insertUser = token.getUserNo();
		this.insertUserName = token.getUserName();
		this.insertTime = new Date();

		return this;
	}
}
