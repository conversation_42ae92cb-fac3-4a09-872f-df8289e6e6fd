package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadParam;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadQueryParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceEHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceEHeadDto toDto(GwOcrInvoiceEHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceEHead toPo(GwOcrInvoiceEHeadParam param);

    /**
     * model -> param
     * @param model
     * @return
     */
    GwOcrInvoiceEHeadParam poToEntity(GwOcrInvoiceEHead model);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceEHead toPo(GwOcrInvoiceEHeadQueryParam param);
    /**
     * 数据库原始数据更新
     * @param GwOcrInvoiceEHeadParam
     * @param GwOcrInvoiceEHead
     */
    void updatePo(GwOcrInvoiceEHeadParam GwOcrInvoiceEHeadParam, @MappingTarget GwOcrInvoiceEHead GwOcrInvoiceEHead);
    default void patchPo(GwOcrInvoiceEHeadParam GwOcrInvoiceEHeadParam, GwOcrInvoiceEHead GwOcrInvoiceEHead) {
        // TODO 自行实现局部更新
    }
}
