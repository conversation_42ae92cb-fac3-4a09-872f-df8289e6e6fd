package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.GwOcrLogDto;
import com.dcjet.cs.dto.ocrInvoice.GwOcrLogParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrLog;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrLogDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrLogDto toDto(GwOcrLog po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrLog toPo(GwOcrLogParam param);
    /**
     * 数据库原始数据更新
     * @param gwOcrLogParam
     * @param gwOcrLog
     */
    void updatePo(GwOcrLogParam gwOcrLogParam, @MappingTarget GwOcrLog gwOcrLog);
    default void patchPo(GwOcrLogParam gwOcrLogParam, GwOcrLog gwOcrLog) {
        // TODO 自行实现局部更新
    }
}
