package com.dcjet.cs.ocrInvoice.model;

import com.dcjet.cs.ocrInvoice.model.ocr.OcrFPData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrTDData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrXDData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Setter
@Getter
@Table(name = "t_gw_ocr_invoice_i_head_loc")
public class GwOcrInvoiceIHeadLoc implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 对应OCR发票箱单表头的SID
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 发票号码
     */
    @Column(name = "invoice_no_loc")
    private String invoiceNoLoc;
    /**
     * 发票日期
     */
    @Column(name = "invoice_date_loc")
    private String invoiceDateLoc;
    /**
     * 装箱单号码
     */
    @Column(name = "packing_no_loc")
    private String packingNoLoc;
    /**
     * 关联号码
     */
    @Column(name = "reference_no_loc")
    private String referenceNoLoc;
    /**
     * 发货人
     */
    @Column(name = "shipper_loc")
    private String shipperLoc;
    /**
     * 境外发货人代码
     */
    @Column(name = "shipper_code_loc")
    private String shipperCodeLoc;
    /**
     * 收货人
     */
    @Column(name = "consignee_loc")
    private String consigneeLoc;
    /**
     * 贸易条款
     */
    @Column(name = "trade_terms_loc")
    private String tradeTermsLoc;
    /**
     * 总数量
     */
    @Column(name = "qty_loc")
    private String qtyLoc;
    /**
     * 总数量单位
     */
    @Column(name = "unit_loc")
    private String unitLoc;
    /**
     * 总金额
     */
    @Column(name = "total_amount_loc")
    private String totalAmountLoc;
    /**
     * 币制
     */
    @Column(name = "curr_loc")
    private String currLoc;
    /**
     * 统一原产国
     */
    @Column(name = "origin_country_loc")
    private String originCountryLoc;
    /**
     * 统一订单号
     */
    @Column(name = "order_no_loc")
    private String orderNoLoc;
    /**
     * 托盘数(件数)
     */
    @Column(name = "pallet_num_loc")
    private String palletNumLoc;
    /**
     * 散箱数
     */
    @Column(name = "bulk_ctns_loc")
    private String bulkCtnsLoc;
    /**
     * 总箱数
     */
    @Column(name = "total_ctns_loc")
    private String totalCtnsLoc;
    /**
     * 总件数
     */
    @Column(name = "pack_num_loc")
    private String packNumLoc;
    /**
     * 总净重
     */
    @Column(name = "net_wt_loc")
    private String netWtLoc;
    /**
     * 净重单位
     */
    @Column(name = "net_wt_unit_loc")
    private String netWtUnitLoc;
    /**
     * 总毛重
     */
    @Column(name = "gross_wt_loc")
    private String grossWtLoc;
    /**
     * 毛重单位
     */
    @Column(name = "gross_wt_unit_loc")
    private String grossWtUnitLoc;
    /**
     * 总体积
     */
    @Column(name = "volume_loc")
    private String volumeLoc;
    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改数据时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "mawb_loc")
    private String mawbLoc;
    @Column(name = "hawb_loc")
    private String hawbLoc;
    @Column(name = "forwarder_loc")
    private String forwarderLoc;
    @Column(name = "depport_loc")
    private String depportLoc;
    @Column(name = "desport_loc")
    private String desportLoc;
    @Column(name = "bill_date_loc")
    private String billDateLoc;
    @Column(name = "flt_no_loc")
    private String fltNoLoc;
    @Column(name = "flt_date_loc")
    private String fltDateLoc;
    @Column(name = "flt_no_1st_loc")
    private String fltNo1stLoc;
    @Column(name = "flt_date_1st_loc")
    private String fltDate1stLoc;
    @Column(name = "freight_loc")
    private String freightLoc;
    @Column(name = "total_chwt_loc")
    private String totalChwtLoc;
    /**
     * 送货单号
     */
    @Column(name = "delivery_note_no_loc")
    private String deliveryNoteNoLoc;
    /**
     * 单据内部编号
     */
    @Column(name = "ems_list_no_loc")
    private String emsListNoLoc;
    /**
     * 合同协议号
     */
    @Column(name = "contr_no_loc")
    private String contrNoLoc;

    public GwOcrInvoiceIHeadLoc initData(GwOcrLog log) {
        this.tradeCode = log.getTradeCode();
        this.insertUser = log.getInsertUser();
        this.insertUserName = log.getInsertUserName();
        this.insertTime = new Date();
        return this;
    }

    public void assembly(OcrFPData ocrFPData) {
        if (ocrFPData.getFPINVOICE_NO() != null) {
            this.setInvoiceNoLoc(ocrFPData.getFPINVOICE_NO().toJson());
        }
        if (ocrFPData.getFPPACKING_NO() != null) {
            this.setPackingNoLoc(ocrFPData.getFPPACKING_NO().toJson());
        }
        if (ocrFPData.getFPREFERENCE_NO() != null) {

            this.setReferenceNoLoc(ocrFPData.getFPREFERENCE_NO().toJson());
        }
        if (ocrFPData.getFPSHIPPER() != null) {
            this.setShipperLoc(ocrFPData.getFPSHIPPER().toJson());
        }
        if (ocrFPData.getFPSHIPPER_CODE() != null) {
            this.setShipperCodeLoc(ocrFPData.getFPSHIPPER_CODE().toJson());
        }
        if (ocrFPData.getFPCONSIGNEE() != null) {
            this.setConsigneeLoc(ocrFPData.getFPCONSIGNEE().toJson());
        }
        if (ocrFPData.getFPINCOTERMS() != null) {
            this.setTradeTermsLoc(ocrFPData.getFPINCOTERMS().toJson());
        }
        if (ocrFPData.getFPTOTAL_QTY() != null) {
            this.setQtyLoc(ocrFPData.getFPTOTAL_QTY().toJson());
        }
        if (ocrFPData.getFPTOTAL_QTYUNIT() != null) {
            this.setUnitLoc(ocrFPData.getFPTOTAL_QTYUNIT().toJson());
        }
        if (ocrFPData.getFPTOTAL_AMOUNT() != null) {
            this.setTotalAmountLoc(ocrFPData.getFPTOTAL_AMOUNT().toJson());
        }
        if (ocrFPData.getFPTOTAL_CURR() != null) {
            this.setCurrLoc(ocrFPData.getFPTOTAL_CURR().toJson());
        }
        if (ocrFPData.getFPHEAD_COO() != null) {
            this.setOriginCountryLoc(ocrFPData.getFPHEAD_COO().toJson());
        }
        if (ocrFPData.getFPHEAD_PO() != null) {
            this.setOrderNoLoc(ocrFPData.getFPHEAD_PO().toJson());
        }
        if (ocrFPData.getFPTOTAL_PALLET() != null) {
            this.setPalletNumLoc(ocrFPData.getFPTOTAL_PALLET().toJson());
        }
        if (ocrFPData.getFPBULK_CTNS() != null) {
            this.setBulkCtnsLoc(ocrFPData.getFPBULK_CTNS().toJson());
        }
        if (ocrFPData.getFPTOTAL_CTNS() != null) {
            this.setTotalCtnsLoc(ocrFPData.getFPTOTAL_CTNS().toJson());
        }
        if (ocrFPData.getFPTOTAL_PKG() != null) {
            this.setPackNumLoc(ocrFPData.getFPTOTAL_PKG().toJson());
        }
        if (ocrFPData.getFPTOTAL_NW() != null) {
            this.setNetWtLoc(ocrFPData.getFPTOTAL_NW().toJson());
        }
        if (ocrFPData.getFPTOTAL_NWUnit() != null) {
            this.setNetWtUnitLoc(ocrFPData.getFPTOTAL_NWUnit().toJson());
        }
        if (ocrFPData.getFPTOTAL_GW() != null) {
            this.setGrossWtLoc(ocrFPData.getFPTOTAL_GW().toJson());
        }
        if (ocrFPData.getFPTOTAL_GWUnit() != null) {
            this.setGrossWtUnitLoc(ocrFPData.getFPTOTAL_GWUnit().toJson());
        }
        if (ocrFPData.getFPTOTAL_MEAS() != null) {
            this.setVolumeLoc(ocrFPData.getFPTOTAL_MEAS().toJson());
        }
        if (ocrFPData.getFPCommodity_Delivery_no() != null) {
            this.setDeliveryNoteNoLoc(ocrFPData.getFPCommodity_Delivery_no().toJson());
        }
        if (ocrFPData.getFPEMS_LIST_NO() != null) {
            this.setEmsListNoLoc(ocrFPData.getFPEMS_LIST_NO().getValue());
        }
        if (ocrFPData.getFPCONTRACT_NO() != null) {
            this.setContrNoLoc(ocrFPData.getFPCONTRACT_NO().toJson());
        }
    }

    public void assembly(OcrXDData ocrXDData) {
        if (ocrXDData.getXDINVOICE_NO() != null) {
            this.setInvoiceNoLoc(ocrXDData.getXDINVOICE_NO().toJson());
        }
        if (ocrXDData.getXDPACKING_NO() != null) {
            this.setPackingNoLoc(ocrXDData.getXDPACKING_NO().toJson());
        }
        if (ocrXDData.getXDREFERENCE_NO() != null) {

            this.setReferenceNoLoc(ocrXDData.getXDREFERENCE_NO().toJson());
        }
        if (ocrXDData.getXDSHIPPER() != null) {
            this.setShipperLoc(ocrXDData.getXDSHIPPER().toJson());
        }
        if (ocrXDData.getXDSHIPPER_CODE() != null) {
            this.setShipperCodeLoc(ocrXDData.getXDSHIPPER_CODE().toJson());
        }
        if (ocrXDData.getXDCONSIGNEE() != null) {
            this.setConsigneeLoc(ocrXDData.getXDCONSIGNEE().toJson());
        }
        if (ocrXDData.getXDINCOTERMS() != null) {
            this.setTradeTermsLoc(ocrXDData.getXDINCOTERMS().toJson());
        }
        if (ocrXDData.getXDTOTAL_QTY() != null) {
            this.setQtyLoc(ocrXDData.getXDTOTAL_QTY().toJson());
        }
        if (ocrXDData.getXDTOTAL_QTYUNIT() != null) {
            this.setUnitLoc(ocrXDData.getXDTOTAL_QTYUNIT().toJson());
        }
        if (ocrXDData.getXDTOTAL_AMOUNT() != null) {
            this.setTotalAmountLoc(ocrXDData.getXDTOTAL_AMOUNT().toJson());
        }
        if (ocrXDData.getXDTOTAL_CURR() != null) {
            this.setCurrLoc(ocrXDData.getXDTOTAL_CURR().toJson());
        }
        if (ocrXDData.getXDHEAD_COO() != null) {
            this.setOriginCountryLoc(ocrXDData.getXDHEAD_COO().toJson());
        }
        if (ocrXDData.getXDHEAD_PO() != null) {
            this.setOrderNoLoc(ocrXDData.getXDHEAD_PO().toJson());
        }
        if (ocrXDData.getXDTOTAL_PALLET() != null) {
            this.setPalletNumLoc(ocrXDData.getXDTOTAL_PALLET().toJson());
        }
        if (ocrXDData.getXDBULK_CTNS() != null) {
            this.setBulkCtnsLoc(ocrXDData.getXDBULK_CTNS().toJson());
        }
        if (ocrXDData.getXDTOTAL_CTNS() != null) {
            this.setTotalCtnsLoc(ocrXDData.getXDTOTAL_CTNS().toJson());
        }
        if (ocrXDData.getXDTOTAL_PKG() != null) {
            this.setPackNumLoc(ocrXDData.getXDTOTAL_PKG().toJson());
        }
        if (ocrXDData.getXDTOTAL_NW() != null) {
            this.setNetWtLoc(ocrXDData.getXDTOTAL_NW().toJson());
        }
        if (ocrXDData.getXDTOTAL_NWUnit() != null) {
            this.setNetWtUnitLoc(ocrXDData.getXDTOTAL_NWUnit().toJson());
        }
        if (ocrXDData.getXDTOTAL_GW() != null) {
            this.setGrossWtLoc(ocrXDData.getXDTOTAL_GW().toJson());
        }
        if (ocrXDData.getXDTOTAL_GWUnit() != null) {
            this.setGrossWtUnitLoc(ocrXDData.getXDTOTAL_GWUnit().toJson());
        }
        if (ocrXDData.getXDTOTAL_MEAS() != null) {
            this.setVolumeLoc(ocrXDData.getXDTOTAL_MEAS().toJson());
        }
    }

    public void assembly(OcrTDData ocrTDData) {
        if (ocrTDData.getTDINVOICE_NO() != null) {
            this.setInvoiceNoLoc(ocrTDData.getTDINVOICE_NO().toJson());
        }
        if (ocrTDData.getTDPACKING_NO() != null) {
            this.setPackingNoLoc(ocrTDData.getTDPACKING_NO().toJson());
        }
        if (ocrTDData.getTDREFERENCE_NO() != null) {

            this.setReferenceNoLoc(ocrTDData.getTDREFERENCE_NO().toJson());
        }
        if (ocrTDData.getTDSHIPPER() != null) {
            this.setShipperLoc(ocrTDData.getTDSHIPPER().toJson());
        }
        if (ocrTDData.getTDSHIPPER_CODE() != null) {
            this.setShipperCodeLoc(ocrTDData.getTDSHIPPER_CODE().toJson());
        }
        if (ocrTDData.getTDCONSIGNEE() != null) {
            this.setConsigneeLoc(ocrTDData.getTDCONSIGNEE().toJson());
        }
        if (ocrTDData.getTDINCOTERMS() != null) {
            this.setTradeTermsLoc(ocrTDData.getTDINCOTERMS().toJson());
        }
        if (ocrTDData.getTDTOTAL_QTY() != null) {
            this.setQtyLoc(ocrTDData.getTDTOTAL_QTY().toJson());
        }
        if (ocrTDData.getTDTOTAL_QTYUNIT() != null) {
            this.setUnitLoc(ocrTDData.getTDTOTAL_QTYUNIT().toJson());
        }
        if (ocrTDData.getTDTOTAL_AMOUNT() != null) {
            this.setTotalAmountLoc(ocrTDData.getTDTOTAL_AMOUNT().toJson());
        }
        if (ocrTDData.getTDTOTAL_CURR() != null) {
            this.setCurrLoc(ocrTDData.getTDTOTAL_CURR().toJson());
        }
        if (ocrTDData.getTDHEAD_COO() != null) {
            this.setOriginCountryLoc(ocrTDData.getTDHEAD_COO().toJson());
        }
        if (ocrTDData.getTDHEAD_PO() != null) {
            this.setOrderNoLoc(ocrTDData.getTDHEAD_PO().toJson());
        }
        if (ocrTDData.getTDTOTAL_PALLET() != null) {
            this.setPalletNumLoc(ocrTDData.getTDTOTAL_PALLET().toJson());
        }
        if (ocrTDData.getTDBULK_CTNS() != null) {
            this.setBulkCtnsLoc(ocrTDData.getTDBULK_CTNS().toJson());
        }
        if (ocrTDData.getTDTOTAL_CTNS() != null) {
            this.setTotalCtnsLoc(ocrTDData.getTDTOTAL_CTNS().toJson());
        }
        if (ocrTDData.getTDTOTAL_PKG() != null) {
            this.setPackNumLoc(ocrTDData.getTDTOTAL_PKG().toJson());
        }
        if (ocrTDData.getTDTOTAL_NW() != null) {
            this.setNetWtLoc(ocrTDData.getTDTOTAL_NW().toJson());
        }
        if (ocrTDData.getTDTOTAL_NWUnit() != null) {
            this.setNetWtUnitLoc(ocrTDData.getTDTOTAL_NWUnit().toJson());
        }
        if (ocrTDData.getTDTOTAL_GW() != null) {
            this.setGrossWtLoc(ocrTDData.getTDTOTAL_GW().toJson());
        }
        if (ocrTDData.getTDTOTAL_GWUnit() != null) {
            this.setGrossWtUnitLoc(ocrTDData.getTDTOTAL_GWUnit().toJson());
        }
        if (ocrTDData.getTDTOTAL_MEAS() != null) {
            this.setVolumeLoc(ocrTDData.getTDTOTAL_MEAS().toJson());
        }
        if (ocrTDData.getTDMAWB() != null) {
            this.setMawbLoc(ocrTDData.getTDMAWB().toJson());
        }
        if (ocrTDData.getTDHAWB() != null) {
            this.setHawbLoc(ocrTDData.getTDHAWB().toJson());
        }
        if (ocrTDData.getTDFORWARDER() != null) {
            this.setForwarderLoc(ocrTDData.getTDFORWARDER().toJson());
        }
        if (ocrTDData.getTDDEPPORT() != null) {
            this.setDepportLoc(ocrTDData.getTDDEPPORT().toJson());
        }
        if (ocrTDData.getTDDESPORT() != null) {
            this.setDesportLoc(ocrTDData.getTDDESPORT().toJson());
        }
        if (ocrTDData.getTDBILL_DATE() != null) {
            this.setBillDateLoc(ocrTDData.getTDBILL_DATE().toJson());
        }
        if (ocrTDData.getTDFLT_NO() != null) {
            this.setFltNoLoc(ocrTDData.getTDFLT_NO().toJson());
        }
        if (ocrTDData.getTDFLT_DATE() != null) {
            this.setFltDateLoc(ocrTDData.getTDFLT_DATE().toJson());
        }
        if (ocrTDData.getTDFLT_NO_1ST() != null) {
            this.setFltNo1stLoc(ocrTDData.getTDFLT_NO_1ST().toJson());
        }
        if (ocrTDData.getTDFLT_DATE_1ST() != null) {
            this.setFltDate1stLoc(ocrTDData.getTDFLT_DATE_1ST().toJson());
        }
        if (ocrTDData.getTDFREIGHT() != null) {
            this.setFreightLoc(ocrTDData.getTDFREIGHT().toJson());
        }
        if (ocrTDData.getTDTOTAL_CHWT() != null) {
            this.setTotalChwtLoc(ocrTDData.getTDTOTAL_CHWT().toJson());
        }
    }
}
