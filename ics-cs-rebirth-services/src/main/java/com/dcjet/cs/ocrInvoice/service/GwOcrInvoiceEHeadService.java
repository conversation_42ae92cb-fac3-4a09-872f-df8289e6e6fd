package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.dao.BiCustomerParamsMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.ocrInvoice.OcrInvoiceSumInfo;
import com.dcjet.cs.dto.ocrInvoice.ocrE.*;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.erp.service.DecCommonService;
import com.dcjet.cs.erp.service.DecErpEListNService;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.ocrInvoice.component.exp.OcrDataValidationException;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceEHeadMapper;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceEListMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceEHeadDtoMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceEListDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEHead;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEList;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHead;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.FileUtil;
import com.dcjet.cs.util.ValidatorUtil;
import com.dcjet.cs.util.pageSort.DcPageSort;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.dcjet.cs.util.FunctionUtil.distinctByKey;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Service
public class GwOcrInvoiceEHeadService extends BaseService<GwOcrInvoiceEHead> {

    @Resource
    private GwOcrInvoiceEHeadMapper mapper;
    @Resource
    private GwOcrInvoiceEHeadDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceEHead> getMapper() {
        return mapper;
    }
    @Resource
    private GwOcrInvoiceEListMapper listMapper;
    @Resource
    private GwOcrInvoiceEListDtoMapper listDtoMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecCommonService decCommonService;
    @Resource
    private BiCustomerParamsMapper biCustomerParamsMapper;
    @Resource
    private GwOcrInvoiceIEService gwOcrInvoiceIEService;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Value("${dc.export.temp:}")
    private String tempPath;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    private static Map<String, Field> mapColumn = new LinkedHashMap<>();
    static {
        try {
            mapColumn.put("总数量", GwOcrInvoiceEHead.class.getDeclaredField("qty"));
            mapColumn.put("总金额", GwOcrInvoiceEHead.class.getDeclaredField("totalAmount"));
            mapColumn.put("总件数", GwOcrInvoiceEHead.class.getDeclaredField("packNum"));
            mapColumn.put("总体积", GwOcrInvoiceEHead.class.getDeclaredField("volume"));
            mapColumn.put("总净重", GwOcrInvoiceEHead.class.getDeclaredField("netWt"));
            mapColumn.put("总毛重", GwOcrInvoiceEHead.class.getDeclaredField("grossWt"));
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    };

    @Resource
    private ValidatorUtil validatorUtil;
    @Resource
    private DecErpEListNService decErpEListNService;
    @Resource
    private FastdFsService fastdFsService;

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceEHeadQueryParam
     * @param pageParam
     * @return
     */
    @SneakyThrows
    @DcPageSort
    public ResultObject<List<GwOcrInvoiceEHeadDto>> getListPaged(GwOcrInvoiceEHeadQueryParam gwOcrInvoiceEHeadQueryParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceEHead gwOcrInvoiceEHead = dtoMapper.toPo(gwOcrInvoiceEHeadQueryParam);
        Page<GwOcrInvoiceEHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceEHead));
        List<GwOcrInvoiceEHeadDto> gwOcrInvoiceEHeadDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceEHeadDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        // 设置businessNo
        gwOcrInvoiceEHeadDtos.stream().forEach(e-> e.setBusinessNoByDataType());
        ResultObject<List<GwOcrInvoiceEHeadDto>> paged = ResultObject.createInstance(gwOcrInvoiceEHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceEHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEHeadDto insert(GwOcrInvoiceEHeadParam gwOcrInvoiceEHeadParam, UserInfoToken userInfo) {
        GwOcrInvoiceEHead gwOcrInvoiceEHead = dtoMapper.toPo(gwOcrInvoiceEHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceEHead.setSid(sid);
        gwOcrInvoiceEHead.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceEHead.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceEHead);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceEHead) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceEHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEHeadDto update(GwOcrInvoiceEHeadParam gwOcrInvoiceEHeadParam, UserInfoToken userInfo) {
        GwOcrInvoiceEHead head = mapper.selectByPrimaryKey(gwOcrInvoiceEHeadParam.getSid());
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据已删除，请重新查询"));
        }
        if ("2".equals(head.getCreateStatus())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已生成状态的数据不可编辑"));
        }
        dtoMapper.updatePo(gwOcrInvoiceEHeadParam, head);
        if (checkRepeatForUpdate(head)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据重复"));
        }
        head.setUpdateUser(userInfo.getUserNo());
        head.setUpdateUserName(userInfo.getUserName());
        head.setUpdateTime(new Date());
        head.setModifyStatus("2");
        //企业参数库转换
        gwOcrInvoiceIEService.setBiCustomerParams(userInfo);
        gwOcrInvoiceIEService.convertBasicParamsE(head);
        // 更新数据
        int update = mapper.updateByPrimaryKey(head);
        return update > 0 ? dtoMapper.toDto(head) : null;
    }

    private boolean checkRepeatForUpdate(GwOcrInvoiceEHead gwOcrInvoiceEHead) {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeCode", gwOcrInvoiceEHead.getTradeCode());
        criteria.andNotEqualTo("sid", gwOcrInvoiceEHead.getSid());
        String dataType = gwOcrInvoiceEHead.getDataType();
        criteria.andEqualTo("dataType", dataType);
        if ("INVOICE".equals(dataType) || "PACKING".equals(dataType)) {
            criteria.andEqualTo("invoiceNo", gwOcrInvoiceEHead.getInvoiceNo());
        } else if ("LADING".equals(dataType)) {
            criteria.andEqualTo("mawb", gwOcrInvoiceEHead.getMawb());
            if (StringUtils.isNotEmpty(gwOcrInvoiceEHead.getHawb())) {
                criteria.andEqualTo("hawb", gwOcrInvoiceEHead.getHawb());
            }
        } else {
            criteria.andCondition("1=2");
        }
        int cnt = mapper.selectCountByExample(example);
        return cnt > 0;
    }


    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) throws Exception {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sid", sids);
        criteria.andNotEqualTo("createStatus", "1");
        int cnt = mapper.selectCountByExample(example);
        if (cnt > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有状态为[已识别]的数据才可以删除"));
        }

        List<String> listSids = mapper.selectSubTaskId(sids);
        // 删除附件
        Example example2 = new Example(Attached.class);
        Example.Criteria criteria2 = example2.createCriteria();
        criteria2.andIn("businessSid", listSids);
        criteria2.andEqualTo("tradeCode", userInfo.getCompany());
        List<Attached> attachedList = attachedMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(attachedList)) {
            for (Attached attached : attachedList) {
                fileHandler.deleteFile(attached.getFileName());
                attachedMapper.deleteByPrimaryKey(attached.getSid());
            }
        }
        mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceEHeadDto> selectAll(GwOcrInvoiceEHeadQueryParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceEHead gwOcrInvoiceEHead = dtoMapper.toPo(exportParam);
        gwOcrInvoiceEHead.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceEHeadDto> gwOcrInvoiceEHeadDtos = new ArrayList<>();
        List<GwOcrInvoiceEHead> gwOcrInvoiceEHeads = mapper.getList(gwOcrInvoiceEHead);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceEHeads)) {
            gwOcrInvoiceEHeadDtos = gwOcrInvoiceEHeads.stream().map(head -> {
                GwOcrInvoiceEHeadDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceEHeadDtos;
    }

    /**
     * getOne
     * @param gwOcrInvoiceEHeadQueryParam
     * @return
     */
    public GwOcrInvoiceEHeadDto getOne(GwOcrInvoiceEHeadQueryParam gwOcrInvoiceEHeadQueryParam) {
        // 启用分页查询
        GwOcrInvoiceEHead gwOcrInvoiceEHead = dtoMapper.toPo(gwOcrInvoiceEHeadQueryParam);
        List<GwOcrInvoiceEHeadDto> gwOcrInvoiceEHeadDtos = mapper.getList(gwOcrInvoiceEHead).stream().map(head -> {
            GwOcrInvoiceEHeadDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(gwOcrInvoiceEHeadDtos)) {
            return null;
        }
        return gwOcrInvoiceEHeadDtos.get(0);
    }

    /**
     * 是否已存在发票、箱单
     * @param dataType
     * @param invoiceNo
     * @return
     */
    public Boolean isExistsInvoice(String dataType, String invoiceNo, String tradeCode) {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeCode", tradeCode);
        criteria.andEqualTo("dataType", dataType);
        criteria.andEqualTo("invoiceNo", invoiceNo);
        int cnt = mapper.selectCountByExample(example);
        return cnt > 0;
    }

    /**
     * 是否存在提单
     * @param dataType
     * @param mawb 主提运单号
     * @param hawb 分提运单号
     * @param tradeCode
     * @return
     */
    public Boolean isExistsBill(String dataType, String mawb, String hawb, String tradeCode) {
        Example example = new Example(GwOcrInvoiceIHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeCode", tradeCode);
        criteria.andEqualTo("dataType", dataType);
        criteria.andEqualTo("mawb", mawb);
        if (StringUtils.isNotEmpty(hawb)) {
            criteria.andEqualTo("hawb", hawb);
        }
        int cnt = mapper.selectCountByExample(example);
        return cnt > 0;
    }

    /**
     * 功能描述 ocr识别数据进口预录入单信息
     * <AUTHOR>
     * @date 2021-10-09
     * @version 1.0
     * @param gwOcrEHeadDecParam 1
     * @param userInfo 2
     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<DecErpEHeadN> createDec(GwOcrEHeadDecParam gwOcrEHeadDecParam, UserInfoToken userInfo) throws Exception {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("生成成功"));
        List<GwOcrInvoiceEHead> gwOcrInvoiceEHeads = mapper.selectBySids(gwOcrEHeadDecParam.getSids());
        List<String> invoiceListSid = gwOcrInvoiceEHeads.stream().filter(x->"INVOICE".equals(x.getDataType())).map(GwOcrInvoiceEHead::getSid).collect(Collectors.toList());
        List<String> packingListSid = gwOcrInvoiceEHeads.stream().filter(x->"PACKING".equals(x.getDataType())).map(GwOcrInvoiceEHead::getSid).collect(Collectors.toList());
        List<GwOcrInvoiceEList> gwOcrInvoiceELists = new ArrayList<>();
        List<GwOcrInvoiceEList> gwOcrPackingELists = new ArrayList<>();
        if(invoiceListSid.size()>0) {
            gwOcrInvoiceELists = listMapper.selectByHeadIds(invoiceListSid);
        }
        if(packingListSid.size()>0) {
            gwOcrPackingELists = listMapper.selectByHeadIds(packingListSid);
        }
        if(gwOcrInvoiceEHeads.size() > 0) {
            List<GwOcrInvoiceEHead> createList = gwOcrInvoiceEHeads.stream().filter(x->"2".equals(x.getCreateStatus())).collect(Collectors.toList());
            if(createList.size()>0) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("选中的发票中有已生成数据，无法生成"));
            }
            List<GwOcrInvoiceEHead> createList2 = gwOcrInvoiceEHeads.stream().filter(e -> StringUtils.isNotBlank(e.getEmsListNo())).filter(distinctByKey(GwOcrInvoiceEHead::getEmsListNo)).collect(Collectors.toList());
            if(createList2.size()>1) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("存在不同单据内部编号的发票/箱单，生成失败！"));
            }
            List<GwOcrInvoiceEHead> decList = gwOcrInvoiceEHeads.stream().filter(x->"LADING".equals(x.getDataType())).collect(Collectors.toList());
            if(decList.size() > 1) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("不允许选择多份提单生成，生成失败！"));
            }
            List<GwOcrInvoiceEHead> gwOcrInvoiceEHeadSums = mapper.selectSumBySids(gwOcrEHeadDecParam.getSids());
            String invoiceNoList = gwOcrInvoiceEHeads.stream().filter(x->x.getInvoiceNo() != null).map(GwOcrInvoiceEHead::getInvoiceNo).distinct().collect(Collectors.joining("/"));
            DecErpEHeadN decErpEHeadN = new DecErpEHeadN();
            String sid = UUID.randomUUID().toString();
            decErpEHeadN.setSid(sid);
            decErpEHeadN.setEntryClearanceType("M");
            decErpEHeadN.setDeclTrnrel("0");
            decErpEHeadN.setMergeType("0");
            decErpEHeadN.setDataSource(CommonEnum.dataSourceEnum.SOURCE_15.getCode());
            decErpEHeadN.setEmsListNo(gwOcrEHeadDecParam.getEmsListNo());
            gwOcrInvoiceEHeadSums.removeAll(Collections.singleton(null));
            if(gwOcrInvoiceEHeadSums.size() > 0) {
                GwOcrInvoiceEHead gwOcrInvoiceEHead = gwOcrInvoiceEHeadSums.stream().findFirst().get();
                decErpEHeadN.setTradeTerms(gwOcrInvoiceEHead.getTradeTerms());
                decErpEHeadN.setDestinationCountry(gwOcrInvoiceEHead.getOriginCountryConvert());
                decErpEHeadN.setInvoiceNo(invoiceNoList);//斜杠汇总
                /* 业务需求,不带入总数量
                if (gwOcrInvoiceEHead.getQty() != null && gwOcrInvoiceEHead.getQty().compareTo(BigDecimal.ZERO) > 0) {
                    decErpEHeadN.setSumQty(gwOcrInvoiceEHead.getQty());//汇总
                }*/
                if (gwOcrInvoiceEHead.getTotalAmount() != null && gwOcrInvoiceEHead.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    decErpEHeadN.setSumDecTotal(gwOcrInvoiceEHead.getTotalAmount());//汇总
                }
                if (gwOcrInvoiceEHead.getPackNum() != null && gwOcrInvoiceEHead.getPackNum() > 0) {
                    decErpEHeadN.setPackNum(new BigDecimal(gwOcrInvoiceEHead.getPackNum()));//汇总
                }
                if (gwOcrInvoiceEHead.getVolume() != null && gwOcrInvoiceEHead.getVolume().compareTo(BigDecimal.ZERO) > 0) {
                    decErpEHeadN.setVolume(gwOcrInvoiceEHead.getVolume());//汇总
                }
                if (gwOcrInvoiceEHead.getNetWt() != null && gwOcrInvoiceEHead.getNetWt().compareTo(BigDecimal.ZERO) > 0) {
                    decErpEHeadN.setNetWt(gwOcrInvoiceEHead.getNetWt());
                    if (StringUtils.isNotBlank(gwOcrInvoiceEHead.getNetWtUnitConvert()) && "036".equals(gwOcrInvoiceEHead.getNetWtUnitConvert().toUpperCase())) {
                        decErpEHeadN.setNetWt(gwOcrInvoiceEHead.getNetWt().divide(new BigDecimal(1000), 5, BigDecimal.ROUND_HALF_UP));
                    }
                }
                if (gwOcrInvoiceEHead.getGrossWt() != null && gwOcrInvoiceEHead.getGrossWt().compareTo(BigDecimal.ZERO) > 0) {
                    decErpEHeadN.setGrossWt(gwOcrInvoiceEHead.getGrossWt());
                    if (StringUtils.isNotBlank(gwOcrInvoiceEHead.getGrossWtUnitConvert()) && "036".equals(gwOcrInvoiceEHead.getGrossWtUnitConvert().toUpperCase())) {
                        decErpEHeadN.setGrossWt(gwOcrInvoiceEHead.getGrossWt().divide(new BigDecimal(1000), 5, BigDecimal.ROUND_HALF_UP));
                    }
                }
                //赋值境外收发货人
                decErpEHeadN.setOverseasShipper(null);
            }
            if(decList.size()>0) {
                if (decErpEHeadN.getPackNum() == null&&decList.get(0).getPackNum()!=null) {
                    decErpEHeadN.setPackNum(BigDecimal.valueOf(decList.get(0).getPackNum()));
                }
                if (decErpEHeadN.getGrossWt() == null) {
                    decErpEHeadN.setGrossWt(decList.get(0).getGrossWt());
                }
                BiCustomerParams biCustomerParams = new BiCustomerParams();
                biCustomerParams.setTradeCode(userInfo.getCompany());
                biCustomerParams.setParamsType("PORT");
                List<BiCustomerParams> biCustomerPort = biCustomerParamsMapper.getList(biCustomerParams);
                decErpEHeadN.setMawb(decList.get(0).getMawb());
                decErpEHeadN.setHawb(decList.get(0).getHawb());
                decErpEHeadN.setVoyageNo(decList.get(0).getFltNo());
                decErpEHeadN.setCweight(decList.get(0).getTotalChwt());
                if(StringUtils.isNotBlank(decList.get(0).getDepport())) {
                    List<BiCustomerParams> biCustomerParamsList = biCustomerPort.stream().filter(x -> x.getParamsCode().equals(decList.get(0).getDepport())).collect(Collectors.toList());
                    if (biCustomerParamsList.size() > 0) {
                        decErpEHeadN.setDespPort(biCustomerParamsList.get(0).getCustomParamCode());
                        decErpEHeadN.setTradeCountry(biCustomerParamsList.get(0).getNote());
                    }
                }
                if(StringUtils.isNotBlank(decList.get(0).getDesport())) {
                    List<BiCustomerParams> biCustomerParamsList = biCustomerPort.stream().filter(x -> x.getParamsCode().equals(decList.get(0).getDesport())).collect(Collectors.toList());
                    if (biCustomerParamsList.size() > 0) {
                        decErpEHeadN.setDestPort(biCustomerParamsList.get(0).getCustomParamCode());
                    }
                }
            }
            //当勾选的数据类型中包含箱单时,发票每行净重、毛重按均摊方式赋值
            if(gwOcrPackingELists.size() > 0) {
                //对箱单中的净重单位进行转换
                for(GwOcrInvoiceEList gwOcrInvoiceEList : gwOcrPackingELists) {
                    if(StringUtils.isNotBlank(gwOcrInvoiceEList.getNetWtUnitConvert())&&"036".equals(gwOcrInvoiceEList.getNetWtUnitConvert().toUpperCase())) {
                        gwOcrInvoiceEList.setNetWt(gwOcrInvoiceEList.getNetWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                    }
                    if(StringUtils.isNotBlank(gwOcrInvoiceEList.getGrossWtUnitConvert())&&"036".equals(gwOcrInvoiceEList.getGrossWtUnitConvert().toUpperCase())) {
                        gwOcrInvoiceEList.setGrossWt(gwOcrInvoiceEList.getGrossWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                    }
                }
                for(GwOcrInvoiceEList eList : gwOcrInvoiceELists) {
                    String facGNo = eList.getFacGNo();
                    BigDecimal qty = eList.getQty();
                    if(qty != null) {
                        //获取当前料号在箱单中的汇总数量
                        BigDecimal qtyPackingTotal = gwOcrPackingELists.stream().filter(s -> StringUtils.equals(s.getFacGNo(),facGNo) && s.getQty() != null).map(GwOcrInvoiceEList::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //获取当前料号在箱单中的汇总净重
                        BigDecimal netWtPackingTotal = gwOcrPackingELists.stream().filter(s -> StringUtils.equals(s.getFacGNo(),facGNo) && s.getNetWt() != null).map(GwOcrInvoiceEList::getNetWt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //获取当前料号在箱单中的汇总毛重
                        BigDecimal grossWtPackingTotal = gwOcrPackingELists.stream().filter(s -> StringUtils.equals(s.getFacGNo(),facGNo) && s.getGrossWt() != null).map(GwOcrInvoiceEList::getGrossWt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if(qtyPackingTotal != null && qtyPackingTotal.compareTo(BigDecimal.ZERO) != 0) {
                            //计算表体当前料号均摊净重
                            BigDecimal netWt = netWtPackingTotal.multiply(qty.divide(qtyPackingTotal,8, BigDecimal.ROUND_HALF_UP));
                            eList.setNetWt(netWt);
                        }
                        if(grossWtPackingTotal != null && grossWtPackingTotal.compareTo(BigDecimal.ZERO) != 0) {
                            //计算表体当前料号均摊毛重
                            BigDecimal grossWt = grossWtPackingTotal.multiply(qty.divide(qtyPackingTotal,8, BigDecimal.ROUND_HALF_UP));
                            eList.setGrossWt(grossWt);
                        }
                    }
                }
            }
            List<DecErpEListN> decErpEListNList = new ArrayList<>();
            for(GwOcrInvoiceEList gwOcrInvoiceEList : gwOcrInvoiceELists) {
                DecErpEListN decErpEListN = new DecErpEListN();
                decErpEListN.setEmsListNo(gwOcrEHeadDecParam.getEmsListNo());
                decErpEListN.setOrderNo(gwOcrInvoiceEList.getOrderNo());
                decErpEListN.setFacGNo(gwOcrInvoiceEList.getFacGNo());
                decErpEListN.setQty(gwOcrInvoiceEList.getQty());
                decErpEListN.setQty1(gwOcrInvoiceEList.getQty1());
                decErpEListN.setDecPrice(gwOcrInvoiceEList.getDecPrice());
                decErpEListN.setDecTotal(gwOcrInvoiceEList.getDecTotal());
                decErpEListN.setCurr(gwOcrInvoiceEList.getCurrConvert());
                decErpEListN.setNetWt(gwOcrInvoiceEList.getNetWt());

                decErpEListN.setUnitErp(gwOcrInvoiceEList.getUnit());//用于申报数量比例因子转换
                GwOcrInvoiceEHead gwOcrInvoiceEHeadSelf = gwOcrInvoiceEHeads.stream().filter(x->x.getSid().equals(gwOcrInvoiceEList.getHeadId())).findFirst().get();
                decErpEListN.setInvoiceNo(gwOcrInvoiceEHeadSelf.getInvoiceNo());
                if(StringUtils.isNotBlank(gwOcrInvoiceEList.getNetWtUnitConvert())&&"036".equals(gwOcrInvoiceEList.getNetWtUnitConvert().toUpperCase())) {
                    decErpEListN.setNetWt(gwOcrInvoiceEList.getNetWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                }
                decErpEListN.setGrossWt(gwOcrInvoiceEList.getGrossWt());
                if(StringUtils.isNotBlank(gwOcrInvoiceEList.getGrossWtUnitConvert())&&"036".equals(gwOcrInvoiceEList.getGrossWtUnitConvert().toUpperCase())) {
                    decErpEListN.setGrossWt(gwOcrInvoiceEList.getGrossWt().divide(new BigDecimal(1000),8, BigDecimal.ROUND_HALF_UP));
                }
//                decErpEListN.setSerialNo(new BigDecimal(gwOcrInvoiceEList.getSerialNo()));
                decErpEListN.setDestinationCountry(gwOcrInvoiceEList.getOriginCountryConvert());
                decErpEListNList.add(decErpEListN);
            }
            decErpEHeadN.setGMark("E");
            result = decCommonService.insertAllE(decErpEHeadN, decErpEListNList,userInfo,null,true,null,gwOcrEHeadDecParam.getTemplateId());
            if(!result.isSuccess()) {
                return result;
            }
            decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(sid);

            //更新表体汇总数据到表头
            decErpEHeadN = decErpEListNService.updateSumAll(decErpEHeadN, decErpEHeadN.getSid());

            if(gwOcrInvoiceEHeadSums.size() > 0) {
                //判断如果OCR有值,取OCR值
                GwOcrInvoiceEHead gwOcrInvoiceEHead = gwOcrInvoiceEHeadSums.stream().findFirst().get();
                //判断如果境外收货人代码不为空
                if(StringUtils.isNotBlank(gwOcrInvoiceEHead.getShipperCode())) {
                    BiClientInformation biClientInformation = new BiClientInformation();
                    biClientInformation.setTradeCode(userInfo.getCompany());
                    //客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)
                    biClientInformation.setCustomerType(CommonVariable.CLI);
                    biClientInformation.setCustomerCode(gwOcrInvoiceEHead.getShipperCode());
                    List<BiClientInformation> biClientInformations = biClientInformationMapper.select(biClientInformation);
                    if (biClientInformations.size() == 1) {
                        //境外收货人代码
                        decErpEHeadN.setOverseasShipper(gwOcrInvoiceEHead.getShipperCode());
                        //境外收货人名称
                        decErpEHeadN.setOverseasShipperName(biClientInformations.get(0).getCompanyName());
                        //境外收货人AEO
                        decErpEHeadN.setOverseasShipperAeo(biClientInformations.get(0).getAeoCode());
                        decErpEHeadNMapper.updateByPrimaryKey(decErpEHeadN);
                    }else{
                        //判断境外收货人不为空
                        if(StringUtils.isNotBlank(gwOcrInvoiceEHead.getConsignee())) {
                            //境外收货人代码
                            decErpEHeadN.setOverseasShipper(gwOcrInvoiceEHead.getShipperCode());
                            //境外收货人名称
                            decErpEHeadN.setOverseasShipperName(gwOcrInvoiceEHead.getConsignee());
                            decErpEHeadNMapper.updateByPrimaryKey(decErpEHeadN);
                        }
                    }
                }else {
                    //判断境外收货人不为空
                    if(StringUtils.isNotBlank(gwOcrInvoiceEHead.getConsignee())) {
                        //境外收货人代码
                        decErpEHeadN.setOverseasShipper(null);
                        //境外收货人Aeo
                        decErpEHeadN.setOverseasShipperAeo(null);
                        //境外收货人名称
                        decErpEHeadN.setOverseasShipperName(gwOcrInvoiceEHead.getConsignee());
                        decErpEHeadNMapper.updateByPrimaryKey(decErpEHeadN);
                    }
                }
            }
            //更新OCR表头状态
            for(GwOcrInvoiceEHead gwOcrInvoiceEHead1 :gwOcrInvoiceEHeads) {
                gwOcrInvoiceEHead1.setCreateStatus("2");
                gwOcrInvoiceEHead1.setHeadId(sid);
                gwOcrInvoiceEHead1.setEmsListNo(gwOcrEHeadDecParam.getEmsListNo());
                mapper.updateByPrimaryKey(gwOcrInvoiceEHead1);
            }
            //插入附件
            String uuid = UUID.randomUUID().toString();
            String theTempPath = tempPath + "/" + uuid + "/";
            //判断文件夹是否存在
            File dir = new File(theTempPath);
            if (!dir.exists()) {
                dir.mkdir();
            }
            //定义附件Map,用于判断同附件名附件是否已生成,解决附件重复问题
            Map<String,Attached> attachedMap = new HashMap<>();
            for(GwOcrInvoiceEHead ocrHead : gwOcrInvoiceEHeads) {
                Example example = new Example(Attached.class);
                Example.Criteria criteria = example.createCriteria();
                if(StringUtils.isBlank(ocrHead.getSubTaskId())) {
                    continue;
                }
                criteria.andEqualTo("businessSid", ocrHead.getSubTaskId());
                List<Attached> attachedList = attachedMapper.selectByExample(example);
                for(Attached att : attachedList) {
                    String originFileName = att.getOriginFileName();
                    if(!attachedMap.containsKey(originFileName)) {
                        String fastUrl = att.getFileName();
                        String filePath = theTempPath + originFileName;
                        fastdFsService.downloadFileToLocal(fastUrl,filePath);
                        File file = new File(filePath);
                        if(file.exists()) {
                            gwOcrInvoiceIEService.addAttached(decErpEHeadN.getSid(), DecVariable.ATTACHED_EM, CommonEnum.OCR_FILE_TYPE_ENUM.getValue(ocrHead.getDataType()),filePath,originFileName,userInfo);
                        }
                        attachedMap.put(originFileName,att);
                    }
                }
            }
            //删除临时文件
            FileUtil.deleteFileTree(theTempPath);
        }
        return result;
    }

    /**
     * 进口ocr汇总预览
     * @param sids
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    @SneakyThrows
    public List<OcrInvoiceSumInfo> getSumInfo(List<String> sids) {
        if(CollectionUtils.isEmpty(sids)) {
            return null;
        }
        Example example = new Example(GwOcrInvoiceEHead.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andIn("sid", sids);
        List<GwOcrInvoiceEHead> list = mapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 汇总
        Map<String, Optional<GwOcrInvoiceEHead>> map = list.stream().collect(Collectors.groupingBy(e->e.getDataType(), Collectors.reducing((a,b) -> sumab(a, b))));
        // 行列转置
        List<OcrInvoiceSumInfo> sumInfos = new ArrayList<>();
        for(Map.Entry<String, Field> entry : mapColumn.entrySet()) {
            OcrInvoiceSumInfo sumInfo = new OcrInvoiceSumInfo();
            sumInfo.setColType("head");
            sumInfo.setColName(entry.getKey());
            entry.getValue().setAccessible(true);
            if (entry.getValue().getType() != BigDecimal.class) {
                if (map.containsKey("INVOICE")) {
                    if (map.get("INVOICE").get().getPackNum() != null) {
                        sumInfo.setFpValue(new BigDecimal(map.get("INVOICE").get().getPackNum()));
                    }
                }
                if (map.containsKey("PACKING")) {
                    if (map.get("PACKING").get().getPackNum() != null) {
                        sumInfo.setXdValue(new BigDecimal(map.get("PACKING").get().getPackNum()));
                    }
                }
            } else {
                if (map.containsKey("INVOICE")) {
                    sumInfo.setFpValue((BigDecimal) entry.getValue().get(map.get("INVOICE").get()));
                }
                if (map.containsKey("PACKING")) {
                    sumInfo.setXdValue((BigDecimal) entry.getValue().get(map.get("PACKING").get()));
                }
            }
            sumInfo.clacDiff();

            sumInfos.add(sumInfo);
        }
        return sumInfos;
    }

    private GwOcrInvoiceEHead sumab(GwOcrInvoiceEHead a, GwOcrInvoiceEHead b) {
        if (b.getQty() != null) {
            if (a.getQty() != null) {
                a.setQty(a.getQty().add(b.getQty()));
            } else {
                a.setQty(b.getQty());
            }
        }
        if (b.getTotalAmount() != null) {
            if (a.getTotalAmount() != null) {
                a.setTotalAmount(a.getTotalAmount().add(b.getTotalAmount()));
            } else {
                a.setTotalAmount(b.getTotalAmount());
            }
        }
        if (b.getPackNum() != null) {
            if (a.getPackNum() != null) {
                a.setPackNum(a.getPackNum() + b.getPackNum());
            } else {
                a.setPackNum(b.getPackNum());
            }
        }
        if (b.getVolume() != null) {
            if (a.getVolume() != null) {
                a.setVolume(a.getVolume().add(b.getVolume()));
            } else {
                a.setVolume(b.getVolume());
            }
        }
        if (b.getNetWt() != null) {
            if (a.getNetWt() != null) {
                a.setNetWt(a.getNetWt().add(b.getNetWt()));
            } else {
                a.setNetWt(b.getNetWt());
            }
        }
        if (b.getGrossWt() != null) {
            if (a.getGrossWt() != null) {
                a.setGrossWt(a.getGrossWt().add(b.getGrossWt()));
            } else {
                a.setGrossWt(b.getGrossWt());
            }
        }
        return a;
    }

    /**
     * OCR进口表头信息校验
     * @param head
     * @return
     * @throws OcrDataValidationException
     */
    public String checkOcrHead(GwOcrInvoiceEHead head) throws OcrDataValidationException {
        GwOcrInvoiceEHeadParam tobeCheck = dtoMapper.poToEntity(head);
        String err = validatorUtil.validation(tobeCheck);
        if (err != null) {
            throw new OcrDataValidationException(err);
        }
        return null;
    }

    /**
     * OCR进口表体信息校验
     * @param list
     * @return
     * @throws OcrDataValidationException
     */
    public String checkOcrList(GwOcrInvoiceEList list) throws OcrDataValidationException {
        GwOcrInvoiceEListParam tobeCheck = listDtoMapper.poToEntity(list);
        String err = validatorUtil.validation(tobeCheck);
        if (err != null) {
            throw new OcrDataValidationException(err);
        }
        return null;
    }
}
