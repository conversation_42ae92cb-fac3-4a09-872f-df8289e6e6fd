package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListParam;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListSumInfo;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceIHeadMapper;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceIListMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceIListDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHead;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIList;
import com.dcjet.cs.util.pageSort.DcPageSort;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Service
public class GwOcrInvoiceIListService extends BaseService<GwOcrInvoiceIList> {
    @Resource
    private GwOcrInvoiceIListMapper mapper;
    @Resource
    private GwOcrInvoiceIListDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceIList> getMapper() {
        return mapper;
    }

    @Resource
    private GwOcrInvoiceIEService gwOcrInvoiceIEService;
    @Resource
    private GwOcrInvoiceIHeadMapper headMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceIListParam
     * @param pageParam
     * @return
     */
    @SneakyThrows
    @DcPageSort(model = GwOcrInvoiceIList.class)
    public ResultObject<List<GwOcrInvoiceIListDto>> getListPaged(GwOcrInvoiceIListParam gwOcrInvoiceIListParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceIList gwOcrInvoiceIList = dtoMapper.toPo(gwOcrInvoiceIListParam);
//        GenericsUtils.setSortParam(pageParam.getSort(), this);
        Page<GwOcrInvoiceIList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceIList));
        List<GwOcrInvoiceIListDto> gwOcrInvoiceIListDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceIListDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrInvoiceIListDto>> paged = ResultObject.createInstance(gwOcrInvoiceIListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceIListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIListDto insert(GwOcrInvoiceIListParam gwOcrInvoiceIListParam, UserInfoToken userInfo) {
        GwOcrInvoiceIList gwOcrInvoiceIList = dtoMapper.toPo(gwOcrInvoiceIListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceIList.setSid(sid);
        gwOcrInvoiceIList.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceIList.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceIList);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceIList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceIListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIListDto update(GwOcrInvoiceIListParam gwOcrInvoiceIListParam, UserInfoToken userInfo) {
        GwOcrInvoiceIList list = mapper.selectByPrimaryKey(gwOcrInvoiceIListParam.getSid());
        if (list == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据已删除，请重新查询"));
        }
        GwOcrInvoiceIHead head = headMapper.selectByPrimaryKey(list.getHeadId());
        if (head != null && "2".equals(head.getCreateStatus())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已生成状态的数据不可编辑"));
        }

        dtoMapper.updatePo(gwOcrInvoiceIListParam, list);
        list.setUpdateUser(userInfo.getUserNo());
        list.setUpdateUserName(userInfo.getUserName());
        list.setUpdateTime(new Date());
        //企业参数库转换
        gwOcrInvoiceIEService.setBiCustomerParams(userInfo);
        gwOcrInvoiceIEService.convertBasicParamsI(list);
        // 更新数据
        int update = mapper.updateByPrimaryKey(list);
        // 更新表头状态，更新时间
        GwOcrInvoiceIHead uptModel = new GwOcrInvoiceIHead(){{
            setSid(list.getHeadId());
            setModifyStatus("2");
            setUpdateTime(new Date());
            setUpdateUser(userInfo.getUserNo());
            setUpdateUserName(userInfo.getUserNo());
        }};
        headMapper.updateByPrimaryKeySelective(uptModel);
        return update > 0 ? dtoMapper.toDto(list) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceIListDto> selectAll(GwOcrInvoiceIListParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceIList gwOcrInvoiceIList = dtoMapper.toPo(exportParam);
        gwOcrInvoiceIList.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceIListDto> gwOcrInvoiceIListDtos = new ArrayList<>();
        List<GwOcrInvoiceIList> gwOcrInvoiceILists = mapper.getList(gwOcrInvoiceIList);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceILists)) {
            gwOcrInvoiceIListDtos = gwOcrInvoiceILists.stream().map(head -> {
                GwOcrInvoiceIListDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceIListDtos;
    }

    /**
     * 统计数量，金额，净重，毛重，件数
     * @param headId
     * @param token
     * @return
     */
    public GwOcrInvoiceIListSumInfo sumByHeadId(String headId, UserInfoToken token) {
        return mapper.sumByHeadId(headId);
    }
}
