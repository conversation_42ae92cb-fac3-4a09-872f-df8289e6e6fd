package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListParam;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListSumInfo;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceEHeadMapper;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceEListMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceEListDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEHead;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEList;
import com.dcjet.cs.util.pageSort.DcPageSort;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2023-02-13
 */
@Service
public class GwOcrInvoiceEListService extends BaseService<GwOcrInvoiceEList> {
    @Resource
    private GwOcrInvoiceEListMapper mapper;
    @Resource
    private GwOcrInvoiceEListDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceEList> getMapper() {
        return mapper;
    }

    @Resource
    private GwOcrInvoiceIEService gwOcrInvoiceIEService;
    @Resource
    private GwOcrInvoiceEHeadMapper headMapper;

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceEListParam
     * @param pageParam
     * @return
     */
    @SneakyThrows
    @DcPageSort(model = GwOcrInvoiceEList.class)
    public ResultObject<List<GwOcrInvoiceEListDto>> getListPaged(GwOcrInvoiceEListParam gwOcrInvoiceEListParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceEList gwOcrInvoiceEList = dtoMapper.toPo(gwOcrInvoiceEListParam);
//        GenericsUtils.setSortParam(pageParam.getSort(), this);
        Page<GwOcrInvoiceEList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceEList));
        List<GwOcrInvoiceEListDto> gwOcrInvoiceIListDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceEListDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrInvoiceEListDto>> paged = ResultObject.createInstance(gwOcrInvoiceIListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceEListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEListDto insert(GwOcrInvoiceEListParam gwOcrInvoiceEListParam, UserInfoToken userInfo) {
        GwOcrInvoiceEList gwOcrInvoiceEList = dtoMapper.toPo(gwOcrInvoiceEListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceEList.setSid(sid);
        gwOcrInvoiceEList.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceEList.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceEList);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceEList) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceEListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEListDto update(GwOcrInvoiceEListParam gwOcrInvoiceEListParam, UserInfoToken userInfo) {
        GwOcrInvoiceEList list = mapper.selectByPrimaryKey(gwOcrInvoiceEListParam.getSid());
        if (list == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据已删除，请重新查询"));
        }
        GwOcrInvoiceEHead head = headMapper.selectByPrimaryKey(list.getHeadId());
        if (head != null && "2".equals(head.getCreateStatus())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已生成状态的数据不可编辑"));
        }

        dtoMapper.updatePo(gwOcrInvoiceEListParam, list);
        list.setUpdateUser(userInfo.getUserNo());
        list.setUpdateUserName(userInfo.getUserName());
        list.setUpdateTime(new Date());
        //企业参数库转换
        gwOcrInvoiceIEService.setBiCustomerParams(userInfo);
        gwOcrInvoiceIEService.convertBasicParamsE(list);
        // 更新数据
        int update = mapper.updateByPrimaryKey(list);
        // 更新表头状态，更新时间
        GwOcrInvoiceEHead uptModel = new GwOcrInvoiceEHead(){{
            setSid(list.getHeadId());
            setModifyStatus("2");
            setUpdateTime(new Date());
            setUpdateUser(userInfo.getUserNo());
            setUpdateUserName(userInfo.getUserNo());
        }};
        headMapper.updateByPrimaryKeySelective(uptModel);
        return update > 0 ? dtoMapper.toDto(list) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceEListDto> selectAll(GwOcrInvoiceEListParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceEList gwOcrInvoiceEList = dtoMapper.toPo(exportParam);
        gwOcrInvoiceEList.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceEListDto> gwOcrInvoiceEListDtos = new ArrayList<>();
        List<GwOcrInvoiceEList> gwOcrInvoiceELists = mapper.getList(gwOcrInvoiceEList);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceELists)) {
            gwOcrInvoiceEListDtos = gwOcrInvoiceELists.stream().map(head -> {
                GwOcrInvoiceEListDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceEListDtos;
    }

    /**
     * 统计数量，金额，净重，毛重，件数
     * @param headId
     * @param token
     * @return
     */
    public GwOcrInvoiceEListSumInfo sumByHeadId(String headId, UserInfoToken token) {
        return mapper.sumByHeadId(headId);
    }
}
