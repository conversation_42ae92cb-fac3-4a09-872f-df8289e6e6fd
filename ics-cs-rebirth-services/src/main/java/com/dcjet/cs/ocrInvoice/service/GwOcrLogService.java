package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.GwOcrLogDto;
import com.dcjet.cs.dto.ocrInvoice.GwOcrLogParam;
import com.dcjet.cs.ocrInvoice.dao.GwOcrLogMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrLogDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrLog;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Service
public class GwOcrLogService extends BaseService<GwOcrLog> {
    @Resource
    private GwOcrLogMapper gwOcrLogMapper;
    @Resource
    private GwOcrLogDtoMapper gwOcrLogDtoMapper;
    @Override
    public Mapper<GwOcrLog> getMapper() {
        return gwOcrLogMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrLogParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwOcrLogDto>> getListPaged(GwOcrLogParam gwOcrLogParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrLog gwOcrLog = gwOcrLogDtoMapper.toPo(gwOcrLogParam);
        Page<GwOcrLog> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwOcrLogMapper.getList(gwOcrLog));
        List<GwOcrLogDto> gwOcrLogDtos = page.getResult().stream().map(head -> {
            GwOcrLogDto dto = gwOcrLogDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrLogDto>> paged = ResultObject.createInstance(gwOcrLogDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrLogParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrLogDto insert(GwOcrLogParam gwOcrLogParam, UserInfoToken userInfo) {
        GwOcrLog gwOcrLog = gwOcrLogDtoMapper.toPo(gwOcrLogParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrLog.setSid(sid);
        gwOcrLog.setInsertUser(userInfo.getUserNo());
        gwOcrLog.setInsertTime(new Date());
        // 新增数据
        int insertStatus = gwOcrLogMapper.insert(gwOcrLog);
        return  insertStatus > 0 ? gwOcrLogDtoMapper.toDto(gwOcrLog) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwOcrLogParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrLogDto update(GwOcrLogParam gwOcrLogParam, UserInfoToken userInfo) {
        GwOcrLog gwOcrLog = gwOcrLogMapper.selectByPrimaryKey(gwOcrLogParam.getSid());
        gwOcrLogDtoMapper.updatePo(gwOcrLogParam, gwOcrLog);
        gwOcrLog.setUpdateUser(userInfo.getUserNo());
        gwOcrLog.setUpdateTime(new Date());
        // 更新数据
        int update = gwOcrLogMapper.updateByPrimaryKey(gwOcrLog);
        return update > 0 ? gwOcrLogDtoMapper.toDto(gwOcrLog) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		gwOcrLogMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrLogDto> selectAll(GwOcrLogParam exportParam, UserInfoToken userInfo) {
        GwOcrLog gwOcrLog = gwOcrLogDtoMapper.toPo(exportParam);
        // gwOcrLog.setTradeCode(userInfo.getCompany());
        List<GwOcrLogDto> gwOcrLogDtos = new ArrayList<>();
        List<GwOcrLog> gwOcrLogs = gwOcrLogMapper.getList(gwOcrLog);
        if (CollectionUtils.isNotEmpty(gwOcrLogs)) {
            gwOcrLogDtos = gwOcrLogs.stream().map(head -> {
                GwOcrLogDto dto = gwOcrLogDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrLogDtos;
    }

    /**
     * 获取日志
     * @param taskId
     * @param token
     * @return
     */
    public GwOcrLogDto getOne(String taskId, UserInfoToken token) {
        Example example = new Example(GwOcrLog.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andEqualTo("taskId", taskId);

        List<GwOcrLog> list = gwOcrLogMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        //判断taskId下的所有附件识别完成才返回
        List<GwOcrLog> unFinishedList = list.stream().filter(s -> "0".equals(s.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(unFinishedList)) {
            return gwOcrLogDtoMapper.toDto(unFinishedList.get(0));
        }
        return gwOcrLogDtoMapper.toDto(list.get(0));
    }

    /**
     * 获取任务列表
     * @return
     */
    public List<GwOcrLog> getTaskList() {
        Example example = new Example(GwOcrLog.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("status", "0");

        List<GwOcrLog> list = gwOcrLogMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list;
    }
}
