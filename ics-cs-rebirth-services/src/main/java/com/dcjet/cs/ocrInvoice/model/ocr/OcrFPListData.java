package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("")
public class OcrFPListData implements Serializable {

    @ApiModelProperty("序号")
    @JsonProperty("FPCommodity_Item")
    private OcrFieldInfo FPCommodity_Item;

    @ApiModelProperty("订单号码")
    @JsonProperty("FPCommodity_PO")
    private OcrFieldInfo FPCommodity_PO;

    @ApiModelProperty("订单项号")
    @JsonProperty("FPCommodity_PO_Item")
    private OcrFieldInfo FPCommodity_PO_Item;

    @ApiModelProperty("料号")
    @JsonProperty("FPCommodity_Gds_PartNo")
    private OcrFieldInfo FPCommodity_Gds_PartNo;

    @ApiModelProperty("商品描述")
    @JsonProperty("FPCommodity_Gds_Desc")
    private OcrFieldInfo FPCommodity_Gds_Desc;

    @ApiModelProperty("数量")
    @JsonProperty("FPCommodity_Gds_Qty")
    private OcrFieldInfo FPCommodity_Gds_Qty;

    @ApiModelProperty("数量单位")
    @JsonProperty("FPCommodity_Gds_Unit")
    private OcrFieldInfo FPCommodity_Gds_Unit;

    @ApiModelProperty("单价")
    @JsonProperty("FPCommodity_Gds_Price")
    private OcrFieldInfo FPCommodity_Gds_Price;

    @ApiModelProperty("百个单价")
    @JsonProperty("FPCommodity_Gds_PriceHundred")
    private OcrFieldInfo FPCommodity_Gds_PriceHundred;

    @ApiModelProperty("千个单价")
    @JsonProperty("FPCommodity_Gds_PriceThousand")
    private OcrFieldInfo FPCommodity_Gds_PriceThousand;

    @ApiModelProperty("币别")
    @JsonProperty("FPCommodity_Gds_Curr")
    private OcrFieldInfo FPCommodity_Gds_Curr;

    @ApiModelProperty("金额")
    @JsonProperty("FPCommodity_Gds_AMT")
    private OcrFieldInfo FPCommodity_Gds_AMT;

    @ApiModelProperty("净重")
    @JsonProperty("FPCommodity_Gds_NW")
    private OcrFieldInfo FPCommodity_Gds_NW;

    @ApiModelProperty("净重单位")
    @JsonProperty("FPCommodity_Gds_NWUnit")
    private OcrFieldInfo FPCommodity_Gds_NWUnit;

    @ApiModelProperty("毛重")
    @JsonProperty("FPCommodity_Gds_GW")
    private OcrFieldInfo FPCommodity_Gds_GW;

    @ApiModelProperty("毛重单位")
    @JsonProperty("FPCommodity_Gds_GWUnit")
    private OcrFieldInfo FPCommodity_Gds_GWUnit;

    @ApiModelProperty("原产国")
    @JsonProperty("FPCommodity_Gds_COO")
    private OcrFieldInfo FPCommodity_Gds_COO;

    @ApiModelProperty("件数")
    @JsonProperty("FPCommodity_Gds_PkgQty")
    private OcrFieldInfo FPCommodity_Gds_PkgQty;

    @ApiModelProperty("包装方式")
    @JsonProperty("FPCommodity_Gds_PkgUom")
    private OcrFieldInfo FPCommodity_Gds_PkgUom;

    @ApiModelProperty("包装编号")
    @JsonProperty("FPCommodity_Gds_PkgNo")
    private OcrFieldInfo FPCommodity_Gds_PkgNo;

    @ApiModelProperty("送货单号")
    @JsonProperty("FPCommodity_Delivery_no")
    private OcrFieldInfo FPCommodity_Delivery_no;

    @ApiModelProperty("法一数量")
    @JsonProperty("FPCommodity_Gds_QTY1")
    private OcrFieldInfo FPCommodity_Gds_QTY1;

    @ApiModelProperty("最终目的国")
    @JsonProperty("FPCommodity_Gds_DEST_CNT")
    private OcrFieldInfo FPCommodity_Gds_DEST_CNT;
}
