package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadLocParam;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceIHeadLocMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceIHeadLocDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHeadLoc;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Service
public class GwOcrInvoiceIHeadLocService extends BaseService<GwOcrInvoiceIHeadLoc> {
    @Resource
    private GwOcrInvoiceIHeadLocMapper mapper;
    @Resource
    private GwOcrInvoiceIHeadLocDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceIHeadLoc> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceIHeadLocParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwOcrInvoiceIHeadLocDto>> getListPaged(GwOcrInvoiceIHeadLocParam gwOcrInvoiceIHeadLocParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceIHeadLoc gwOcrInvoiceIHeadLoc = dtoMapper.toPo(gwOcrInvoiceIHeadLocParam);
        Page<GwOcrInvoiceIHeadLoc> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceIHeadLoc));
        List<GwOcrInvoiceIHeadLocDto> gwOcrInvoiceIHeadLocDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceIHeadLocDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrInvoiceIHeadLocDto>> paged = ResultObject.createInstance(gwOcrInvoiceIHeadLocDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceIHeadLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIHeadLocDto insert(GwOcrInvoiceIHeadLocParam gwOcrInvoiceIHeadLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceIHeadLoc gwOcrInvoiceIHeadLoc = dtoMapper.toPo(gwOcrInvoiceIHeadLocParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceIHeadLoc.setSid(sid);
        gwOcrInvoiceIHeadLoc.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceIHeadLoc.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceIHeadLoc);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceIHeadLoc) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceIHeadLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceIHeadLocDto update(GwOcrInvoiceIHeadLocParam gwOcrInvoiceIHeadLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceIHeadLoc gwOcrInvoiceIHeadLoc = mapper.selectByPrimaryKey(gwOcrInvoiceIHeadLocParam.getSid());
        dtoMapper.updatePo(gwOcrInvoiceIHeadLocParam, gwOcrInvoiceIHeadLoc);
        gwOcrInvoiceIHeadLoc.setUpdateUser(userInfo.getUserNo());
        gwOcrInvoiceIHeadLoc.setUpdateTime(new Date());
        // 更新数据
        int update = mapper.updateByPrimaryKey(gwOcrInvoiceIHeadLoc);
        return update > 0 ? dtoMapper.toDto(gwOcrInvoiceIHeadLoc) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceIHeadLocDto> selectAll(GwOcrInvoiceIHeadLocParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceIHeadLoc gwOcrInvoiceIHeadLoc = dtoMapper.toPo(exportParam);
        // gwOcrInvoiceIHeadLoc.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceIHeadLocDto> gwOcrInvoiceIHeadLocDtos = new ArrayList<>();
        List<GwOcrInvoiceIHeadLoc> gwOcrInvoiceIHeadLocs = mapper.getList(gwOcrInvoiceIHeadLoc);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceIHeadLocs)) {
            gwOcrInvoiceIHeadLocDtos = gwOcrInvoiceIHeadLocs.stream().map(head -> {
                GwOcrInvoiceIHeadLocDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceIHeadLocDtos;
    }

    /**
     *
     * @param sid
     * @return
     */
    public GwOcrInvoiceIHeadLocDto getOne(String sid) {
        GwOcrInvoiceIHeadLoc loc = mapper.selectByPrimaryKey(sid);
        return dtoMapper.toDto(loc);
    }
}
