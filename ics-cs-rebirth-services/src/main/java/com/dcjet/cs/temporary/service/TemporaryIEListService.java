
package com.dcjet.cs.temporary.service;

import com.dcjet.cs.dto.temporary.TemporaryIEListDto;
import com.dcjet.cs.dto.temporary.TemporaryIEListExtractionParam;
import com.dcjet.cs.dto.temporary.TemporaryIEListParam;
import com.dcjet.cs.dto.temporary.TemporaryIEListSearchParam;
import com.dcjet.cs.temporary.dao.TemporaryIEHeadMapper;
import com.dcjet.cs.temporary.dao.TemporaryIEListMapper;
import com.dcjet.cs.temporary.dao.TemporaryIListMapper;
import com.dcjet.cs.temporary.manager.TemporaryIEListManager;
import com.dcjet.cs.temporary.manager.TemporaryIHeadManager;
import com.dcjet.cs.temporary.mapper.TemporaryIEListDtoMapper;
import com.dcjet.cs.temporary.mapper.TemporaryIListDtoMapper;
import com.dcjet.cs.temporary.model.TemporaryIEHead;
import com.dcjet.cs.temporary.model.TemporaryIEList;
import com.dcjet.cs.temporary.model.TemporaryIList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/***
 * 修理复运进境表体 service default implement
 *
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Service
public class TemporaryIEListService {

	@Resource
    private TemporaryIEListMapper temporaryIEListMapper;

	@Resource
    private TemporaryIEListDtoMapper temporaryIEListDtoMapper;

	@Resource
	private TemporaryIEHeadMapper temporaryIEHeadMapper;

	@Resource
	private TemporaryIEListManager temporaryIEListManager;

	@Resource
	private TemporaryIListMapper temporaryIListMapper;

	@Resource
	private TemporaryIListDtoMapper temporaryIListDtoMapper;

	@Resource
	private TemporaryIHeadManager temporaryIHeadManager;
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("暂时进出境-复运出境管理表体");
	 /**
     * 获取分页信息
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject<List<TemporaryIEListDto>> list(TemporaryIEListSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        TemporaryIEList po = temporaryIEListDtoMapper.fromSearchParam(searchParam);
        po.setTradeCode(token.getCompany());
        Page<TemporaryIEList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> temporaryIEListMapper.getList(po));
        List<TemporaryIEListDto> dtoList = page.getResult().stream().map(head -> {
            TemporaryIEListDto dto = temporaryIEListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<TemporaryIEListDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

	/***
     * 根据id获取详情
     *
     * @param sid
     * @param token
     * @return
     */
	public TemporaryIEListDto get(@Nonnull String sid, UserInfoToken token) {
        TemporaryIEList po = temporaryIEListMapper.selectByPrimaryKey(sid);
        return temporaryIEListDtoMapper.toDto(po);
    }

	 /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TemporaryIEListDto insert(TemporaryIEListParam param, UserInfoToken token) {
        TemporaryIEList po = temporaryIEListDtoMapper.toPo(param);
		po.preInsert(token);
        int insertState = temporaryIEListMapper.insert(po);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,token,po);
        return  insertState > 0 ? temporaryIEListDtoMapper.toDto(po) : null;
    }


	/**
     * 修改
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TemporaryIEListDto update(@Nonnull String sid, TemporaryIEListParam param, UserInfoToken token) {
        TemporaryIEList po = temporaryIEListMapper.selectByPrimaryKey(sid);
        if (po == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择有效的数据"));
        }

        temporaryIEListDtoMapper.updatePo(param, po);
        po.preUpdate(token);
        int updateState = temporaryIEListMapper.updateByPrimaryKey(po);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,token,po);
        return updateState > 0 ? temporaryIEListDtoMapper.toDto(po) : null;
	}

	/**
     * 批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids, UserInfoToken userInfo) {
        Example exampleList = new Example(TemporaryIEList.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<TemporaryIEList> temporaryIListList = temporaryIEListMapper.selectByExample(exampleList);
        temporaryIEListManager.deleteByIdList(sids,  userInfo);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,temporaryIListList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void extraction(TemporaryIEListExtractionParam param, UserInfoToken token) {
        TemporaryIEHead head = temporaryIEHeadMapper.selectByPrimaryKey(param.getHeadId());
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据非法"));
        }

        if (!head.canExtraction()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前单据已生成报关，禁止此操作"));
        }

        Map<String, BigDecimal> map = null;

        TemporaryIList queryParam = temporaryIListDtoMapper.fromEIListExtractionParam(param);
        queryParam.setTradeCode(token.getCompany());

        if (param.getData() != null && !param.getData().isEmpty()) {
            map = param.getData().stream().collect(Collectors.toMap(it -> it.getSid(), it -> it.getExtractQty(), (a, b) -> a));
            List<String> idList = new ArrayList<>(map.keySet());
            queryParam.setIdList(idList);
        }

        List<TemporaryIList> list = temporaryIListMapper.take(queryParam);
        if (list.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到可以被提取的数据"));
        }
        final Map<String, BigDecimal> finalMap = map;
        List<TemporaryIEList> eiListList = list.stream().map(it -> {
            TemporaryIEList ieList = temporaryIEListDtoMapper.fromEList(it);
            if (finalMap != null && finalMap.containsKey(it.getSid())) {
                BigDecimal qty = finalMap.get(it.getSid());
                if (qty == null) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数量不能为空"));
                }
                if (qty.compareTo(it.getRemainQty()) == 1) {
                    throw new ArgumentException(String.format(xdoi18n.XdoI18nUtil.t("企业料号")+":[%s],%s",it.getFacGNo(),xdoi18n.XdoI18nUtil.t("本次复运数量不能大于剩余数量")));
                }
                ieList.setQty(qty);
                ieList.calculate();
            } else {
                ieList.setQty(it.getRemainQty());
            }
            ieList.setHeadId(head.getSid());
            ieList.setTemporaryListId(it.getSid());
            ieList.preInsert(token);

            temporaryIHeadManager.updateEHeadStatusProgress(null, it.getHeadId(), token);
            return ieList;
        }).collect(Collectors.toList());
        temporaryIEListMapper.insertList(eiListList);

    }


}
