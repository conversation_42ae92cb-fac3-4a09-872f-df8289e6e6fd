package com.dcjet.cs.docCenter.service;

import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.docCenter.dao.DocCenterMapper;
import com.dcjet.cs.dto.docCenter.ErpDecDocDto;
import com.dcjet.cs.dto.docCenter.ErpDecDocParam;
import com.dcjet.cs.dto.docCenter.ErpDecDocSyncEntryParam;
import com.dcjet.cs.dto.docCenter.K2EntryNoPrintDto;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.util.*;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs.war.dao.WarringPassConfigMapper;
import com.dcjet.cs.war.model.WarringPassConfig;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DocCenterService {
    @Resource
    private DocCenterMapper mapper;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private WarringPassConfigMapper warringPassConfigMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Value("${dc.export.temp:}")
    private String tempPath;

    @Resource
    ExportDocCenterService exportDocCenterService;

    /**
     * 获取文档统计列表
     * @param param
     * @param pageParam
     * @param userInfo
     * @return
     */
    public ResultObject getDocListPaged(ErpDecDocParam param, PageParam pageParam, UserInfoToken userInfo) {
//        Example example = new Example(Attached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("sid", Arrays.asList("cc2d2379-5fd1-4bbb-ba50-e8a802ccfe5a","cf70250d-058f-4d70-889a-95e98cf138c9"));
//        List<Attached> list = attachedMapper.selectByExample(example);
//
//        exportDocCenterService.ExportFileBytes(list);

        param.setTradeCode(userInfo.getCompany());
        String entryNo = param.getEntryNo();
        if (StringUtils.isNotBlank(entryNo)) {
            entryNo = entryNo.replaceAll(",", "#").replaceAll(" ", "#");
            List<String> entryNoList = Arrays.asList(entryNo.split("#"));
            if (entryNoList.size() > 1) {
                param.setEntryNoList(entryNoList);
                param.setEntryNo(null);
            }
        }

        Page<ErpDecDocDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getDocList(param));
        List<ErpDecDocDto> results = page.getResult();
        results = convertForDocPrint2(results);
        return ResultObject.createInstance(results, (int) page.getTotal(), page.getPageNum());
    }

    public List<ErpDecDocDto> convertForDocPrint2(List<ErpDecDocDto> list) {
        for(ErpDecDocDto item : list) {
            if (!ConstantsStatus.STATUS_0.equals(item.getEmsListNoCnt())) {
                item.setEmsListNoCnt(CommonVariable.YES_EN);
            } else {
                item.setEmsListNoCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getEntryNoCnt())) {
                item.setEntryNoCnt(CommonVariable.YES_EN);
            } else {
                item.setEntryNoCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getInvoiceNoCnt())) {
                item.setInvoiceNoCnt(CommonVariable.YES_EN);
            } else {
                item.setInvoiceNoCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getPactCodeCnt())) {
                item.setPactCodeCnt(CommonVariable.YES_EN);
            } else {
                item.setPactCodeCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getInvoiceBoxCnt())) {
                item.setInvoiceBoxCnt(CommonVariable.YES_EN);
            } else {
                item.setInvoiceBoxCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getInvBoxBillCnt())) {
                item.setInvBoxBillCnt(CommonVariable.YES_EN);
            } else {
                item.setInvBoxBillCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getTaxCnt())) {
                item.setTaxCnt(CommonVariable.YES_EN);
            } else {
                item.setTaxCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getStatementCnt())) {
                item.setStatementCnt(CommonVariable.YES_EN);
            } else {
                item.setStatementCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getSituationCnt())) {
                item.setSituationCnt(CommonVariable.YES_EN);
            } else {
                item.setSituationCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getOtherCnt())) {
                item.setOtherCnt(CommonVariable.YES_EN);
            } else {
                item.setOtherCnt("");
            }

            if (!ConstantsStatus.STATUS_0.equals(item.getReviewCnt())) {
                item.setReviewCnt(CommonVariable.YES_EN);
            } else {
                item.setReviewCnt("");
            }
            if (!ConstantsStatus.STATUS_0.equals(item.getOrderCnt())) {
                item.setOrderCnt(CommonVariable.YES_EN);
            } else {
                item.setOrderCnt("");
            }
            if (!ConstantsStatus.STATUS_0.equals(item.getOriginCnt())) {
                item.setOriginCnt(CommonVariable.YES_EN);
            } else {
                item.setOriginCnt("");
            }

            item.setClassificationCnt(!ConstantsStatus.STATUS_0.equals(item.getClassificationCnt()) ? CommonVariable.YES_EN : "");
            item.setArrivalCnt(!ConstantsStatus.STATUS_0.equals(item.getArrivalCnt()) ? CommonVariable.YES_EN : "");
            item.setPictureCnt(!ConstantsStatus.STATUS_0.equals(item.getPictureCnt()) ? CommonVariable.YES_EN : "");
            item.setDeclarationCnt(!ConstantsStatus.STATUS_0.equals(item.getDeclarationCnt()) ? CommonVariable.YES_EN : "");
            item.setInspectionCnt(!ConstantsStatus.STATUS_0.equals(item.getInspectionCnt()) ? CommonVariable.YES_EN : "");
            item.setDrawingCnt(!ConstantsStatus.STATUS_0.equals(item.getDrawingCnt()) ? CommonVariable.YES_EN : "");
            item.setFreightlistCnt(!ConstantsStatus.STATUS_0.equals(item.getFreightlistCnt()) ? CommonVariable.YES_EN : "");
            item.setQuotationCnt(!ConstantsStatus.STATUS_0.equals(item.getQuotationCnt()) ? CommonVariable.YES_EN : "");
            item.setVoucherCnt(!ConstantsStatus.STATUS_0.equals(item.getVoucherCnt()) ? CommonVariable.YES_EN : "");
            item.setAttorneyCnt(!ConstantsStatus.STATUS_0.equals(item.getAttorneyCnt()) ? CommonVariable.YES_EN : "");
            // 报关单同步状态;0：否，1：是。同步单一窗口报关单数据状态
            item.setSyncEntryStatus(ConstantsStatus.STATUS_1.equals(item.getSyncEntryStatus()) ? CommonVariable.YES_EN : "");
        }
        return list;
    }

    public List<ErpDecDocDto> getDocListAll(ErpDecDocParam param, UserInfoToken userInfo) {
        param.setTradeCode(userInfo.getCompany());
        String entryNo = param.getEntryNo();
        if (StringUtils.isNotBlank(entryNo)) {
            entryNo = entryNo.replaceAll(",", "#").replaceAll(" ", "#");
            List<String> entryNoList = Arrays.asList(entryNo.split("#"));
            if (entryNoList.size() > 1) {
                param.setEntryNoList(entryNoList);
                param.setEntryNo(null);
            }
        }
        return mapper.getDocList(param);
    }

    public ResultObject<List<Attached>> getDocAttachedList(String emsListNo, String ieMark, String acmpType, UserInfoToken userInfo) {
        String headId = null;
        if(CommonVariable.IE_MARK_I.equals(ieMark)) {
            Example example = new Example(DecErpIHeadN.class);
            Example.Criteria criteria = example.createCriteria();

            criteria.andEqualTo("emsListNo", emsListNo);
            criteria.andEqualTo("tradeCode", userInfo.getCompany());

            List<DecErpIHeadN> list = decErpIHeadNMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(list)) {
                headId = list.get(0).getSid();
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提单不存在"));
            }
        } else if(CommonVariable.IE_MARK_E.equals(ieMark)) {
            Example example = new Example(DecErpEHeadN.class);
            Example.Criteria criteria = example.createCriteria();

            criteria.andEqualTo("emsListNo", emsListNo);
            criteria.andEqualTo("tradeCode", userInfo.getCompany());

            List<DecErpEHeadN> list = decErpEHeadNMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(list)) {
                headId = list.get(0).getSid();
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提单不存在"));
            }
        }
        List<Attached> result = mapper.getDocAttached(userInfo.getCompany(), headId, acmpType);
        return ResultObject.createInstance(true, "", result);
    }

    /**
     * 进出口文档批量下载校验接口
     * @param erpDecDocParam
     * @param ieMark
     * @return
     */
    public List<String> checkDownLoad(ErpDecDocParam erpDecDocParam, String ieMark, String tradeCode) {
        if (CollectionUtils.isEmpty(erpDecDocParam.getEmsListNos())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请勾选需要下载的数据"));
        }
        if (CollectionUtils.isEmpty(erpDecDocParam.getAcmpTypeList())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请至少勾选一种随附单据类型进行下载"));
        }
        List<String> acmpTypeList = erpDecDocParam.getAcmpTypeList();
        if (acmpTypeList.contains("invoiceNo")) {
            acmpTypeList.add("boxNo");
        }
        Set<String> headIds = null;
        if(CommonVariable.IE_MARK_I.equals(ieMark)) {
            Example example = new Example(DecErpIHeadN.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("emsListNo", erpDecDocParam.getEmsListNos());
            criteria.andEqualTo("tradeCode", tradeCode);

            List<DecErpIHeadN> list = decErpIHeadNMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(list)) {
                headIds = list.stream().map(DecErpIHeadN::getSid).collect(Collectors.toSet());
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提单不存在"));
            }
        } else if(CommonVariable.IE_MARK_E.equals(ieMark)) {
            Example example = new Example(DecErpEHeadN.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("emsListNo", erpDecDocParam.getEmsListNos());
            criteria.andEqualTo("tradeCode", tradeCode);

            List<DecErpEHeadN> list = decErpEHeadNMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(list)) {
                headIds = list.stream().map(DecErpEHeadN::getSid).collect(Collectors.toSet());
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提单不存在"));
            }
        }

        List<Attached> result = mapper.getDocAttacheds(tradeCode, new ArrayList<>(headIds), acmpTypeList);
        if (CollectionUtils.isEmpty(result)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("所选预录入单无附件，请重新选择"));
        }
        BigDecimal sumFileSize = result.stream().map(Attached::getFileSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (sumFileSize.compareTo(BigDecimal.valueOf(204800)) == 1) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("单次附件下载量不允许超过200M，请重新选择后再下载"));
        }
        // ACMP_TYPE || '-' || ORIGIN_FILE_NAME as ORIGIN_FILE_NAME
//        if (result.size() != result.stream().map(Attached::getOriginFileName).collect(Collectors.toSet()).size()) {
//            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("所需下载的附件中存在相同文件名称，无法下载"));
//        }
        return result.stream().map(Attached::getSid).collect(Collectors.toList());
    }

    /**
     * 可下载文档类型查询
     * @param userInfo
     * @return
     */
    public ResultObject getAcmpTypeList(UserInfoToken userInfo) {
        WarringPassConfig config = new WarringPassConfig(){{
            setTradeCode(userInfo.getCompany());
        }};
        List<WarringPassConfig> list = warringPassConfigMapper.select(config);
        // 附件类型 k:code v:name
        Map<String, String> attachTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list) && StringUtils.isNotBlank(list.get(0).getAttachType())) {
            // 配置的可下载类型
            Map<String, Object> map = JsonObjectMapper.getInstance().json2Map(list.get(0).getAttachType());
            String attachType = (String) map.get("selected");
            Arrays.asList(attachType.split(",")).stream().forEach(x -> {
                attachTypeMap.put(x, allTypeMap(x));
            });
        }

        return ResultObject.createInstance(true, "", attachTypeMap);
    }

    public String allTypeMap(String key) {
        return new HashMap<String, String>(24){{
            put("emsListNo", "提运单号");
            put("entryNo", "报关单");
            put("invoiceNo", "发票箱单");
            put("boxNo", "发票箱单");
            put("pactCode", "合同");
            put("invoiceBox", "发票&箱单");
            put("invBoxBill", "发票&箱单&提单");
            put("tax", "税单");
            put("statement", "对账单");
            put("situation", "情况说明");
            put("other", "其他");
            put("orderNo", "订单");
            put("origin", "原产地证");
            put("review", "复核单");
            put("Classification", "归类证书");
            put("arrival", "到货通知");
            put("picture", "图片");
            put("Declaration", "申报资料");
            put("inspection", "商检证书");
            put("drawing", "图纸");
            put("freightlist", "运费清单");
            put("quotation", "报价单");
            put("voucher", "出入库凭证");
            put("attorney", "出口委托书");
        }}.get(key);
    }

    /**
     * 校验是否可以同步报关单数据
     * @param params
     * @param ieMark
     * @return
     */
    public ResultObject checkSyncEntry(List<ErpDecDocSyncEntryParam> params,String ieMark, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("校验成功"));
        checkParams(params,ieMark);
        int count = selectSyncEntryStatusCount(params,ieMark,userInfo.getCompany());
        if(count > 0) {
            resultObject.setData("confirm");
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("存在已同步数据是否继续"));
        }
        return resultObject;
    }

    /**
     * 公共参数校验
     * @param params
     * @param ieMark
     */
    private void checkParams(List<ErpDecDocSyncEntryParam> params,String ieMark) {
        boolean bool1 = ieMark.equals(CommonEnum.I_E_MARK_ENUM.I_MARK.getCode());
        boolean bool2 = ieMark.equals(CommonEnum.I_E_MARK_ENUM.E_MARK.getCode());
        if(!bool1 && !bool2) {
            throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("进出口类型传参错误,只能是I或E"));
        }
        if(CollectionUtils.isEmpty(params)) {
            throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("待同步的报关单数据不能为空"));
        }else {
            if(params.size() > 10) {//判断单次提交不能茶瓯哦10票
                throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("待同步的报关单数据不能超过10票"));
            }
        }
        if(params.stream().filter(it -> StringUtils.isBlank(it.getEntryNo())).count() > 0) {
            throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("未产生报关单号无法同步"));
        }
        GwstdHttpConfig printConfig = commonService.getHttpConfigInfo(Constants.ENTRY_DATA_PRINT);
        if (null == printConfig || StringUtils.isBlank(printConfig.getBaseUrl()) || StringUtils.isBlank(printConfig.getServiceUrl())) {
            throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("未配置【报关小精灵报关单打印接口】接口信息"));
        }
        GwstdHttpConfig downloadConfig = commonService.getHttpConfigInfo(Constants.ENTRY_DATA_DOWNLOAD);
        if (null == downloadConfig || StringUtils.isBlank(downloadConfig.getBaseUrl()) || StringUtils.isBlank(downloadConfig.getServiceUrl())) {
            throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("未配置【报文中心报关单下载接口】接口信息"));
        }
    }

    /**
     * 同步报文中心报关单附件并匹配到预录入单随附单据【类型：报关单】
     * @return
     */
    @Transactional
    public ResultObject syncEntryAttached(List<ErpDecDocSyncEntryParam> params,String ieMark, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("同步成功"));
        checkParams(params, ieMark);
        String ieMarkName = CommonEnum.I_E_MARK_ENUM.I_MARK.getCode().equals(ieMark) ? "进口" : "出口";
        log.info(String.format("################# 开始同步报文中心(单一窗口)%s报关单附件 #################", ieMarkName));
        GwstdHttpConfig printConfig = commonService.getHttpConfigInfo(Constants.ENTRY_DATA_PRINT);
        GwstdHttpConfig downloadConfig = commonService.getHttpConfigInfo(Constants.ENTRY_DATA_DOWNLOAD);
        String entryPrintUrl = printConfig.getBaseUrl() + printConfig.getServiceUrl();
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        String accessToken = userInfo.getAccessToken();
//        String accessToken = "d833b1f6-15b1-4801-94ee-eb02ccbfa6ba";//线上环境token,如果过期了,请更换;用于测试
        String message = "";
        int totalCount = params.size(), successCount = 0, errorCount = 0;
        //循环下载报关单附件数据
        for (ErpDecDocSyncEntryParam s : params) {
            try {
                log.info(String.format("################# 开始下载报关单:【%s】附件 #################", s.getEntryNo()));
                String printUrl;
                if (entryPrintUrl.endsWith("/")) {
                    printUrl = entryPrintUrl + s.getEntryNo();
                } else {
                    printUrl = entryPrintUrl + "/" + s.getEntryNo();
                }
                String fileName = "GW-" + s.getEntryNo() + ".pdf";
                String emsListNo;
                if (CommonEnum.I_E_MARK_ENUM.I_MARK.getCode().equals(ieMark)) {
                    DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(s.getSid());
                    if (decErpIHeadN == null) {
                        resultObject.setCode(Constants.HTTP_UNAUTHORIZED);
                        resultObject.setSuccess(false);
                        resultObject.setMessage(xdoi18n.XdoI18nUtil.t("token已过期"));
                        resultObject.setData(String.format("[%s]%s[%s]", s.getSid(), xdoi18n.XdoI18nUtil.t("根据sid查询不到提单信息"), ieMarkName));
                        return resultObject;
                    }
                    emsListNo = decErpIHeadN.getEmsListNo();
                } else {
                    DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(s.getSid());
                    if (decErpEHeadN == null) {
                        resultObject.setCode(Constants.HTTP_UNAUTHORIZED);
                        resultObject.setSuccess(false);
                        resultObject.setMessage(xdoi18n.XdoI18nUtil.t("token已过期"));
                        resultObject.setData(String.format("[%s]%s[%s]", s.getSid(), xdoi18n.XdoI18nUtil.t("根据sid查询不到提单信息"), ieMarkName));
                        return resultObject;
                    }
                    emsListNo = decErpEHeadN.getEmsListNo();
                }
                //1.调用【报关小精灵报关单打印接口】获取文件路径
                log.info(String.format("################# 开始调用【报关小精灵报关单打印接口】,url为：%s #################", printUrl));
                Map<String, String> map = new HashMap<>(1);
                map.put("Authorization", "Bearer " + accessToken);
                String response = OkHttpUtils.httpGet(printUrl, map);
                //String response = RequestUtil.sendGet(printUrl, accessToken);//该方式不能获取token过期问题,舍弃
                ResultObject<K2EntryNoPrintDto> result = jsonObjectMapper.fromJson(response, new TypeReference<ResultObject<K2EntryNoPrintDto>>() {
                });
                if (result == null) {
                    resultObject.setCode(400);
                    resultObject.setSuccess(false);
                    resultObject.setMessage(xdoi18n.XdoI18nUtil.t("同步报关单附件接口调用失败,返回结果为空"));
                    resultObject.setData(response);
                    log.info("################# 同步报关单附件接口调用失败,接口返回结果为空 #################");
                    return resultObject;
                }
                if (Constants.HTTP_UNAUTHORIZED == result.getCode()) {
                    resultObject.setCode(Constants.HTTP_UNAUTHORIZED);
                    resultObject.setSuccess(false);
                    resultObject.setMessage(xdoi18n.XdoI18nUtil.t("同步报关单附件接口调用失败,token过期"));
                    resultObject.setData(response);
                    log.info(String.format("################# 同步报关单附件接口调用失败,token过期:%s #################", response));
                    return resultObject;
                }
                //2.删除原提单上传的随附单据
                Example example = new Example(Attached.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("businessSid", s.getSid());
                criteria.andEqualTo("originFileName", fileName);
                criteria.andEqualTo("dataSource", ConstantsStatus.STATUS_1);
                List<Attached> list = attachedMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(list)) {
                    for (Attached attached : list) {
                        fileHandler.deleteFile(attached.getFileName());
                        log.info(String.format("################# 删除obs附件成功,文件名：%s #################", fileName));
                        attachedMapper.deleteByPrimaryKey(attached.getSid());
                        log.info(String.format("################# 删除%s提单报关单随附单据数据成功,提单内部编号：%s #################", ieMarkName, emsListNo));
                    }
                }
                log.info(String.format("################# 调用【报关小精灵报关单打印接口】结束,接口返回：%s #################", response));
                boolean bool = result.getCode() == 200 && result.getData() != null && result.getData().getResult() != null && StringUtils.isNotBlank(result.getData().getResult().get_message());
                if (bool) {
                    //3.调用【报文中心下载接口】下载pdf文件
                    String fileUrl = result.getData().getResult().get_message();
                    String downloadUrl = downloadConfig.getBaseUrl() + downloadConfig.getServiceUrl() + "/" + fileUrl;
                    String tempFileName = UUID.randomUUID().toString() + ".pdf";
                    log.info(String.format("################# 开始调用【报文中心下载接口】,url为：%s #################", downloadUrl));
                    String filePath = RequestUtil.downloadFile(downloadUrl, tempPath, tempFileName);
                    log.info(String.format("################# 调用【报文中心下载接口】结束,附件下载成功 #################"));
                    File downloadFile = new File(filePath);
                    //4.上传附件至obs
                    String obsPath = fileHandler.uploadFile(filePath);
                    log.info(String.format("################# 上传附件至obs成功,返回的obs地址：%s #################", obsPath));
                    //5.插入附件表
                    Attached attached = new Attached();
                    attached.setSid(UUID.randomUUID().toString());
                    attached.setTradeCode(userInfo.getCompany());
                    attached.setBusinessSid(s.getSid());
                    attached.setInsertUser(userInfo.getUserNo());
                    attached.setInsertTime(new Date());
                    attached.setBusinessType(ieMark.equals(CommonEnum.I_E_MARK_ENUM.I_MARK.getCode()) ? DecVariable.ATTACHED_IM : DecVariable.ATTACHED_EM);
                    attached.setAcmpType("entryNo");//附件类型为"报关单号"
                    attached.setFileName(obsPath);
                    attached.setOriginFileName(fileName);
                    attached.setFileSize(new BigDecimal(downloadFile.length()).divide(BigDecimal.valueOf(1024), 5, BigDecimal.ROUND_HALF_UP));//文件大小，转成KB
                    attached.setDataSource(ConstantsStatus.STATUS_1);//此处设置为自动模式
                    attachedMapper.insert(attached);
                    log.info(String.format("################# 新增%s提单报关单随附单据数据成功,提单内部编号：%s #################", ieMarkName, emsListNo));
                    downloadFile.delete();//删除临时文件
                    //6.更新同步报关单附件状态
                    updateSyncEntryStatus(s.getEntryNo(), ConstantsStatus.STATUS_1, null, ieMark, userInfo);
                    log.info("################# 同步报关单附件成功 #################");
                    successCount++;
                } else {
                    //调用接口返回错误处理
                    String msg;
                    if (result != null && StringUtils.isNotBlank(result.getMessage())) {
                        msg = result.getMessage().replace(xdoi18n.XdoI18nUtil.t("报关单"), "");
                        msg = String.format(xdoi18n.XdoI18nUtil.t("报关单号【%s】%s;"), s.getEntryNo(), msg);
                    } else {
                        msg = String.format(xdoi18n.XdoI18nUtil.t("报关单号【%s】数据不存在;"), s.getEntryNo());
                    }
                    updateSyncEntryStatus(s.getEntryNo(), ConstantsStatus.STATUS_0, msg, ieMark, userInfo);
                    message += msg;
                    log.info(String.format("################# 同步报关单附件失败,失败原因：%s #################", msg));
                    errorCount++;
                }
            } catch (Exception ex) {
                //异常处理
                String errorMsg = String.format(xdoi18n.XdoI18nUtil.t("报关单号【%s】数据同步失败;"), s.getEntryNo());
                log.error(errorMsg, ex);
                String msg = ex.getMessage();
                if (StringUtils.isBlank(msg)) {
                    msg = xdoi18n.XdoI18nUtil.t("报关单号【%s】数据同步失败,未知异常;");
                } else {
                    msg.substring(0, msg.length() > 1000 ? 1000 : msg.length());
                    msg = xdoi18n.XdoI18nUtil.t("报关单号【%s】数据同步失败,") + msg + ";";
                }
                message += msg;
                updateSyncEntryStatus(s.getEntryNo(), ConstantsStatus.STATUS_0, msg, ieMark, userInfo);
                errorCount++;
            }
        }
        if (successCount < totalCount) {//部分失败
            resultObject.setSuccess(false);
            resultObject.setCode(400);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("存在同步失败数据"));
            resultObject.setData(StringUtils.isBlank(message) ? xdoi18n.XdoI18nUtil.t("未知原因") : message);
        }
        if (errorCount == totalCount) {//全部失败
            resultObject.setSuccess(false);
            resultObject.setCode(400);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("同步失败"));
            resultObject.setData(StringUtils.isBlank(message) ? xdoi18n.XdoI18nUtil.t("未知原因") : message);
        }
        log.info(String.format("################# 同步报文中心(单一窗口)%s报关单附件结束 #################", ieMarkName));
        return resultObject;
    }

    /**
     * 校验是否存在已同步数据
     */
    private int selectSyncEntryStatusCount(List<ErpDecDocSyncEntryParam> params,String ieMark,String tradeCode) {
        List<String> entryNoList = new ArrayList<>(params.size());
        params.stream().forEach(s -> entryNoList.add(s.getEntryNo()));
        int count;
        if(ieMark.equals(CommonEnum.I_E_MARK_ENUM.I_MARK.getCode())) {
            count = decIEntryHeadMapper.selectSyncEntryStatusCount(entryNoList,tradeCode);
        }else {
            count = decEEntryHeadMapper.selectSyncEntryStatusCount(entryNoList,tradeCode);
        }
        return count;
    }

    /**
     * 更新同步报关单附件状态信息
     * @param entryNo
     * @param ieMark
     * @param userInfo
     */
    public void updateSyncEntryStatus(String entryNo,String status,String syncEntryMessage,String ieMark,UserInfoToken userInfo) {
        Map<String,Object> map = new HashMap<>(5);
        map.put("entryNo",entryNo);
        map.put("tradeCode",userInfo.getCompany());
        map.put("syncEntryMessage",syncEntryMessage);
        map.put("syncEntryStatus",status);
        map.put("updateUser",userInfo.getUserNo());
        map.put("updateUserName",userInfo.getUserName());
        map.put("updateTime",new Date());
        if(ieMark.equals(CommonEnum.I_E_MARK_ENUM.I_MARK.getCode())) {
            decIEntryHeadMapper.updateSyncStatusByEntryNo(map);
        }else {
            decEEntryHeadMapper.updateSyncStatusByEntryNo(map);
        }
    }

    public List<String> checkDownLoadAll(ErpDecDocParam erpDecDocParam, String ieMark, String tradeCode) {
        if (CollectionUtils.isEmpty(erpDecDocParam.getEmsListNos())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请勾选需要下载的数据"));
        }
        if (CollectionUtils.isEmpty(erpDecDocParam.getAcmpTypeList())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请至少勾选一种随附单据类型进行下载"));
        }
        List<String> acmpTypeList = erpDecDocParam.getAcmpTypeList();
        if (acmpTypeList.contains("invoiceNo")) {
            acmpTypeList.add("boxNo");
        }
        Set<String> headIds = null;
        if(CommonVariable.IE_MARK_I.equals(ieMark)) {
            Example example = new Example(DecErpIHeadN.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("emsListNo", erpDecDocParam.getEmsListNos());
            criteria.andEqualTo("tradeCode", tradeCode);

            List<DecErpIHeadN> list = decErpIHeadNMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(list)) {
                headIds = list.stream().map(DecErpIHeadN::getSid).collect(Collectors.toSet());
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提单不存在"));
            }
        } else if(CommonVariable.IE_MARK_E.equals(ieMark)) {
            Example example = new Example(DecErpEHeadN.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("emsListNo", erpDecDocParam.getEmsListNos());
            criteria.andEqualTo("tradeCode", tradeCode);

            List<DecErpEHeadN> list = decErpEHeadNMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(list)) {
                headIds = list.stream().map(DecErpEHeadN::getSid).collect(Collectors.toSet());
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提单不存在"));
            }
        }

        List<Attached> result = mapper.getDocAttacheds(tradeCode, new ArrayList<>(headIds), acmpTypeList);
        if (CollectionUtils.isEmpty(result)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("所选预录入单无附件，请重新选择"));
        }
        BigDecimal sumFileSize = result.stream().map(Attached::getFileSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (sumFileSize.compareTo(BigDecimal.valueOf(204800)) == 1) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("单次附件下载量不允许超过200M，请重新选择后再下载"));
        }
//        if (result.size() != result.stream().map(Attached::getOriginFileName).collect(Collectors.toSet()).size()) {
//            throw new ErrorException(400, "所需下载的附件中存在相同文件名称，无法下载");
//        }
        return  new ArrayList<String>(headIds);
    }
}
