package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description: TODO 表体数据
 * @date： 2021/5/25
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class CutList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 商品序号
     */
    private String GNo;
    /**
     * 商品编码
     */
    private String CodeT;
    /**
     * 附加税号
     */
    private String CodeS;
    /**
     * 商品名称
     */
    private String GName;
    /**
     * 商品规格型号
     */
    private String GModel;
    /**
     * 申报数量
     */
    private String GQty;
    /**
     * 申报计量单位
     */
    private String GUnit;
    /**
     * 法定数量
     */
    private String Qty1;
    /**
     * 法定计量单位
     */
    private String Unit1;
    /**
     * 第二数量
     */
    private String Qty2;
    /**
     * 第二计量单位
     */
    private String Unit2;
    /**
     * 成交总价
     */
    private String TradeTotal;
    /**
     * 币制
     */
    private String TradeCurr;
    /**
     * 原产地（地区）
     */
    private String OriginCountry;
    /**
     * 备注
     */
    private String Note;
}
