package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description: TODO 请求报文接口响应回执MessageData内容
 * @date： 2021/5/25
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class Messagedata implements Serializable {
    private static final long serialVersionUID = 1L;
    private String Code;
    private String Name;
}
