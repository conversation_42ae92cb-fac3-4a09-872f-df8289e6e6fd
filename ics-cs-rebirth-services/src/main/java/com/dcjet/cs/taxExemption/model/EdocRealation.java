package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 减免税回执数据
 * @date： 2021/5/25
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class EdocRealation implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 对应文件路径
     */
    private String FdfsId;
    /**
     * 文件名、随附单据编号
     */
    private String EdocID;
    /**
     * 随附单证类别
     */
    private String EdocCode;
    /**
     * 随附单据格式类型
     */
    private String EdocFomatType;
    /**
     * 操作说明（重传原因）
     */
    private String OpNote;
    /**
     * 随附单据文件企业名
     */
    private String EdocCopId;

    /**
     * 所属单位海关编号
     */
    private String EdocOwnerCode;
    /**
     * 签名单位代码
     */
    private String SignUnit;
    /**
     * 签名时间
     */
    private String SignTime;
    /**
     * 所属单位名称
     */
    private String EdocOwnerName;
    /**
     * 随附单据文件大小
     */
    private String EdocSize;
}
