package com.dcjet.cs.taxExemption.service;

import com.alibaba.fastjson.JSONObject;
import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.taxExemption.dao.SendMsgMapper;
import com.dcjet.cs.taxExemption.model.*;
import com.dcjet.cs.util.FileUtil;
import com.dcjet.cs.util.RequestUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description: 减免税报文上传
 **/
@Service
public class SendMsgService {
    private static Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private SendMsgMapper sendExemptionMapper;
    @Resource
    private GwstdHttpConfigMapper httpConfigMapper;
    @Resource
    private FastdFsService fastdFsService;
    /**
     * 模板文件临时存储路径
     */
    @Value("${dc.export.temp: }")
    private String tempPath;

    /**
     * 减免税发送数据查询
     */
    public CutHead getSendDate(String headSid) {
        return sendExemptionMapper.getSendDate(headSid);
    }

    public List<CutList> getSendList(String headSid) {
        return sendExemptionMapper.getSendList(headSid);
    }
    public List<EdocRealation> getSendDocList(String headSid,String company,String companyName) {
        return sendExemptionMapper.getSendDocList(headSid,company,companyName);
    }

    public String getSendCount() {
        return sendExemptionMapper.getSendCount();
    }

    /**
     * 减免税状态更新
     * status:0 暂存，4 报文发送
     */
    public void updateStatusDate(String headSid, String status) throws Exception {
        sendExemptionMapper.updateStatusDate(status, headSid);
    }

    /**
     * 减免税状态查询
     */
    public Boolean getStatus(String headSid) {
        String STATUS = sendExemptionMapper.getStatus(headSid);
        if (!StringUtils.isEmpty(STATUS) && STATUS.equals("4")) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 减免税状态、审核状态
     */
    public Map<String,Object> getApprStatus(String headSid) {
        Map<String,Object> status = sendExemptionMapper.getApprStatus(headSid);
        return status;
    }

    /**
     * 减免税重新发送
     */
    public ResultObject againSendMsg(String headSid) {
        ResultObject res = ResultObject.createInstance(true);
        try {
            String STATUS = sendExemptionMapper.getStatus(headSid);
            if (!StringUtils.isEmpty(STATUS) && STATUS.equals("4")) {
                sendExemptionMapper.resetData(headSid);
            }
        } catch (Exception e) {
            res.setSuccess(false);
            res.setMessage(xdoi18n.XdoI18nUtil.t("减免税重新发送失败：内部编号重置异常"));
        }
        return res;
    }

    /**
     * 减免税请求报文上传
     */
    public ResultObject sendApi(String tradeCode, String messageId, String fdfsId, String copemsNo, String entryNo, UserInfoToken userInfo) {
        ResultObject res = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送成功"));
        try {
            GwstdHttpConfig gwstdHttpConfig = httpConfigMapper.selectByType("REDL_MSG_SEND");
            if (gwstdHttpConfig == null) {
                throw new ErrorException(400, String.format("未配置减免税报文发送接口,接口类型：[%s].","REDL_MSG_SEND"));
            }
            String postUrl = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
            //获取token
            String token = userInfo.getAccessToken();
            logger.info(String.format("---------------------企业:%s,##减免税报文发送token：%s",tradeCode,token));
            //发送报文
            PostParam para = new PostParam();
            para.setMessageID(messageId);
            para.setMessageSubType("REDLCUT");
            para.setFdfsId(fdfsId);
            para.setCopEmsNo(copemsNo);
            para.setSendUser(userInfo.getUserNo());
            para.setDeclType("0");
            para.setEmsNo("");
            para.setSeqNo("");
            para.setSenderId("");
            para.setEntryNo(entryNo);
            para.setEntryId(entryNo);
            para.setBizField(new BizField(){{setTradeCode(userInfo.getCompany());}});
            ObjectMapper mapper = new ObjectMapper();
            // 解决No serializer found for class java.lang.Object异常
            mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            // null值转 ""
            mapper.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() {
                @Override
                public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                    jsonGenerator.writeString("");//空串
                }
            });
            String paraStr = mapper.writeValueAsString(para);
            logger.info("---------------------减免税报文发送param：{}", paraStr);
            String json = RequestUtil.sendPost(postUrl, token, paraStr);
            logger.info("---------------------减免税报文发送返回：{}", json);
            MessageObject postResult = JSONObject.parseObject(json, MessageObject.class);
            if (postResult.getMessageData().getCode().equals("3")) {
                res.setMessage(xdoi18n.XdoI18nUtil.t("成功"));
                res.setSuccess(true);
            } else {
                res.setMessage(postResult.getMessageData().getName());
                res.setSuccess(false);
            }
        } catch (Exception e) {
            logger.error("---------------------减免税报文发送出错,错误信息为:", e);
            res.setMessage(xdoi18n.XdoI18nUtil.t("减免税报文发送失败"));
            res.setSuccess(false);
        } finally {
            return res;
        }
    }

    /**/
    @SneakyThrows
    public String getFileLocalPath(EdocRealation doc) {
        String fileName = tempPath + UUID.randomUUID().toString();
        if(StringUtils.isNotEmpty(doc.getFdfsId())) {
        //获取扩展名
            String exdName = FileUtil.getExtention(doc.getEdocCopId());
            if (StringUtils.isNotEmpty(exdName)) {
                fileName += "." + exdName;
            }
            // 下载附件
            fastdFsService.downloadFileToLocal(doc.getFdfsId(),fileName);
        }
        logger.info("----减免税附件下载本地临时目录param：{}", fileName);
        return fileName;
    }

}
