package com.dcjet.cs.taxExemption.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description: 获取TOKEN返回内容
 */
@Setter
@Getter
public class TokenResult implements Serializable {
    private static final long serialVersionUID = 1L;
    private boolean success;
    private int code;
    private String message;
    private UserInfo data;
    private String errorField;
    private int pageIndex;
    private int total;
}
