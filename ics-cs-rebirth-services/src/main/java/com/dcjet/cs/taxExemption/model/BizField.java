package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description:
 * @author: WJ
 * @createDate: 2022/7/14 20:04
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class BizField implements Serializable {
    private static final long serialVersionUID = 1L;

    private String TradeCode;
}
