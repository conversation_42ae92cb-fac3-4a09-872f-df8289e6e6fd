package com.dcjet.cs.taxExemption.dao;

import com.dcjet.cs.taxExemption.model.DevFreeApplyRet;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

/**
 * generated by Generate 神码
 * DevFreeApplyReturnInfo
 *
 * <AUTHOR>
 * @date: 2021-5-26
 */
public interface DevFreeApplyRetMapper extends Mapper<DevFreeApplyRet> {
    void updateHead(@Param("clientSeqNo") String clientSeqNo, @Param("projectId") String projectId,
                    @Param("prjZNo") String prjZNo, @Param("etpsPreentNo") String etpsPreentNo, @Param("RECEIPT_STATUS") String RECEIPT_STATUS, @Param("tradeCode") String tradeCode);
}
