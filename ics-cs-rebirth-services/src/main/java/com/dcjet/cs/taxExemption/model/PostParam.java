package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description: 请求报文参数
 * @date： 2021/5/25
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PostParam implements Serializable {
    private static final long serialVersionUID = 1L;
    private String MessageID;
    private String MessageSubType;
    private String FdfsId;
    private String CopEmsNo;
    private String SendUser;
    private String DeclType;
    private String SenderId;
    private String SeqNo;
    private String EmsNo;
    private String entryNo;
    private String entryId;
    private BizField BizField;
}
