package com.dcjet.cs.taxExemption.dao;

import com.dcjet.cs.taxExemption.model.CutHead;
import com.dcjet.cs.taxExemption.model.CutList;
import com.dcjet.cs.taxExemption.model.EdocRealation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface SendMsgMapper {

    CutHead getSendDate(@Param("headSid") String headSid);

    List<CutList> getSendList(@Param("headSid") String headSid);

    List<EdocRealation> getSendDocList(@Param("headSid") String headSid,@Param("companyCode") String companyCode,@Param("companyName") String companyName);

    String getSendCount();

    @Update("update T_DEV_FREE_APPLY_HEAD set STATUS=#{status} where SID=#{headSid}")
    void updateStatusDate(@Param("status") String status, @Param("headSid") String headSid) throws Exception;

    @Select("select STATUS from T_DEV_FREE_APPLY_HEAD where SID=#{headSid}")
    String getStatus(@Param("headSid") String headSid);

    @Select("select STATUS,APPR_STATUS from T_DEV_FREE_APPLY_HEAD where SID=#{headSid}")
    Map<String,Object> getApprStatus(@Param("headSid") String headSid);

    void resetData(@Param("headSid") String headSid);
}