package com.dcjet.cs.taxExemption.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description: 获取TOKEN返回data内容
 */
@Setter
@Getter
public class UserInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    private String userNo;
    private String userName;
    private String company;
    private String companyName;
    private String accessToken;
    private String appNo;
    private Object extData;
    private Object menu;
    private String cdzToken;
}
