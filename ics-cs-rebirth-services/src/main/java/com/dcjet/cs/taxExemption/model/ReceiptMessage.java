package com.dcjet.cs.taxExemption.model;

import com.dcjet.cs.mqsync.model.BasicKafkaModel;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description: 减免税回执
 * @date： 2021/5/25
 */
@Setter
@Getter
public class ReceiptMessage extends BasicKafkaModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 减免税回执数据
     */
    @JsonProperty("MessageData")
    private ReceiptData MessageData;
    /**
     * 报文编号
     */
    @JsonProperty("MessageID")
    private String MessageID;
    /**
     * 报文类型
     */
    @JsonProperty("MessageSubType")
    private String MessageSubType;

    @JsonProperty("BizField")
    private String BizField;

    @Override
    public void setData() {
        if(MessageData != null) {
            this.setBusinessId(MessageData.getEtpsPreentNo());
        }
        this.setMsgType(CommonEnum.KAFKA_MSG_ENUM.EXEMPTION_RET.getCode());//指定为减免税回执类型
        this.setServiceName(CommonEnum.KAFKA_MSG_ENUM.EXEMPTION_RET.getServiceName());//kafka处理的消费类
    }

}
