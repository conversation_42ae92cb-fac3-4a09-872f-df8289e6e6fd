package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:免表数据
 * @date： 2021/5/25
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class SendMsgData implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表头数据
     */
    @JsonProperty("CutHead")
    private CutHead cutHead;
    /**
     * 表体数据
     */
    @JsonProperty("CutList")
    private List<CutList> cutList;
    /**
     * 签名数据
     */
    @JsonProperty("CutSystem")
    private CutSystem cutSystem;
    /**
     * 随附单据数据
     */
    @JsonProperty("EdocRealation")
    private List<EdocRealation> edocRealationList;

    @JsonProperty("BizField")
    private BizField BizField;
}
