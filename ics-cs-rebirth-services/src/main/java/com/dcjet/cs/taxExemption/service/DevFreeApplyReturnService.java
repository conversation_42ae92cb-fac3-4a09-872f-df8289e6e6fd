package com.dcjet.cs.taxExemption.service;

import com.dcjet.cs.mqsync.core.service.BaseMqSyncService;
import com.dcjet.cs.taxExemption.dao.DevFreeApplyRetMapper;
import com.dcjet.cs.taxExemption.model.DevFreeApplyRet;
import com.dcjet.cs.taxExemption.model.ReceiptData;
import com.dcjet.cs.taxExemption.model.ReceiptMessage;
import com.xdo.common.json.JsonObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 减免税回执Service
 */
@Service
@Slf4j
public class DevFreeApplyReturnService extends BaseMqSyncService<ReceiptMessage> {
    @Resource
    private DevFreeApplyRetMapper devFreeApplyReturnInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consume(String data) {
        ReceiptData receiptData = null;
        try {
            ReceiptMessage message = JsonObjectMapper.getInstance().fromJson(data, ReceiptMessage.class);
            receiptData = message.getMessageData();
            saveData(message);
        } catch (Exception ex) {
            assert receiptData != null;
            log.error(String.format("KAFKA接口获取减免税回执更新失败,单据号：【%s】", receiptData.getClientSeqNo()), ex);
            throw new RuntimeException(ex);
        }
    }

    /**
     * @Description: 减免税回执数据持久化与反填减免税表头
     * @date：2021/05/30
     * @params:
     * @return:
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveData(ReceiptMessage message) throws Exception {
        try {
            ReceiptData receiptData = message.getMessageData();
            String tradeCode = message.getTradeCode();

            DevFreeApplyRet entity = new DevFreeApplyRet();
            entity.setSid(UUID.randomUUID().toString());
            // 数据中心统一编号
            String etpsPreentNo = receiptData.getEtpsPreentNo();
            entity.setEtpsPreentNo(etpsPreentNo);
            // 回执状态
            String manageResult = receiptData.getManageResult();
            entity.setManageResult(manageResult);
            // 项目统一编号
            String projectId = receiptData.getProjectId();
            entity.setProjectId(projectId);
            // 征免税客户端统一编号
            String clientSeqNo = receiptData.getClientSeqNo();
            entity.setClientSeqNo(clientSeqNo);
            // 征免税证明海关编号
            String prjZNo = receiptData.getPrjZNo();
            entity.setPrjZNo(prjZNo);

            entity.setMsgType(receiptData.getMsgType());
            entity.setMessageSubType(message.getMessageSubType());
            entity.setMessageId(message.getMessageID());
            entity.setNoteTime(receiptData.getNoteTime());
            List<String> note = receiptData.getCheckInfo().getNote();
            if (CollectionUtils.isNotEmpty(note)) {
                entity.setCheckinfo(note.get(0).substring(0, Math.min(note.get(0).length(), 100)));
            }
            entity.setInsertTime(new Date());
            log.info("开始插入数据！中心统一编号："+etpsPreentNo);
            devFreeApplyReturnInfoMapper.insert(entity);
            log.info("更新免表回执状态！中心统一编号为："+etpsPreentNo);
            if (!StringUtils.equals(manageResult,"V")) {
                devFreeApplyReturnInfoMapper.updateHead(clientSeqNo, projectId, prjZNo, etpsPreentNo, manageResult, tradeCode);
            }
            log.info("更新结束！中心统一编号为："+etpsPreentNo);
        } catch (Exception e) {
            log.error("---------------------减免税回执数据持久化异常：", e);
            throw new Exception(xdoi18n.XdoI18nUtil.t("减免税回执数据持久化异常!"));
        }
    }
}
