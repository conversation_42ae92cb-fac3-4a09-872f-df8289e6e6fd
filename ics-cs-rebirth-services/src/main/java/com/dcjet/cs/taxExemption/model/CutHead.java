package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Transient;
import java.io.Serializable;

/**
 * @Description: TODO 表头数据
 * @date： 2021/5/25
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class CutHead implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 暂存单编号
     */
    private String TmpNo;
    /**
     * 中心统一编号
     */
    private String SeqNo;
    /**
     * 征免税证明编号
     */
    private String CutId;
    /**
     * 免表类型
     */
    private String RedType;
    /**
     * 项目统一编号
     */
    private String ProjectId;
    /**
     * 项目性质
     */
    private String ProjectFund;
    /**
     * 征免性质/代码
     */
    private String CutMode;
    /**
     * 产业政策审批条目
     */
    private String ProjectType;
    /**
     * 关注册编号
     */
    private String OwnerCode;
    /**
     * 企业名称
     */
    private String OwnerName;
    /**
     * 经营单位代码
     */
    private String TradeCode;
    /**
     * 经营单位名称
     */
    private String TradeName;
    /**
     * 同协议号
     */
    private String ContrNo;
    /**
     * 许可证编号
     */
    private String LicenseNo;
    /**
     * 审批部门/代码
     */
    private String ApprDept;
    /**
     * 货物是否已向海关申报进口
     */
    private String DeclGoodsFlag;
    /**
     * 主管海关
     */
    private String MasterCustoms;
    /** 报关单申报地海关 */
    private String DecMasterCustoms;
    /**
     * 审批依据
     */
    private String ApprReason;
    /**
     * 进（出）口岸
     */
    private String IEPort;
    /**
     * 进（出）口标志
     */
    private String IEFlag;
    /**
     * 有效日期
     */
    private String LimitDate;
    /**
     * 成交方式
     */
    private String TransMode;
    /**
     * 运费币制
     */
    private String FeeCurr;
    /**
     * 运费标记
     */
    private String FeeMark;
    /**
     * 运费/率
     */
    private String FeeRate;
    /**
     * 保费币制
     */
    private String InsurCurr;
    /**
     * 保费标记
     */
    private String InsurMark;
    /**
     * 保费/率
     */
    private String InsurRate;
    /**
     * 杂费币制
     */
    private String OtherCurr;
    /**
     * 杂费标记
     */
    private String OtherMark;
    /**
     * 杂费/率
     */
    private String OtherRate;
    /**
     * 备注
     */
    private String Note;
    /**
     * 申请形式
     */
    private String ApplyType;
    /**
     * 申请人统一社会信用代码
     */
    private String ApplyCo_Scc;
    /**
     * 收发货人统一社会信用代码
     */
    private String ComCoScc;
    /**
     * 状况报告书递交标志
     */
    private String HasSpecFile;
    /**
     * 手机
     */
    private String ContactCellphone;
    /**
     * 联系人
     */
    private String Contactor;
    /**
     * 固定电话
     */
    private String ContactPhone;
    /**
     * 终审日期
     */
    private String RechkDate;
    /**
     * 批复日期
     */
    private String ReplyDate;
    /**
     * 受托单位海关注册编码
     */
    private String EntrustedComCode;
    /**
     * 受托单位统一社会信用代码
     */
    private String EntrustedCoScc;
    /**
     * 受托单位名称
     */
    private String EntrustedComName;
    /**
     * 申报单位海关注册编码
     */
    private String DeclComCode;
    /**
     * 修撤申请形式
     */
    private String ModApplyType;
    /**
     * 修改/作废原因
     */
    private String Reason;
    /**
     * 业务类型
     */
    private String BusinessType;
    /**
     * 申报单位名称
     */
    private String DeclComName;
    /**
     * 序列
     */
    @Transient
    @JsonIgnore
    private String sequence;
    /**
     * 免表内部编号
     */
    @Transient
    @JsonIgnore
    private String emsListNo;

    /**
     * 使用地点
     */
    private String UsePlace;

    /**
     * 申请人所在地
     */
    private String BelongAreaName;

    /**
     * 减免税申请人种类
     */
    private String TradeTypeName;
    /**
     * 市场主体类型
     */
    private String MarketSubjectType;
    /**
     * 报关单号
     */
    private String EntryNo;
    private String EntryId;
    /**
     * 是否已申报进口
     */
    private String IsDeclare;

}
