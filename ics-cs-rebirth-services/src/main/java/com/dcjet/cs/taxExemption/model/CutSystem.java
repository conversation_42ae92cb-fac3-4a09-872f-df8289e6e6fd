package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description: TODO 签名数据
 * @date： 2021/5/25
 */
@Setter
@Getter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class CutSystem implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户端统一编号
     */
    private String ClientSeqNo;
    /**
     * 数字签名
     */
    private String Sign;
}
