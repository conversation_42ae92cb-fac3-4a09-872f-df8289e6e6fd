package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


/**
 * @Description: 减免税回执数据
 * @date： 2021/5/25
 */
@Setter
@Getter
public class ReceiptData implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 数据中心统一编号（关联后期所有回执使用）
     */
    @JsonProperty("EtpsPreentNo")
    private String EtpsPreentNo;
    /**
     * 项目统一编号
     */
    @JsonProperty("ProjectId")
    private String ProjectId;
    /**
     * 备注信息
     */
    @JsonProperty("CheckInfo")
    private CheckInfo CheckInfo;
    /**
     * 处理结果
     */
    @JsonProperty("ManageResult")
    private String ManageResult;
    /**
     * 征免税客户端统一编号
     */
    @JsonProperty("ClientSeqNo")
    private String ClientSeqNo;
    /**
     * 回执类型
     */
    @JsonProperty("MsgType")
    private String MsgType;

    /**
     * 征免税证明海关编
     */
    @JsonProperty("PrjZNo")
    private String PrjZNo;
    /**
     * 回执时间
     */
    @JsonProperty("NoteTime")
    private String NoteTime;
}
