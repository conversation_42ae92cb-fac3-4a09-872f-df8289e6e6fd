package com.dcjet.cs.u9.service;

import com.dcjet.cs.u9.dto.U9DataBackResponseDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * U9数据回传服务测试类
 * <AUTHOR>
 * @date 2025-08-12
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class U9DataBackServiceTest {
    
    @Resource
    private U9DataBackService u9DataBackService;
    
    @Test
    public void testSendU9DataBack() {
        // 测试发送U9数据回传
        String tradeCode = "1234567890";
        String reportType = "进口报关";
        String delcareNo = "123456789012345678";
        
        U9DataBackResponseDto response = u9DataBackService.sendU9DataBack(tradeCode, reportType, delcareNo);
        
        System.out.println("响应结果：" + response);
        System.out.println("是否成功：" + response.getIsSuccess());
        System.out.println("响应消息：" + response.getMessage());
    }
}
