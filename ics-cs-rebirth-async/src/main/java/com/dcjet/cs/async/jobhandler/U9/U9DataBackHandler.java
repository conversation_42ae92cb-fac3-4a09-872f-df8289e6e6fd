package com.dcjet.cs.async.jobhandler.U9;

import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.gwstd.service.GwstdJobService;
import com.dcjet.cs.u9.model.InfosList;
import com.dcjet.cs.u9.service.U9DataBackService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.findsword.domain.TokenInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class U9DataBackHandler {
    @Resource
    private InfosList infosList;
    @Resource
    private U9DataBackService u9DataBackService;
    
    @XxlJob("U9DataBackHandler")
    public ReturnT<String> execute(String strSystemCode)  {
        log.info("=================数据反馈接口调度任务开始=================");
        List<TokenInfo> infos = infosList.getInfos();
        for (int i = 0; i < infos.size(); i++) {
            String companyCode = infos.get(i).getCompanyCode();
            u9DataBackService.sendU9DataBack(companyCode);
        }
        log.info("=================数据反馈接口调度任务结束=================");
        return ReturnT.SUCCESS;
    }
}
