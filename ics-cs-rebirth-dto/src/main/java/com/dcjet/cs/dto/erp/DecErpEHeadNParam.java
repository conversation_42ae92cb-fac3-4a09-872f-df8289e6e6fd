package com.dcjet.cs.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@ApiModel(value = "出口表头传入参数")
public class DecErpEHeadNParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 单据内部编号
     */
	@XdoSize(max = 32, message = "{单据内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 企业代码
     */
	@XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 经营企业名称
     */
	@XdoSize(max = 70, message = "{经营企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("经营企业名称")
    private String tradeName;
    /**
     * 经营企业社会信用代码
     */
	@XdoSize(max = 18, message = "{经营企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("经营企业社会信用代码")
    private String tradeCreditCode;
    /**
     * 申报单位编码
     */
	@XdoSize(max = 10, message = "{申报单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位编码")
    private String declareCode;
    /**
     * 申报单位名称
     */
	@XdoSize(max = 70, message = "{申报单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位名称")
    private String declareName;
    /**
     * 申报单位社会信用代码
     */
	@XdoSize(max = 18, message = "{申报单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位社会信用代码")
    private String declareCreditCode;
    /**
     * 录入单位编号
     */
	@XdoSize(max = 10, message = "{录入单位编号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位编号")
    private String inputCode;
    /**
     * 录入单位名称
     */
	@XdoSize(max = 70, message = "{录入单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位名称")
    private String inputName;
    /**
     * 录入单位社会信用代码
     */
	@XdoSize(max = 18, message = "{录入单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位社会信用代码")
    private String inputCreditCode;
    /**
     * 供应商编码
     */
	@XdoSize(max = 20, message = "{供应商编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
	@XdoSize(max = 100, message = "{供应商名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 货代编码
     */
	@XdoSize(max = 50, message = "{货代编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货代编码")
    private String forwardCode;
    /**
     * 货代名称
     */
	@XdoSize(max = 100, message = "{货代名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货代名称")
    private String forwardName;
    /**
     * 出境关别
     */
	@XdoSize(max = 4, message = "{出境关别长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("出境关别")
    private String IEPort;
    /**
     * 申报地海关
     */
	@XdoSize(max = 4, message = "{主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报地海关")
    private String masterCustoms;
    /**
     * 监管方式
     */
	@XdoSize(max = 6, message = "{监管方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 运输方式
     */
	@XdoSize(max = 6, message = "{运输方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 料件成品标记
     */
	@XdoSize(max = 4, message = "{料件成品标记长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("料件成品标记")
    private String GMark;
    /**
     * 贸易国别
     */
	@XdoSize(max = 6, message = "{贸易国别长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("贸易国别")
    private String tradeNation;
    /**
     * 归并类型0-自动归并 1-人工归并
     */
	@XdoSize(max = 1, message = "{归并类型0-自动归并 1-人工归并长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("归并类型0-自动归并 1-人工归并")
    private String mergeType;
    /**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
    /**
     * 录入日期
     */
    @ApiModelProperty("录入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inputDate;
    /**
     * 录入日期-开始
     */
    @ApiModelProperty("录入日期-开始")
    private String inputDateFrom;
    /**
     * 录入日期-结束
     */
    @ApiModelProperty("录入日期-结束")
    private String inputDateTo;
    /**
     * 成交方式
     */
	@XdoSize(max = 6, message = "{成交方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成交方式")
    private String transMode;
    /**
     * 运抵国
     */
	@XdoSize(max = 3, message = "{运抵国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运抵国")
    private String tradeCountry;
    /**
     * 启运港
     */
	@XdoSize(max = 6, message = "{启运港长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("启运港")
    private String despPort;
    /**
     * 价格说明
     */
	@XdoSize(max = 20, message = "{价格说明长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("价格说明")
    private String promiseItems;
    /**
     * 有效标志 0 有效，1 无效
     */
	@XdoSize(max = 1, message = "{有效标志 0 有效，1 无效长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("有效标志 0 有效，1 无效")
    private String validMark;
    /**
     * 备案号
     */
	@XdoSize(max = 20, message = "{备案号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 主提运单
     */
	@XdoSize(max = 50, message = "{主提运单长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("主提运单")
    private String mawb;
    /**
     * 提运单号
     */
	@XdoSize(max = 50, message = "{提运单长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("提运单")
    private String hawb;
    /**
     * 合同协议号
     */
	@XdoSize(max = 32, message = "{合同协议号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("合同协议号")
    private String contrNo;
    /**
     * 许可证号
     */
	@XdoSize(max = 50, message = "{许可证号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("许可证号")
    private String licenseNo;
    /**
     * 发票号码
     */
	@XdoSize(max = 100, message = "{发票号码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票号码")
    private String invoiceNo;
    /**
     * 件数
     */
	@Digits(integer = 9, fraction = 0, message = "{件数必须为数字,整数位最大9位,小数最大0位!}")
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 体积
     */
	@Digits(integer = 11, fraction = 5, message = "{体积必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 总净重
     */
	@Digits(integer = 13, fraction = 8, message = "{净重必须为数字,整数位最大13位,小数最大8位!}")
    @ApiModelProperty("总净重")
    private BigDecimal netWt;
    /**
     * 指运港
     */
	@XdoSize(max = 40, message = "{指运港长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("指运港")
    private String destPort;
    /**
     * 包装种类
     */
	@XdoSize(max = 50, message = "{包装种类长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("包装种类")
    private String wrapType;
    /**
     * 运输工具名称
     */
	@XdoSize(max = 100, message = "{运输工具名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输工具名称")
    private String trafName;
    /**
     * 航次号
     */
	@XdoSize(max = 100, message = "{航次号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("航次号")
    private String voyageNo;
    /**
     * 杂费币制
     */
	@XdoSize(max = 3, message = "{杂费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("杂费币制")
    private String otherCurr;
    /**
     * 杂费类型
     */
	@XdoSize(max = 1, message = "{杂费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("杂费类型")
    private String otherMark;
    /**
     * 杂费
     */
	@Digits(integer = 11, fraction = 5, message = "{杂费必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("杂费")
    private BigDecimal otherRate;
    /**
     * 运费币制
     */
	@XdoSize(max = 3, message = "{运费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运费币制")
    private String feeCurr;
    /**
     * 运费类型
     */
	@XdoSize(max = 1, message = "{运费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运费类型")
    private String feeMark;
    /**
     * 运费
     */
	@Digits(integer = 11, fraction = 5, message = "{运费必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("运费")
    private BigDecimal feeRate;
    /**
     * 保费币制
     */
	@XdoSize(max = 3, message = "{保费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保费币制")
    private String insurCurr;
    /**
     * 保费类型
     */
	@XdoSize(max = 1, message = "{保费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保费类型")
    private String insurMark;
    /**
     * 保费
     */
	@Digits(integer = 11, fraction = 5, message = "{保费必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("保费")
    private BigDecimal insurRate;
    /**
     * 总毛重
     */
	@Digits(integer = 13, fraction = 5, message = "{总毛重必须为数字,整数位最大13位,小数最大5位!}")
    @ApiModelProperty("总毛重")
    private BigDecimal grossWt;
    /**
     * 出口日期
     */
    @ApiModelProperty("出口日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date iedate;
    /**
     * 出口日期-开始
     */
    @ApiModelProperty("出口日期-开始")
    private String iEDateFrom;
    /**
     * 出口日期-结束
     */
    @ApiModelProperty("出口日期-结束")
    private String iEDateTo;
    /**
     * 境内货源地
     */
	@XdoSize(max = 5, message = "{境内货源地长度不能超过5位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内货源地")
    private String districtCode;
    /**
     * 境内发货人编码
     */
	@XdoSize(max = 20, message = "{境内发货人编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内发货人编码")
    private String receiveCode;
    /**
     * 境内发货人名称
     */
	@XdoSize(max = 255, message = "{境内发货人名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内发货人名称")
    private String receiveName;
    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date voyageDate;
    /**
     * 航班日期-开始
     */
    @ApiModelProperty("航班日期-开始")
    private String voyageDateFrom;
    /**
     * 航班日期-结束
     */
    @ApiModelProperty("航班日期-结束")
    private String voyageDateTo;
    /**
     * 出货日期
     */
    @ApiModelProperty("出货日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shipDate;
    /**
     * 出货日期-开始
     */
    @ApiModelProperty("出货日期-开始")
    private String shipDateFrom;
    /**
     * 出货日期-结束
     */
    @ApiModelProperty("出货日期-结束")
    private String shipDateTo;
    /**
     * 境外发货人编码
     */
	@XdoSize(max = 255, message = "{境外发货人编码长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外发货人编码")
    private String overseasShipper;
    /**
     * 境外发货人名称
     */
	@XdoSize(max = 255, message = "{境外发货人名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外发货人名称")
    private String overseasShipperName;
    /**
     * 征免性质
     */
	@XdoSize(max = 3, message = "{征免性质长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征免性质")
    private String cutMode;
    /**
     * 货物存放地点
     */
	@XdoSize(max = 255, message = "{货物存放地点长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货物存放地点")
    private String warehouse;
    /**
     * 最终目的国
     */
	@XdoSize(max = 3, message = "{最终目的国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 入境口岸/出境口岸
     */
	@XdoSize(max = 6, message = "{入境口岸/出境口岸长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("入境口岸/出境口岸")
    private String entryPort;
    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    private String insertTimeTo;
    /**
     * 发送时间-开始
     */
    @ApiModelProperty("发送时间-开始")
    private String declareDateFrom;
    /**
     * 发送时间-结束
     */
    @ApiModelProperty("发送时间-结束")
    private String declareDateTo;
    /**
     * 审核时间-开始
     */
    @ApiModelProperty("审核时间-开始")
    private String apprDateFrom;
    /**
     * 审核时间-结束
     */
    @ApiModelProperty("审核时间-结束")
    private String apprDateTo;
    /**
     * 内审员
     */
	@XdoSize(max = 50, message = "{内审员长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("内审员")
    private String apprUserParam;
    /**
     * 审核状态
     */
	@XdoSize(max = 3, message = "{审核状态长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审核状态")
    private String apprStatus;
    /**
     * 随附单据是否上传
     */
	@ApiModelProperty("随附单据是否上传 0 无 1 有")
    private String attach;
    /**
     * 保完税标识 0 保税 1 非保税
     */
	@ApiModelProperty("保完税标识 0 保税 1 非保税")
    private String bondMark;
    /**
     * 数据来源 1录入; 2导入; 3数据提取; 4集报提取; 5深加工生成; 6内销生成
     */
    @ApiModelProperty("数据来源 1录入;2导入;3数据提取;4集报提取;5深加工生成;6内销生成")
    private String dataSource;
    /**
     * 报关标志
     */
    @ApiModelProperty("报关标志")
    private String dclcusMark;
    /**
     * 报关类型
     */
    @ApiModelProperty("报关类型")
    private String dclcusType;
    /**
     * 报关单类型
     */
    @ApiModelProperty("报关单类型")
    private String entryType;
    /**
     * 申请表编号/申报表编号
     */
    @ApiModelProperty("申请表编号/申报表编号")
	@XdoSize(max = 50, message = "{申报表编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String applyNo;
    /**
     * 关联核注清单编号
     */
    @ApiModelProperty("关联核注清单编号")
    private String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @ApiModelProperty("关联账册（手册）编号")
	@XdoSize(max = 12, message = "{关联备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
    private String relEmsNo;
    /**
     * 境内目的地(行政区域)
     */
    @ApiModelProperty("境内目的地(行政区域)")
    private String districtPostCode;
    /**
     * shipTo代码
     */
    @ApiModelProperty("shipTo代码")
    private String shipTo;
    /**
     * 申报单位编码
     */
	@XdoSize(max = 50, message = "{申报单位编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位编码(基础信息)")
    private String declareCodeCustoms;
    /**
     * 申报单位名称
     */
	@XdoSize(max = 100, message = "{申报单位名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位名称(基础信息)")
    private String declareNameCustoms;
    /**
     * 清单归并类型
     */
    @ApiModelProperty("清单归类类型")
    private String billType;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUserParam;
    /**
     * 数量汇总
     */
    @ApiModelProperty("数量汇总")
    private BigDecimal sumQty;
    /**
     * 总价汇总
     */
    @ApiModelProperty("总价汇总")
    private BigDecimal sumDecTotal;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 集装箱类型
     */
    @ApiModelProperty("集装箱类型")
	@XdoSize(max = 20, message = "{集装箱类型长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    private String containerType;
    /**
     * 集装箱数量
     */
    @ApiModelProperty("集装箱数量")
    @Digits(integer = 5, fraction = 0, message = "{集装箱数量必须为数字,整数位最大5位!}")
    private String containerNum;
    /**
     * 包装种类2
     */
    @ApiModelProperty("包装种类2")
    @XdoSize(max = 50, message = "{包装种类2长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String wrapType2;
    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
	@XdoSize(max = 50, message = "{贸易条款长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String tradeTerms;
    /**
     * 仓库代码
     */
    @ApiModelProperty("仓库代码")
	@XdoSize(max = 30, message = "{仓库代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    private String warehouseCode;
    /**
     * 订单日期
     */
    @ApiModelProperty("订单日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;
    /**
     * 货物类型 0 集货 1 散货
     */
    @ApiModelProperty("货物类型 0 集货 1 散货")
    private String goodsType;
    /**
     * 柜/罐号
     */
    @ApiModelProperty("柜/罐号")
    @XdoSize(max = 50, message = "{柜/罐号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String tankNo;
    /**
     * 保证金金额
     */
    @ApiModelProperty("保证金金额")
    @Digits(integer = 14, fraction = 5, message = "{保证金金额必须为数字,整数位最大15位,小数最大5位!}")
    private BigDecimal bondPrice;
    /**
     * 缴纳日期
     */
    @ApiModelProperty("缴纳日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentDate;
    /**
     * 清单类型
     */
    @XdoSize(max = 1, message = "{清单类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("清单类型")
    private String billListType;
    /**
     * 内审员
     */
    private String apprUser;
    /**
     * 内部备注
     */
    @ApiModelProperty("内部备注")
    @XdoSize(max = 255, message = "{内部备注长度不能超过255个字符(一个汉字2位字节长度)}")
    private String remark;
    /**
     * 清单申报单位(原报关单申报单位，生成清单时调整回去)
     */
    @XdoSize(max = 10, message = "{清单申报单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("清单申报单位")
    private String agentCode;
    /**
     * 清单申报单位名称(原报关单申报单位，生成清单时调整回去)
     */
    @XdoSize(max = 70, message = "{清单申报单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("清单申报单位名称")
    private String agentName;
    /**
     * 清单申报单位社会统一信用代码(原报关单申报单位，生成清单时调整回去)
     */
    @XdoSize(max = 18, message = "{清单申报单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("清单申报单位社会信用代码")
    private String agentCreditCode;
    /**
     * 清单申报单位编码
     */
    @XdoSize(max = 50, message = "{清单申报单位编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("清单申报单位编码(基础信息)")
    private String agentCodeCustoms;
    /**
     * 清单申报单位名称
     */
    @XdoSize(max = 100, message = "{清单申报单位名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("清单申报单位名称(基础信息)")
    private String agentNameCustoms;
    /**
     * 计划出口日期(邀请日期)
     */
    @ApiModelProperty("计划出口日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inviteDate;
    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    @Digits(integer = 5, fraction = 2, message = "{计费重量必须为数字,整数位最大5位,小数最大2位!}")
    private BigDecimal cweight;
    /**
     * 生成销售单位编码
     */
    @XdoSize(max = 10, message = "{生成销售单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("生成销售单位编码")
    private String ownerCode;
    /**
     * 生成销售单位名称
     */
    @XdoSize(max = 70, message = "{生成销售单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("生成销售单位名称")
    private String ownerName;
    /**
     * 生成销售单位社会信用代码
     */
    @XdoSize(max = 18, message = "{生成销售单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("生成销售单位社会信用代码")
    private String ownerCreditCode;
    /**
     * 境外发货人AEO编码
     */
    @ApiModelProperty("境外发货人AEO编码")
    @XdoSize(max = 50, message = "{境外发货人AEO编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String overseasShipperAeo;
    /**
     * 付款方式
     */
    @ApiModelProperty("付款方式")
    @XdoSize(max = 50, message = "{付款方式长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String paymentMode;
    /**
     * 贸易条款(地区)
     */
    @ApiModelProperty("贸易条款(地区)")
    @XdoSize(max = 50, message = "{贸易条款(地区)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String tradeArea;
    /**
     * 随附单据锁定状态 0 未锁定 1 锁定
     */
    @ApiModelProperty("随附单据锁定状态")
    @XdoSize(max = 1, message = "{随附单据锁定状态长度不能超过1位字节长度!}")
    private String attachLock;
    /**
     * 制单员
     */
    @ApiModelProperty("制单员")
    private String userName;
    /**
     * 单据类型 0大提单 1 小提单
     */
    @ApiModelProperty("单据类型")
    private String decType;
    /**
     * 预报单编号
     */
    @XdoSize(max = 50, message = "{预报单编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("预报单编号")
    private String preEmsListNo;
    /**
     * 提取状态（0 未提取 1 已提取）
     */
    @ApiModelProperty("提取状态（0 未提取 1 已提取）")
    private String status;
    /**
     * 接单状态
     */
    @ApiModelProperty("接单状态")
    private String preStatus;
    /**
     * SA
     */
    @XdoSize(max = 50, message = "{SA长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("SA")
    private String sa;
    /**
     * 接单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("接单时间")
    private Date preDate;
    /**
     * 接单时间-开始
     */
    @ApiModelProperty("接单时间-开始")
    private String preDateFrom;
    /**
     * 接单时间-结束
     */
    @ApiModelProperty("接单时间-结束")
    private String preDateTo;
    /**
     * 系统加工费
     */
    @ApiModelProperty("系统加工费")
    @Digits(integer = 10, fraction = 2, message = "{系统加工费必须为数字,整数位最大10位,小数最大2位!}")
    private BigDecimal decTotalProcess;
    /**
     * 内包装
     */
    @XdoSize(max = 50, message = "{内包装长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("内包装")
    private String nootherPack;
    /**
     * 客供总价
     */
    @ApiModelProperty("客供总价")
    @Digits(integer = 10, fraction = 2, message = "{客供总价必须为数字,整数位最大10位,小数最大2位!}")
    private BigDecimal clientTotal;
    /**
     * 实收总价
     */
    @ApiModelProperty("实收总价")
    @Digits(integer = 10, fraction = 2, message = "{实收总价必须为数字,整数位最大10位,小数最大2位!}")
    private BigDecimal payTotal;
    /**
     * 系统加工费币制
     */
    @ApiModelProperty("系统加工费币制")
    @XdoSize(max = 10, message = "{系统加工费币制长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String decTotalProcessCurr;
    /**
     * 客供总价币制
     */
    @ApiModelProperty("客供总价币制")
    @XdoSize(max = 10, message = "{客供总价币制长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String clientTotalCurr;
    /**
     * 实收总价币制
     */
    @ApiModelProperty("实收总价币制")
    @XdoSize(max = 10, message = "{实收总价币制长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String payTotalCurr;
	/**
	 * 封装
	 */
	@XdoSize(max = 1, message = "{封装长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("封装")
    private String packages;
    /**
     * billTo代码
     */
    @ApiModelProperty("billTo代码")
    @XdoSize(max = 50, message = "{封装长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String billTo;
    /**
     * notify代码
     */
    @ApiModelProperty("notify代码")
    @XdoSize(max = 50, message = "{封装长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String notify;
    /**
     * 关联报关单收货企业社会统一信用代码
     */
    @XdoSize(max = 18, message = "{关联报关单收货企业社会统一信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联报关单收货企业社会统一信用代码")
    private String relEntryReceiveCreditCode;
    /**
     * 关联报关单海关收发货单位编码
     */
    @XdoSize(max = 10, message = "{关联报关单海关收发货单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联报关单海关收发货单位编码")
    private String relEntryReceiveCode;
    /**
     * 关联报关单收发货单位名称
     */
    @XdoSize(max = 70, message = "{关联报关单收发货单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联报关单收发货单位名称")
    private String relEntryReceiveName;
    /**
     * 报关/转关关系标志
     */
    @XdoSize(max = 1, message = "{报关/转关关系标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关/转关关系标志")
    private String declTrnrel;
    /**
     * 料费
     */
    @ApiModelProperty("料费")
    @Digits(integer = 10, fraction = 2, message = "{料费必须为数字,整数位最大10位,小数最大2位!}")
    private BigDecimal matTotal;
    /**
     * 报关单通关类型
     */
    @ApiModelProperty("报关单通关类型")
    private String entryClearanceType;
    /**
     * 发票日期
     */
    @ApiModelProperty("发票日期")
    private Date invoiceDate;
    /**
     * 报价单编号
     */
    @ApiModelProperty("报价单编号")
    private String quoNo;
    /**
     * 是否疫情附加费
     */
    @ApiModelProperty("是否疫情附加费")
    private String yqRateFlag;
    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCost;
    /**
     * 第三方申报端口
     */
    @ApiModelProperty("第三方申报端口")
    private String thirdDeclare;
    /**
     * 运单通道
     */
    @ApiModelProperty("运单通道")
    private String expressChannel;
    /**
     * 运单号
     */
    @XdoSize(max = 50, message = "运单号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运单号")
    private String awb;
    /**
     * DHL税款支付方式 1：DHL垫付，2：客户网服，3：DTP，4：预付，5：客户自付，7：机场自取，8：关税月结，9：汇总征税
     */
    @ApiModelProperty("DHL税款支付方式 1：DHL垫付，2：客户网服，3：DTP，4：预付，5：客户自付，7：机场自取，8：关税月结，9：汇总征税")
    private String dhlDutyMode;
    /**
     * 分送集报 0：不发送 1：发送
     */
    @ApiModelProperty("分送集报")
    private String collectedreports;

    /**
     * 保司名称
     */
    @ApiModelProperty("保司名称")
    private String insureCompany;
}
