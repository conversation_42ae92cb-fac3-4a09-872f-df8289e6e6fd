package com.dcjet.cs.dto.erp;

import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate dcits
 *
 * @author: cc
 * @date: 2019-07-08
 */
@Setter
@Getter
@ApiModel(value = "进口提单表体导入验证")
public class DecErpIListImportParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业料号
     */
    @NotBlank(message = "{企业料号不能为空}")
    @XdoSize(max = 100, message = "{企业料号长度不能超过100位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 订单号码
     */
    @XdoSize(max = 100, message = "{订单号码长度不能超过100位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("订单号码")
    private String orderNo;
    /**
     * 征免方式代码
     */
    @XdoSize(max = 6, message = "{征免方式长度不能超过6位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("征免方式")
    private String dutyMode;
    /**
     * 币制代码
     */
    @NotBlank(message = "{币制不能为空}")
    @XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 申报数量
     */
    @NotNull(message = "{申报数量不能为空}")
    @Digits(integer = 11, fraction = 5, message = "{申报数量不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("申报数量")
    private BigDecimal qty;
    /**
     * 申报单价
     */
    @Digits(integer = 10, fraction = 5, message = "{申报单价不能超过总长度15，小数位不能超过5位}")
    @ApiModelProperty("申报单价")
    private BigDecimal decPrice;
    /**
     * 申报总价
     */
    @Digits(integer = 12, fraction = 4, message = "{申报总价不能超过总长度16，小数位不能超过4位}")
    @ApiModelProperty("申报总价")
    private BigDecimal decTotal;
    /**
     * 原产国
     */
    @NotBlank(message = "{原产国不能为空}")
    @XdoSize(max = 3, message = "{原产国长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("原产国")
    private String originCountry;
    /**
     * 法一数量
     */
    @Digits(integer = 11, fraction = 5, message = "{法一数量不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("法一数量")
    private BigDecimal qty1;
    /**
     * 法二数量
     */
    @Digits(integer = 11, fraction = 5, message = "{法二数量不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("法二数量")
    private BigDecimal qty2;
    /**
     * 单耗版本号
     */
    @XdoSize(max = 8, message = "{单耗版本号长度不能超过8位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("单耗版本号")
    private String exgVersion;
    /**
     * 归并序号
     */
    @Digits(integer = 11, fraction = 0, message = "{归并序号不能超过总长度11，不能包含小数位}")
    @ApiModelProperty("归并序号")
    private BigDecimal entryGNo;
    /**
     * 净重
     */
    @Digits(integer = 13, fraction = 8, message = "{净重不能超过总长度21，小数位不能超过8位}")
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 发票号码
     */
    @XdoSize(max = 30, message = "{发票号码长度不能超过30位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("发票号码")
    private String invoiceNo;
    /**
     * 备案号
     */
    @XdoSize(max = 12, message = "{备案号长度不能超过12位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 毛重
     */
    @Digits(integer = 13, fraction = 5, message = "{毛重不能超过总长度18，小数位不能超过5位}")
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * 体积
     */
    @Digits(integer = 11, fraction = 5, message = "{体积不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 境内目的地(国内地区)
     */
    @XdoSize(max = 5, message = "{境内目的地(国内地区)长度不能超过5位字节长度(一个汉字2位字节长度)}")
    private String districtCode;
    /**
     * 境内目的地(行政区域)
     */
    @XdoSize(max = 6, message = "{境内目的地(行政区域)长度不能超过6位字节长度(一个汉字2位字节长度)}")
    private String districtPostCode;
    /**
     * 最终目的国
     */
    @XdoSize(max = 3, message = "{最终目的国长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 境外发货人
     */
    @XdoSize(max = 30, message = "{境外发货人长度不能超过30位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("境外发货人")
    private String supplierCode;
    /**
     * 监管方式
     */
    @XdoSize(max = 6, message = "{监管方式长度不能超过6位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 保完税标记
     */
    @XdoSize(max = 1, message = "{保完税标记长度不能超过1位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("保完税标记")
    private String bondMark;
    /**
     * 物料类型标识
     */
    @XdoSize(max = 1, message = "{物料类型长度不能超过1位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("物料类型")
    private String GMark;
    /**
     * 关联单号
     */
    @XdoSize(max = 50, message = "{关联单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联单号")
    private String linkedNo;
    /**
     * 成本中心
     */
    @XdoSize(max = 50, message = "{成本中心长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成本中心")
    private String costCenter;
    /**
     * 关联单号行号
     */
    @XdoSize(max = 50, message = "{关联单号序号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联单号行号")
    private String lineNo;
    /**
     * 内部备注
     */
    @XdoSize(max = 255, message = "{内部备注长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("内部备注")
    private String note;
    /**
     * REMARK1
     */
    @XdoSize(max = 250, message = "{REMARK1长度不能超过250位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("REMARK1")
    private String note1;
    /**
     * REMARK2
     */
    @XdoSize(max = 250, message = "{REMARK2长度不能超过250位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("REMARK2")
    private String note2;
    /**
     * REMARK3
     */
    @XdoSize(max = 250, message = "{REMARK3长度不能超过250位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("REMARK3")
    private String note3;
    /**
     * 清单归并序号
     */
    @Digits(integer = 11, fraction = 0, message = "{归并序号不能超过总长度11，不能包含小数位}")
    @ApiModelProperty("清单归并序号")
    private BigDecimal billGNo;
    /**
     * 商品编码
     */
    @XdoSize(max = 10, message = "{商品编码不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品编码")
    private String codeTS;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "{商品名称不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "{申报规格型号不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报规格型号")
    private String GModel;
    /**
     * 计量单位
     */
    @XdoSize(max = 3, message = "{计量单位不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("计量单位")
    private String unit;
    /**
     * 入库关联单号
     */
    @XdoSize(max = 100, message = "{入库关联单号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("入库关联单号")
    private String inOutNo;
    /**
     * 征免性质
     */
    @XdoSize(max = 3, message = "{征免性质长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征免性质")
    private String cutMode;
    /**
     * CIQ编码
     */
    @XdoSize(max=3, message="{CIQ代码长度不能超过3个字符(一个汉字2位字节长度)}")
    @ApiModelProperty("CIQ编码")
    @ExcelColumn(name = "CIQ代码")
    private String ciqNo;
    /**
     * 件数
     */
    @Digits(integer = 9, fraction = 0, message = "{件数必须为数字,整数位最大9位,小数最大0位!}")
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 特许权关联单号
     */
    @ApiModelProperty("特许权关联单号")
    @XdoSize(max = 100, message = "{特许权关联单号长度不能超过100个字符(一个汉字2位字节长度)}")
    private String confirmRoyaltiesNo;
    /**
     * 料号申报要素
     */
    @XdoSize(max = 255, message = "{料号申报要素长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("料号申报要素")
    private String copGModel;
    /**
     * 采购人员
     */
    @XdoSize(max = 100, message = "{采购人员长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("采购人员")
    private  String buyer;
    /**
     * 采购订单行号
     */
    @Digits(integer = 10, fraction = 0, message =  "{采购订单行号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("采购订单行号")
    private BigDecimal orderLineNo;
    /**
     * 分送清单编号
     */
    @XdoSize(max=20, message="{分送清单编号长度不能超过20个字符(一个汉字2位字节长度)}")
    @ApiModelProperty("分送清单编号")
    private  String fsListNo;
}

